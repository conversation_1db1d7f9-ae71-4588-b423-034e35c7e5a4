{"__meta": {"id": "Xfc83170cc6a5d6c0da195b83deb6c342", "datetime": "2025-07-31 11:20:02", "utime": **********.976352, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960801.811344, "end": **********.976387, "duration": 1.1650431156158447, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1753960801.811344, "relative_start": 0, "end": **********.806099, "relative_end": **********.806099, "duration": 0.9947550296783447, "duration_str": "995ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.80612, "relative_start": 0.9947760105133057, "end": **********.97639, "relative_end": 2.86102294921875e-06, "duration": 0.17026996612548828, "duration_str": "170ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47346056, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026239999999999996, "accumulated_duration_str": "26.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.899205, "duration": 0.02302, "duration_str": "23.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 87.729}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.942694, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 87.729, "width_percent": 2.706}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.948431, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 90.434, "width_percent": 9.566}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1051135260 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1051135260\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1514669475 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1514669475\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1073784500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1073784500\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-47354217 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRma1JnZTJYOE1VZXNUMVBMUDdqb1E9PSIsInZhbHVlIjoiYUNPR3dxWVVoODBsM2ZlVmtVcXZ0RU1tRVhiTEVmSTJJMDRIZExxaW5XZjVOdVVhWkt5c29OdTFXNjVZWVhya0NxWHl4UkxmWmJpQnFRM3Nkd2JvYXZrUFAwUmcvaVRTRVRMSCtueEZSWVdoazE0cUJxdTR5SG5OSmkrSUdTVk1abmROcUp5elNCNEJwRFNKdlduVUgxaHNiNzdqaXBBTUpwL1RmdXhVQS9FbTIrdXNjYjhoL2haNlV1VDkydEkvbzNuZzBrVHRZY2lQSDhlTUxzcWZTWnFJUExEajZXRjlDWGp3dmRNUFRRRDdxNlRxQXNVNWZRcnIwZy9OM1U5TFZjR0xyem45cVRqdEZHMWZQMlNzSytrQW93elptR0dRR0FVSGYvZmlLWmc5S1ZaWWRkWVVhUkthV1Nzdm9pa2FEMi8wdFNlQnkvcWxMLy9oQWdGZi93VDJkdUNXb0k5WitXOExqTVA1cmdGZWxMTFVMd25COXJuRDhkanFvUVBYdVpFTmVFcDNNTVczOG53dDYyRGplWVZiNldPZThnVTRLejFKVlFIYncyYkNpVkxlUVpUWmFrRVJPb0pBQ3dsYzNRTTE5c3lWRTcwWmFWRkZJVGVqbWh6cWowenpCcUxsUlBIcDV2dmVOc0xXVTUzZXFXQnBWSnZOV2lEditVWkMiLCJtYWMiOiJmZWI4Y2NiMDNmODI0ZTk3MjFmZDYyNDEzNmQxNTYxMmU4MmRkNzMxNGU4MDljYjVhM2FlYWY5YWM0NTEzNjk3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkM4anplSGNHUnlIOElhRjBLSDhtN3c9PSIsInZhbHVlIjoiNjd6Y1k0Q1IwcVIrZ2d6U2VWRFNxTEV3cysxZ2krZVhmU0F3d2tqRTlYTUx2NjZUVTMxNFBGQzh5ZFJ3ZHVqUmU5T0oyVHhYbFhrNkxEdUgyK0U1ZzBBOGY5K21ObGYxVmgwM3pJUDYzYjUvdDdkTk5ORWhJa0haVmJHdnM3dDF0Y2dBWFNXV25JcldJRmdsYkdyWWJITlRybG56OVNrQ0cxNXNvQkFSVlJtdUk2TzdyUHlaQjVqajU1NEplM2tzTlhQdkZBLzBidG9SODY2YlgvVjFESTUyQXVNYmRwZ3dBNnk4WlN2bnYxdjFycTFUc1BtK0JrSG5sQ2RpQWNsVXgyeDJwUU9KTlJiVlVxMXc2eXBjM002SW02aUhlQWJNbU9iNVJFUHQ3RFdDOCtUTEZzSFlianZtamo2Y1RmOFpwVzkvZVZVZXRWTWNjOXV6UHJtNUI0b0lVZnpVdW02QVExYlBsN2xiYkFoT2hGQVQvWUhFZFQvNEdKS2JlenBqeWdIc1oxRVBsSUhIWHh6M1E4T0FrUndZT2RBc2VWVkFLOWpteWwvMVhkUVUrRXF5U0RsbEhGV0pQK2dSZlJRaDEzMDlaT2NST1JXRnJXUkR5b2xTajY5eHVLbU5iSjhTM2ZLbEZDaFhNd1ZGMUxTbEh1d2pLUzFpSzVIcFFFdXUiLCJtYWMiOiI3NGNiOWVmOWRhNTc1ZmIxOWZmZDRhZTVlNmIzN2EwYWUxMmNjNGE4ZjdmZWZmNGUwMjg0ZTY1MjI3ZjRmZGUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47354217\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1723832687 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723832687\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1554408643 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:20:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis3KzZCRW85c3QySkxJdkxocjB1MHc9PSIsInZhbHVlIjoiYVlraStsdTB6Y1FpYk11YnpIR1FqR3IyK0lueHhZdFZ6M2NXdGQzOUdMTlpVSjNtd0xKS3dFSWNhM0ZEaWlvUDA3b0djN1FONSs1WUN6c0l1RnhKVlU4bUFZczJSenhTbm1vaGR1d0tPUTlpS2w0WXBjWUtGMGZJUDN5TlFKTE9KNlI2WWZ5ZGx0SDU3NHQwaytQTnpEbFJ6aENxajZRWG1KdHNLeWJOWE4xQTc0ZHlBVVNSOVE3ZmpkL0VhQzJPOXJBUlhhVWxVR3RWSG1BU3dUcHVOc1V5dFJLWUpuNDIxQjNIQ3FGbWdaaXY3TFhNQzJ3bEgzU3VIQnEvaTJLcExPb1Q3T2lUVy9ZeC9jMDFsYlVBd3ltVytUVC91d2xJNG5kdVdhUnh0bm1TV091Rlo4YnBvNkNPeTkwb2NiVTZCYU0rb1gxNGtQS3Q5dUQ3a2YyWUFRSmN3TW15SFRLdlJidEZvaS94dnE4LzRCZStXN2hIQTVQTHFTcTlEUUI5U2M4Q2E1TmtOa2RtUkl5U2V4aEphR0tXemUvb2JSWUFDN3UyRnkrSlozT0dxSHhBelVnc1V4UTNBbUd5UWZTZksvYzlTUWMwUldkWmtvYkJjdGg2WkE1czA3dFlJNnNoNi9MVENXd0h2R251cXU1eGhOTC9NMEt2S3o1UzBxVHQiLCJtYWMiOiIzODRmM2E3YzgwNDBiM2I1YWVhMjYxZTEwNDc4M2ZjYTE5NWE3M2NiYmUzYmY1OWVjMzk1ZDgyZTg1MDQ5YmY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:20:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlQwcjBkc2E1Wll1dWE4U0Q0OVcvV3c9PSIsInZhbHVlIjoiZnJtZjdpYTBaUGFic0dNUEdIaHNTMUEyRlZ4eWpseEp1ZFJqY3JkTXNDNERSeXJ3S0xsMDhibzRqNkZlMWtqZng0OTk1eXpYTEJjVWpONEFsSm8vcVVyTGxYTnFCc3k1clFxNG04eHFPMWxyM3N2UHhXSzVSK0tRZ2VZQzVIVmxSY28zOGowTWx5enlwdS81dEcyaHVhdDF2ZWR5UTRyMEhjbWtCWFFCL21BT1hrZTVUQ1BhalBaMndicTdqaUQ5b1NtY0RhMWgrRnVxeXlabVcreUtFQ2svTVNLZWEwaTNHd1RpZWlLYTdhVk5VbzJYekhpZUhEaUVNdVVRSWQvUmVwODBUSEd6ajF0Z2cwdTh6RVVLOFBnelNVTnRMZXVzbEJXK0Q2dzd6ZGF4dVhHTEdFUngyRURWaGlEUTJqeUtyVm9xMjhSRERsdndQbm9nY3A5T3FWam94aU1tTDVBMEhNRnBRZDJzZ0p6eis2MGRCUmdtSnlYSm84ZW5BN3pDNDJIZCtyZk9QRlJZTjBxYnNzVm03b1QwWXQ5cEZYQlQ1Rm05Tit5c1lKZHFMZXBVKzRMVkI3YmZId2RpdXVKWjh1WVFLTkRPT0pZYUFGU2FjNytWczNHUExzQnhOSGJvZjdjK0dGdGk2WHVBQjY5c082dEl1cTFZRWg5SStLZGUiLCJtYWMiOiIxNDc2YjNlZjA0NDViMjIyZGE1NTYyYjliNTI2NzE2ZTZiMjJjOGIyZWM3OGYzNjk4OTA3ZWZiNzc0MGVmNzQwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:20:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis3KzZCRW85c3QySkxJdkxocjB1MHc9PSIsInZhbHVlIjoiYVlraStsdTB6Y1FpYk11YnpIR1FqR3IyK0lueHhZdFZ6M2NXdGQzOUdMTlpVSjNtd0xKS3dFSWNhM0ZEaWlvUDA3b0djN1FONSs1WUN6c0l1RnhKVlU4bUFZczJSenhTbm1vaGR1d0tPUTlpS2w0WXBjWUtGMGZJUDN5TlFKTE9KNlI2WWZ5ZGx0SDU3NHQwaytQTnpEbFJ6aENxajZRWG1KdHNLeWJOWE4xQTc0ZHlBVVNSOVE3ZmpkL0VhQzJPOXJBUlhhVWxVR3RWSG1BU3dUcHVOc1V5dFJLWUpuNDIxQjNIQ3FGbWdaaXY3TFhNQzJ3bEgzU3VIQnEvaTJLcExPb1Q3T2lUVy9ZeC9jMDFsYlVBd3ltVytUVC91d2xJNG5kdVdhUnh0bm1TV091Rlo4YnBvNkNPeTkwb2NiVTZCYU0rb1gxNGtQS3Q5dUQ3a2YyWUFRSmN3TW15SFRLdlJidEZvaS94dnE4LzRCZStXN2hIQTVQTHFTcTlEUUI5U2M4Q2E1TmtOa2RtUkl5U2V4aEphR0tXemUvb2JSWUFDN3UyRnkrSlozT0dxSHhBelVnc1V4UTNBbUd5UWZTZksvYzlTUWMwUldkWmtvYkJjdGg2WkE1czA3dFlJNnNoNi9MVENXd0h2R251cXU1eGhOTC9NMEt2S3o1UzBxVHQiLCJtYWMiOiIzODRmM2E3YzgwNDBiM2I1YWVhMjYxZTEwNDc4M2ZjYTE5NWE3M2NiYmUzYmY1OWVjMzk1ZDgyZTg1MDQ5YmY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:20:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlQwcjBkc2E1Wll1dWE4U0Q0OVcvV3c9PSIsInZhbHVlIjoiZnJtZjdpYTBaUGFic0dNUEdIaHNTMUEyRlZ4eWpseEp1ZFJqY3JkTXNDNERSeXJ3S0xsMDhibzRqNkZlMWtqZng0OTk1eXpYTEJjVWpONEFsSm8vcVVyTGxYTnFCc3k1clFxNG04eHFPMWxyM3N2UHhXSzVSK0tRZ2VZQzVIVmxSY28zOGowTWx5enlwdS81dEcyaHVhdDF2ZWR5UTRyMEhjbWtCWFFCL21BT1hrZTVUQ1BhalBaMndicTdqaUQ5b1NtY0RhMWgrRnVxeXlabVcreUtFQ2svTVNLZWEwaTNHd1RpZWlLYTdhVk5VbzJYekhpZUhEaUVNdVVRSWQvUmVwODBUSEd6ajF0Z2cwdTh6RVVLOFBnelNVTnRMZXVzbEJXK0Q2dzd6ZGF4dVhHTEdFUngyRURWaGlEUTJqeUtyVm9xMjhSRERsdndQbm9nY3A5T3FWam94aU1tTDVBMEhNRnBRZDJzZ0p6eis2MGRCUmdtSnlYSm84ZW5BN3pDNDJIZCtyZk9QRlJZTjBxYnNzVm03b1QwWXQ5cEZYQlQ1Rm05Tit5c1lKZHFMZXBVKzRMVkI3YmZId2RpdXVKWjh1WVFLTkRPT0pZYUFGU2FjNytWczNHUExzQnhOSGJvZjdjK0dGdGk2WHVBQjY5c082dEl1cTFZRWg5SStLZGUiLCJtYWMiOiIxNDc2YjNlZjA0NDViMjIyZGE1NTYyYjliNTI2NzE2ZTZiMjJjOGIyZWM3OGYzNjk4OTA3ZWZiNzc0MGVmNzQwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:20:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554408643\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-346033044 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-346033044\", {\"maxDepth\":0})</script>\n"}}