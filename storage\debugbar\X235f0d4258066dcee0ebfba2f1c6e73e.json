{"__meta": {"id": "X235f0d4258066dcee0ebfba2f1c6e73e", "datetime": "2025-07-31 12:39:49", "utime": **********.276828, "method": "GET", "uri": "/invoice/contact-details?contact_id=lead_12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965586.373983, "end": **********.276859, "duration": 2.902876138687134, "duration_str": "2.9s", "measures": [{"label": "Booting", "start": 1753965586.373983, "relative_start": 0, "end": 1753965588.928607, "relative_end": 1753965588.928607, "duration": 2.554624080657959, "duration_str": "2.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753965588.928647, "relative_start": 2.554664134979248, "end": **********.276863, "relative_end": 4.0531158447265625e-06, "duration": 0.34821605682373047, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46317792, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1633\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1633-1691</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.012879999999999999, "accumulated_duration_str": "12.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1324282, "duration": 0.00943, "duration_str": "9.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 73.214}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2027452, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 73.214, "width_percent": 14.519}, {"sql": "select `id`, `name`, `email`, `phone` as `contact` from `leads` where `id` = '12' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1664}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.226226, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1664", "source": "app/Http/Controllers/InvoiceController.php:1664", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1664", "ajax": false, "filename": "InvoiceController.php", "line": "1664"}, "connection": "radhe_same", "start_percent": 87.733, "width_percent": 12.267}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-833732148 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-833732148\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-896339904 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lead_12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-896339904\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2032333982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2032333982\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-22695138 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkU5MUpYajhMb3dHanFheHYxMGQvc2c9PSIsInZhbHVlIjoicEt0VkZQeG5iZHBNNEx2YlQ0L00wZy9KcXpCMVpuNnR5a29pTGZ6eVJsSUVWM1JhK0hPVXBHOXlVVlRsMzJvUkFRR1E5SVowaVpjSUxpcEhTdVB5SGpiNkNkSThjdDczL3VVdGZDNXNJUHAyejA0Ymptb0RPSTM5ckx4OGNEQnRwRmkxaC9qNXJHd0ZJMWtzdXZrMU9yVGNUYmZtVk95bHphYjMybkk4RFk0RlNiZTZQWGtPUkkrb0VqQitCSEJGYUN3a0xRcGcwN2IyVkp1MUhTLzM3UVAzZ2FJNnJ6aFpzUVQ3c2c4T0tGRGk2eUtmM1J2WTVqRHBFVVQ4T0JuRjdhSG52UFA4TnU3MHZiYTN5ZFlSdE1jeDRqcVNMY2ozRXU4VGc4WVlkQzhkQTgrQ3kvVU1YUFRwMGJVZVVMbHVkODZoMnRLMjJuVk4yd0VBMklBUHNaek1hM2dJbE9POUlxM0ovV3RaV2puNTNuZ0JVT1lKQUo5UHdrUXY2SU1xV3Q3cFZ3ay9nMVFidlA5bzErQUE5TE00dlFBOXVYQTFsT0xWZ01KL1BBQ2J2VnlFY0QwaXI2RFRJeG1kWFdkUjBLa0VjSXpzTThTcjRWSFVDbGZ6VGUzSUhrZ3ZXbDhBYkNCUmRuUVVIWUI2bW96ZlhHM2QxOTRhK3loUlo5NGgiLCJtYWMiOiIwN2Q2NWU0N2QzZjRjY2ZmMGVmZDFmYzg0MWQwZjAwMGFiODJjMzgzZGJkZTk2ZGQ4NTFlNDRlNTdkMmRiMDY0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkFwamY2V1NXTVZaOHYzRklYSlc5VHc9PSIsInZhbHVlIjoiRDVFRHI0UkJTK2kzcWU1YXAzQnFvQjA1cVFKSHRmZUJVbTIwaXUxbzhKUXR2SUFNb0RuaDJ2ZStYZXVsZERQdUowVmRDeStLSjdOSWU0QkZTZXlCdXJYSU1CQWlmUXF2ejJQT25xbWZ0d0djVm81b3pXeGNpem54NWhuS3FGUWY3TmdGUXF6b0g2MUM1MUg1eW5HQUIwWWgrRjNSVkZTcjQzd1VCVnNrQjBHOFQrdFlGODJ5MDFkejZva2ZMY3V3Tkx1WmN5OTB1eERrNXJSbkNoYXM2SEdsb0JsUjBlUnFzQ3g5UVlSbU5oUmRZS0ROQXdHa3pDa2dZM3dDVjdMUUZVMlBWR0hmTXJQVVVoYktSNTh0N01qcjN4VlhNR3hCU2piRG9abkhTTklCZTdKZUFhU2VsNTRHYnpjRGlDYzd5OVNvNFlzYm9MYW5aVkdqRS9iUHFhenNWL2pKa0ZxUTczRGNhQWJaNks1RXQyV1dZeXEzN0ZXN2ZHalRkL1JvTXBJaW04QWlYQjRGRVJHbkN4MjVubldkN3pQamRjRmwvWnEydlVpRVJoMjRkTkVwUUs0N3Z6T1o2OUtqNnppUzFxemEyNVIySHBqbWhxTDM5cmhINFNSR3ozNVdZNU83akZrbmd1dDdzYkFRNUFoR2dMNlJWRllVRU9EREtWUEoiLCJtYWMiOiJjMWFjMWQ4YmQyMjY5M2VkNzdhMjA4YWUyZTg5YTRmNTc0ZDJmOWRlNTczYmZlYzllOTBmMmE4OThjNTQ5ZmM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-22695138\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-770162572 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770162572\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-776753531 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:39:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVGNHArZ0dLYUpvNzVOSjVVT0Q4V2c9PSIsInZhbHVlIjoiV0t1MXZ6RWpuVlJQS3RKeGc4djNTMUtkbmRidFNlaG1sdStvSmxFZGhTQ3RDVjFnK1dObkJpZE52MktoYk5lOWt1QktHV1ZKYmRZcTJXajc1ZDlRTFRSWjZZSy80eWJJcnFidGo3NXZKcUM2THFHMklCZWN2WThuQjhxMFQyMzlMZjVNZkhHK0oxMWxnWGJuN0I4bG9ZOFNmOGdjajFxd1J5YkhNVG83MW1Kc3VBY2VvaVRDcGVTajJsRzF4d0YvK2x4VmZRTjcwVkMybHJKR3B3cFVjUWJNa3oyQkZtNDRSYVNCVFJSdmttaXduY2tBdFRGRXhTVTdaSWxEdWZaWmZ6T1JoQW9yQ3VoblpiUFROYzNPbFdmYWhaUmdFTUhUbmMzb0xDbEJwaUU5VmE3cDFEcFEzcmNHRC9JRVQ1N0d6SjFWWkdMN09JUE1hbVh5SDBGWVJKRjFqclZIYmgwVVhEVFFNRzMya1BxZnJoTml2VmRxUEN2S1A0K3M0RXhsMEpnQTNuMzJxa2l4S2ZwTUs4RmhrMFVBK0RTYkRVa1FLNmlZSUZPZUVhSzllOHJobHJHMHgxcXA3ZjRaMkVjSjQ4WEZGcjVGRVl4NER1VGRoOW9VVDNhYnZCT004YWFuaGhJS25zeUI4elpJcGJPbWlhUThLemV0LzJwUXlEZk8iLCJtYWMiOiIyNzU4MDI0NWZhMWQ2OGRmMDQwZTEyZDhjYTEzN2NjNTU0OGU1ODkyOTU1NzY4MmU0NTViNmFiNzI0NGFhMTc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:39:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlNZR2g0ZWRyN2VsTmxVVkxHVCs4clE9PSIsInZhbHVlIjoiSXJVazVSMGVzTHlWMXpsM0VwYUZrbG9SOWJhdGtaQjNQUGxlSUNURnRmczN6R0x2Ni90QVdlK2lEZVhHcmlleVhNOG02ZTY5emR2WG5PdXBvYXBzVXE5bFoyWmhaTXorVk9YbFZUNUZ0TzZlY0FrZWc1OGdvZHJabkxYb1g4dUFVS3g1UzdKVXVETS9iaCttdnd1WmFOZXVmN1I2cm1uZUFaTENzaUdhUUdUWDhsR0JiSmRUWTNhNWRaWEgrYWsrdU9QZUJhZ0hDM09DaUJhN2R1bWluWlNST0RLd2wzVUxzTk5ISWxIOVNUcTdjajF2K3JvQk9RME1xSEVtVTZ2SC9TOUhyUE8ycGg0NDhCWWplSkdYYmUrVlhsQUM3Q3IveHZvM3VmWldzVFYyVU9mdkYzUHpZb2RxOE54QzBQRGE4M1dVck9vUVJpN1lCKzdXQjBBSUJURDNHdDFRUU1nSmY2K1NmWlN4alV4TVA4cVAxbDl4UkNScldiZHA4ZWw1akpFSWtiYmJXSjlJTjc1dmRpaUlvcENIeC9ySjRpTnF1YnpqVmErRGdUUnNBZXJGLzJwQUZUeGVRYVYrTWRtVkZBQzRjTjJkNkZpbDIrN2JXRDdlcTM5SVg3Y2UxRnFhYW1SN09zcDV2c25CYmp5ZXpnQlA1a08zZDBRY05tamkiLCJtYWMiOiJhNzdiZmI5OWY0MTQ4MDZiZWFhZDQ1Y2VjNWNkMjgxZDhhZjJlOGExMTVjYjVjYjQ2MTFhNzc4NWRiYTMyNDY4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:39:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVGNHArZ0dLYUpvNzVOSjVVT0Q4V2c9PSIsInZhbHVlIjoiV0t1MXZ6RWpuVlJQS3RKeGc4djNTMUtkbmRidFNlaG1sdStvSmxFZGhTQ3RDVjFnK1dObkJpZE52MktoYk5lOWt1QktHV1ZKYmRZcTJXajc1ZDlRTFRSWjZZSy80eWJJcnFidGo3NXZKcUM2THFHMklCZWN2WThuQjhxMFQyMzlMZjVNZkhHK0oxMWxnWGJuN0I4bG9ZOFNmOGdjajFxd1J5YkhNVG83MW1Kc3VBY2VvaVRDcGVTajJsRzF4d0YvK2x4VmZRTjcwVkMybHJKR3B3cFVjUWJNa3oyQkZtNDRSYVNCVFJSdmttaXduY2tBdFRGRXhTVTdaSWxEdWZaWmZ6T1JoQW9yQ3VoblpiUFROYzNPbFdmYWhaUmdFTUhUbmMzb0xDbEJwaUU5VmE3cDFEcFEzcmNHRC9JRVQ1N0d6SjFWWkdMN09JUE1hbVh5SDBGWVJKRjFqclZIYmgwVVhEVFFNRzMya1BxZnJoTml2VmRxUEN2S1A0K3M0RXhsMEpnQTNuMzJxa2l4S2ZwTUs4RmhrMFVBK0RTYkRVa1FLNmlZSUZPZUVhSzllOHJobHJHMHgxcXA3ZjRaMkVjSjQ4WEZGcjVGRVl4NER1VGRoOW9VVDNhYnZCT004YWFuaGhJS25zeUI4elpJcGJPbWlhUThLemV0LzJwUXlEZk8iLCJtYWMiOiIyNzU4MDI0NWZhMWQ2OGRmMDQwZTEyZDhjYTEzN2NjNTU0OGU1ODkyOTU1NzY4MmU0NTViNmFiNzI0NGFhMTc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:39:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlNZR2g0ZWRyN2VsTmxVVkxHVCs4clE9PSIsInZhbHVlIjoiSXJVazVSMGVzTHlWMXpsM0VwYUZrbG9SOWJhdGtaQjNQUGxlSUNURnRmczN6R0x2Ni90QVdlK2lEZVhHcmlleVhNOG02ZTY5emR2WG5PdXBvYXBzVXE5bFoyWmhaTXorVk9YbFZUNUZ0TzZlY0FrZWc1OGdvZHJabkxYb1g4dUFVS3g1UzdKVXVETS9iaCttdnd1WmFOZXVmN1I2cm1uZUFaTENzaUdhUUdUWDhsR0JiSmRUWTNhNWRaWEgrYWsrdU9QZUJhZ0hDM09DaUJhN2R1bWluWlNST0RLd2wzVUxzTk5ISWxIOVNUcTdjajF2K3JvQk9RME1xSEVtVTZ2SC9TOUhyUE8ycGg0NDhCWWplSkdYYmUrVlhsQUM3Q3IveHZvM3VmWldzVFYyVU9mdkYzUHpZb2RxOE54QzBQRGE4M1dVck9vUVJpN1lCKzdXQjBBSUJURDNHdDFRUU1nSmY2K1NmWlN4alV4TVA4cVAxbDl4UkNScldiZHA4ZWw1akpFSWtiYmJXSjlJTjc1dmRpaUlvcENIeC9ySjRpTnF1YnpqVmErRGdUUnNBZXJGLzJwQUZUeGVRYVYrTWRtVkZBQzRjTjJkNkZpbDIrN2JXRDdlcTM5SVg3Y2UxRnFhYW1SN09zcDV2c25CYmp5ZXpnQlA1a08zZDBRY05tamkiLCJtYWMiOiJhNzdiZmI5OWY0MTQ4MDZiZWFhZDQ1Y2VjNWNkMjgxZDhhZjJlOGExMTVjYjVjYjQ2MTFhNzc4NWRiYTMyNDY4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:39:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776753531\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-659173455 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659173455\", {\"maxDepth\":0})</script>\n"}}