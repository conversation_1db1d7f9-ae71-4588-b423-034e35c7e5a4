{"__meta": {"id": "X2bd26e946fc8cbca10e8fcc578ae868b", "datetime": "2025-07-31 12:16:07", "utime": **********.042124, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964165.358186, "end": **********.042182, "duration": 1.6839959621429443, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1753964165.358186, "relative_start": 0, "end": **********.788249, "relative_end": **********.788249, "duration": 1.430063009262085, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.788277, "relative_start": 1.****************, "end": **********.042188, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3060\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1892 to 1898\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1892\" onclick=\"\">routes/web.php:1892-1898</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00779, "accumulated_duration_str": "7.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.94575, "duration": 0.00779, "duration_str": "7.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-636319955 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-636319955\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1531254564 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1531254564\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-296910098 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-296910098\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-818166941 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imo2VzJtS0Fsenl4emVyVUYvMzU0cFE9PSIsInZhbHVlIjoiRFZwZ0ZSZW16UTRUN2FWek5tTU4rQmhvL3ZuUXpUY2lscnFqalp2K3EyVnJqdHVSTndTQTdQMWdZNXBDbFVablQ0NS94Kys4WGtGZWRUdk8yNjJsTGhML2FaOEp1RXFJalAzT2xvQ1o2MlVSRlBMM0x3YWp5NWJMUWY5ZmlJRXByUmVVYllEMnlrS3M3b2hKVURxUWxPY0h3YW9KRWRacEo5MER6a1oyemJGQU0rWGJPQ2NQNmlGZFh0elNsNTd2R1pMcFYwWWN6SVp3RUpPZTdaY3hWQWpGbGQyOVhFVlBUV2xnS0dGWUt4dmVyV3VVamJ0VDdaOGUrWWVlV2x1aUVtSzdodXlJdVN0VWc2UlFKTDlDaXkrdk5VbWZXMjhGQVlGd0lYQUpEVkFxWUFpM1FobnM1a1o2bys3dEs2dWJVV0Z1UkY0UTBFdHE1YzJiUmtlaTJUbmd0NjVEUnhqTEJ2dWdMUkY1N3dhSlRlODVYNFNEQTVqQnlVYk90aXAyRXNnSnlxTjRIcDljY0ErUnA0aDdSMnFDZ3R1Y2VOaEYybWw4YnErTWVIcXNIcG9yNkVOR25peUxQSW1UN1cvbGl0ZUNHTlBSUC9jWExHd3g1ck5Fa3AxaXRrRmRHRC9NdWRUbXZFNWxZQ3ZHMUdNeWdVaWJYd3lYWEtmbWtGOXYiLCJtYWMiOiJhZjhhYjgxNzA3MTRmZjdhZWQ1ODdkNGEyM2FjMjc1NjgzNmE5MDQ2MjQ0NzIxODIyMWViOTczMDI4YWUzZGM0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkN2aWV5eEJNd3pXUlgxRGZoWFpUOHc9PSIsInZhbHVlIjoiN0lLT3lyeStERENTbVVOaW81YVd2RzllRmE2MXhYVjlpZmw3anE2L3NGNjVxOFc1S05ySXUxYXF5MkJnelFBVlRjaDhWK25uZEp1M09UdVFDTG0vU0ZUK1lxSWgvMURmNUYwUE5IYVZiZ3MyM3htUTNEWC9mZkNjM1Q2UmlXNk1UVGZYRmhzN3RkTjByUXA2RHFJbmxIZkpkejlQUGI0TzgrK1VtakN4OHc2aWtqL3oxQXd2bWlVUkxIU0NJdlNVK2ppVWdLWUErM2E0dk9laWpOMmlFaytPSFAwWlUxUkdReWQ2aE5IMklUR1NSNFJ0cDNDQzVVM09ZK0ZQTTR1aFdob0d2TjJSV2dpRThORm55Q2hrcVR6WldaODNXK3h6M1k0L25Wak52WlhpL0cwZUVJMkthbVJ3ZkorN2d2UkF3Unl1Z29ualVpTlpQNHJDUjRCSTdGejQ5a0pSZVNrTlJRTFJtMnFrNUxHZk9nMXUxcThMSmlsdXYxd25tL0V3MWtkQ0FScUVHazBBK3YwRzBVaXlKNzRCck9VVFQ5WmVVRCtzSXE3SlVLRG5jRU5mOWU5UFEwQXF0d21OTm9sN25JR2JTbGJuZUUwRE9vb2dMZTZPa040U0ZFYXBpd2N6M2VJZjZjanJxck5wcEVQcHdZMlF0TW9HWUJKMVdvRzQiLCJtYWMiOiI1ZDU0N2Y3NWNkZGQ4MzI1NDc2ZDZjZTc2YmIxZGJhZDJmNTU5NGE2M2FiZDhjMDhjNWI0NDM5YTNjY2ViNDlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818166941\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-292660929 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292660929\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-496145928 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:16:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imw0V0lXWmRKQU9iMjBrRVFHcXdjQnc9PSIsInZhbHVlIjoiZFVDV3o2WVlqWXQwblNTYnFvaFdUWVpaV3pzbkJ2Z0NnWUdsOVFUdndLMGZ1WjJuUzd3c3Y0K1B6U2UzanRBdkdxQW4wcmEwZVU5TFFJZjdvTkpNSFJpUU1CVEUxMFg0bnkzSDlKV2JSVSs2cENrekNLbGtQdWJMU3FlT2tXZzBCV29wWCsxdnFEUENpM3dhb2ZXYkRKQ3hwV1FINHhzcktOWDUwdzdaRDY3TWZJWG81RWxJYXdYdXl6NkNJT2lwNVFKelgrYm5RVU1BYkR4T3pZeDROeWlyWlg0NlhUbWtGTWduRC9XTHMwc1JZclBpbDliTTZkbUF0MU12V1IxUXpTNjNhUXI2bk5HTHZ0REFWRXBBZ2hHS1p5cUp3SkV5UkxRZkpvRE9PT1N3VVpUZUYxMmpPMjB2enMwMGJtTDJlSkRCMFoxYWNNWDY4RHFxODVqT0t2cndLSk1mWkRpRVcvSWVkU0xHYmhNcnhoRnY0TWo4QUNkd0xScjFUbmE5RkE1MDJzTWlXbDRXeXNvU1A0dEZ6Ui8yeXg0cHgyN3c3UnhqeFU0YUQwYVI5dkRSNmJ6MlYxVWF3UFJRMHB4SDFYS2hGaWdmdmxuc3B3d0ZnSlZzRUNKYWlXMm93NXJUdHczOEs5WGc1VmpFREdyc3V6aTN0YTZzbncxUHgxb3ciLCJtYWMiOiJiYmRlNzA2YjI1Nzc3MTllZmE3ODM5MWNlZmU2ZjQwNjk4ZTA0MjUxMTk1MjJjYTcyNjVkOGVkY2U2M2FjYjc0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlRYMU9IOGpURi9xR2w0SWNGdTk2QlE9PSIsInZhbHVlIjoiRDJwQ2x3QUwwbFNaSzFqYkYxWSs5Y0YwREh4OHNwSDlXQWNYTmVKK29MSVlqN0lEcHh6S05weXkwenRNNmZYeEprK0VTMHpFMlFWNDhOeG5VZm9uNlRHZW5mR3lxNTlsK1NZTDhBRXduS0llK0RVZUFFWnhhaVdWTWV5L1NQdzZ5YUFaS1pCVGRQSUxHMHBlSVc3TWpwOWZuanBFdHFsVEpCWGNzQmc3QlEyRVJneENYM0pONUMzS0svUmM4MjFCN0RURENvVDB0UlFkL2VCMnhmL3RiU1NpMEtZMnowZmZGbCtDZ0FCdE1adE5UNXhrNzBMVHFUdFB4STg5NTN6NmdaN0VMUFNkODRabm5zRmM4aVFXUk92MDFsY2IweWxmRExTL1ZpN1lvNm1RZFJpOXplYld3NlNBZFgrU0ZFcHhoVGVEVFE5OVFUZVFOVVNuUmoxM280T3o0YXQ0SUNoNFZqTDNPMUxJcGErUjhCdHI3d0t5Y0tPamZXdGVISFZvaE4wcWRscUdWN0EyOXZFdDUzSG96eXdRUnZLUU9tcUV3MDh6VFVLc2dqTWZsdUVDdk9QMVZ5ODFQRXQxMHQ5TFRtUFVZbXdiWHdxQnhlK2FBRS9FMG1qYVBxbm1PVkE0eXZnM1VRSThBN3BpRmRHZlEzNjZUc1RKeVFzdnhMcE8iLCJtYWMiOiIwODcxY2RlZmYyMjc5ZTVhOTliMDhlYWNkM2NjYmEyMmM2MzMxODQ0ZWViMzdlMTA5ZTQzOWYwMjM2M2M4NjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imw0V0lXWmRKQU9iMjBrRVFHcXdjQnc9PSIsInZhbHVlIjoiZFVDV3o2WVlqWXQwblNTYnFvaFdUWVpaV3pzbkJ2Z0NnWUdsOVFUdndLMGZ1WjJuUzd3c3Y0K1B6U2UzanRBdkdxQW4wcmEwZVU5TFFJZjdvTkpNSFJpUU1CVEUxMFg0bnkzSDlKV2JSVSs2cENrekNLbGtQdWJMU3FlT2tXZzBCV29wWCsxdnFEUENpM3dhb2ZXYkRKQ3hwV1FINHhzcktOWDUwdzdaRDY3TWZJWG81RWxJYXdYdXl6NkNJT2lwNVFKelgrYm5RVU1BYkR4T3pZeDROeWlyWlg0NlhUbWtGTWduRC9XTHMwc1JZclBpbDliTTZkbUF0MU12V1IxUXpTNjNhUXI2bk5HTHZ0REFWRXBBZ2hHS1p5cUp3SkV5UkxRZkpvRE9PT1N3VVpUZUYxMmpPMjB2enMwMGJtTDJlSkRCMFoxYWNNWDY4RHFxODVqT0t2cndLSk1mWkRpRVcvSWVkU0xHYmhNcnhoRnY0TWo4QUNkd0xScjFUbmE5RkE1MDJzTWlXbDRXeXNvU1A0dEZ6Ui8yeXg0cHgyN3c3UnhqeFU0YUQwYVI5dkRSNmJ6MlYxVWF3UFJRMHB4SDFYS2hGaWdmdmxuc3B3d0ZnSlZzRUNKYWlXMm93NXJUdHczOEs5WGc1VmpFREdyc3V6aTN0YTZzbncxUHgxb3ciLCJtYWMiOiJiYmRlNzA2YjI1Nzc3MTllZmE3ODM5MWNlZmU2ZjQwNjk4ZTA0MjUxMTk1MjJjYTcyNjVkOGVkY2U2M2FjYjc0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlRYMU9IOGpURi9xR2w0SWNGdTk2QlE9PSIsInZhbHVlIjoiRDJwQ2x3QUwwbFNaSzFqYkYxWSs5Y0YwREh4OHNwSDlXQWNYTmVKK29MSVlqN0lEcHh6S05weXkwenRNNmZYeEprK0VTMHpFMlFWNDhOeG5VZm9uNlRHZW5mR3lxNTlsK1NZTDhBRXduS0llK0RVZUFFWnhhaVdWTWV5L1NQdzZ5YUFaS1pCVGRQSUxHMHBlSVc3TWpwOWZuanBFdHFsVEpCWGNzQmc3QlEyRVJneENYM0pONUMzS0svUmM4MjFCN0RURENvVDB0UlFkL2VCMnhmL3RiU1NpMEtZMnowZmZGbCtDZ0FCdE1adE5UNXhrNzBMVHFUdFB4STg5NTN6NmdaN0VMUFNkODRabm5zRmM4aVFXUk92MDFsY2IweWxmRExTL1ZpN1lvNm1RZFJpOXplYld3NlNBZFgrU0ZFcHhoVGVEVFE5OVFUZVFOVVNuUmoxM280T3o0YXQ0SUNoNFZqTDNPMUxJcGErUjhCdHI3d0t5Y0tPamZXdGVISFZvaE4wcWRscUdWN0EyOXZFdDUzSG96eXdRUnZLUU9tcUV3MDh6VFVLc2dqTWZsdUVDdk9QMVZ5ODFQRXQxMHQ5TFRtUFVZbXdiWHdxQnhlK2FBRS9FMG1qYVBxbm1PVkE0eXZnM1VRSThBN3BpRmRHZlEzNjZUc1RKeVFzdnhMcE8iLCJtYWMiOiIwODcxY2RlZmYyMjc5ZTVhOTliMDhlYWNkM2NjYmEyMmM2MzMxODQ0ZWViMzdlMTA5ZTQzOWYwMjM2M2M4NjNhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-496145928\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1464867505 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464867505\", {\"maxDepth\":0})</script>\n"}}