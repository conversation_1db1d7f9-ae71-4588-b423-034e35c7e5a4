<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaxSlab extends Model
{
    protected $fillable = [
        'label',
        'percentage',
        'is_exempt',
    ];

    protected $casts = [
        'percentage' => 'decimal:2',
        'is_exempt' => 'boolean',
    ];

    /**
     * Get the products that use this tax slab
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get formatted percentage
     */
    public function getFormattedPercentageAttribute()
    {
        return $this->percentage . '%';
    }

    /**
     * Check if this tax slab is exempt
     */
    public function isExempt(): bool
    {
        return $this->is_exempt;
    }
}
