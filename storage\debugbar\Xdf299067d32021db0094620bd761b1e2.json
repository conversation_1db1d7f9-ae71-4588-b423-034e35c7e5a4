{"__meta": {"id": "Xdf299067d32021db0094620bd761b1e2", "datetime": "2025-07-31 12:14:52", "utime": **********.636627, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964090.959399, "end": **********.63667, "duration": 1.6772711277008057, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1753964090.959399, "relative_start": 0, "end": **********.423247, "relative_end": **********.423247, "duration": 1.4638481140136719, "duration_str": "1.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.423268, "relative_start": 1.4638690948486328, "end": **********.636677, "relative_end": 6.9141387939453125e-06, "duration": 0.2134089469909668, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47344912, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02045, "accumulated_duration_str": "20.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.524386, "duration": 0.0155, "duration_str": "15.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 75.795}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.564714, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 75.795, "width_percent": 6.699}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5790799, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 82.494, "width_percent": 17.506}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1368772319 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1368772319\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-277013428 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-277013428\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1518275930 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1518275930\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-828305985 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjJUZS95S09JT2d4SnhidVJTemQxZnc9PSIsInZhbHVlIjoib3NERVNpdU0vV0IvVVdKSWtlaWVrUUorcGdZdUhsN2hNbU5BUlNmMUV2bEdMRXBnOVFtKzNvOE9IcmhyUjAxZU1BY0JaSkw0OFRLK29aZk1nRUdab1JyVnVIb2g2RmptY2JjZEFrZjNnQ0dtaCtuRDNDdkFxeWFpRjJoUXdablRTT0M5OVFzUFJaTlVOU1VYam5hNVd4a1NzL3lmNk9hWHB6QXhTWjJJNkFBRjJUNEF4dWtaYnJqeFlyVXVpYVdBeDJUUitYL0ZJNWJ4Smp2bEozNTZIZ0pKWnZQc3UvdExhRlRzVU5ZNjBUN1lOWlBIQ0FkQVNMYmxFVGVJNUo4dVJPZ1liTlcyUER2cTVJUXRNZVJhUTErSU1nMXMxK25NNWxPbmowc2NGWnpHbkUyeENubUYrdUVKK0NGL0lGd3BQaWFDdnVFbHQxbUI2QmRLZ2lxNFVCK2V0UVYzeFNuc2JveVVIK1p5ZDV1MHFwc1NEZ09NVDFlZkhOL3FBSWpUampiekNxV2hlYWhRY09RWUd3VVZBMlJ2b3I2VnYvWUwwQ2dORzVMeGtXOThVWFBFVWNVQWRVQkJXMzRPVTZuY1R2WHUzU09CR0FoTHlCNkNJSjUvN1FmWjlpNXNJWGJ1VmRIdFRHSGFaSWlFdlF3UERXdy81Z1pESzUwZmRTRHEiLCJtYWMiOiIwMGUyYjE4ZmNkYzIyNTdjZWZhNWE4MWIzODQ4ODU2MTM3MzI4NTdmYWZhMzE3YjU3Y2FmZGY3NDY3ODBmN2VjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImdlZi8zcEMwT3VBNitCQXhaNVUwaGc9PSIsInZhbHVlIjoiV1JwZ0RyWGZhRlJJUXhYcDhtOFVEdlc2THlhekVWd204YkVvRTFQeTJMa0Q1Q2JoQTQwU3Mxck5HR1UvUjVROHFoTlNodW56QkM3RmJURlhzeXFIeWFCWmdRMmVsK0ZyOE1hNEtvemxWUmhoRjl5M2pCWVEwdC9RcUo1ejNjeU9BQlRxdXJTRDdGUGVzNExscFQxVFNjR3ZBWElMN3lpNFlHb3NuUDBhY2p1NFA3dDlXOFhhVFJFcVNjUnF0ZDJ5UGsyYjlHbFUxSVpKdzdUSU1xZTVsMS9hZnUzSnd2cDhuYlNnMmhZeCt5aFNCc0ZTZERCdzg0dWZpV0JtbVFCckVaYVhpTE9JVUJmVitzS29NblJtV3dHd0dlcFdRbHhFUzVXWTZYOW0wVG9EeXBQZWRUelJGeC81RDNWLzBncUloSm1EQU1na0NDN1BGclluaGxGN0FKRTlXSUZzcTV1WTJOOE04ekc0MURkTkdYcXBaOWJ0QUFQMkpNY1ZERVF5dDNrdGxUb0U3WVZZb0o3RDRYUHptQTlJUDc5VzJuMWcvR1NCMThIZUQ0bHBBUS8vVHMwQXVFa2ZUd3BxdFovckdOa1NuaTBUdWdzOCtrdDFGRVBCUG9FUE5seUJiK1Mxa2FPYmZsTld3MGxjL0FjdDc0cXlBUFRyWHdpRERIdWUiLCJtYWMiOiIxMTgzNGRkNTBhNDA0ZGJjMDMyOTQ0OTg2YjEwYjdmZDQ1YTViMWFlNGJkNDljMzE2ZWI5N2NjMTQ4YTY1NDk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828305985\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1779676773 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779676773\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1398103213 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:14:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBZaGJzQWVuU2xPNC84ZWphMDdhU0E9PSIsInZhbHVlIjoibGE0ZGpVSW40Nk52dGJYV2JRWDl1THFWRUJLanNSZVhxNUJQR2NKdTlLMkk2NDRaQnZMZkM2MDJYc1Z1VnpvcDRDSG9kVkV0STE3RVpjYUs1NGJDK3ZUWmUrVkVOMGI4RkdaMkc4dGVYZHNVNmJUT0hzc1hUY1ZUZ3lHdTdVVFdHQm5ERnhqVWdlbDRVQUxJbkk3ek02N0dJazdmMWtlbERFOU42RHpGdVVvS3lKZVhTM2pQRU1BWk9aOS9vRmgxVVUwdXYyQWlod1IrWkN0Yll6YndSQTFoN1QzZjgxcVZPQ01EZExvSnkvSFlCWXI3SjdzNnphVlVkbnF5TzJvT1RRRWZ1MzJsaTlrSnNCcnl6K0hNMC9HWFZzZUpHeXJLWmxweEpkVGtFN2NQcGduSGhEUGh1WEh6M3NUUUVhRDI5azhHWldlemRJMXVTZ25oSU9Ga0JQVVBtUlhleUN2eWhlUXdtYzdNaVlqWmVtdXlLc3Y5VWxobC9nN0tJMFl3TlEwYzJjZkFsUG9EYkw0WGRRdmpaU1U2QWZLME1ES1JsQTVNUWpPZ2RNbm8wVllPQk93eDFrRUJQd09KNnprK2NvWFRSaVNvdXN4TytBQ29Qd3ZHaERRa2NHbnB3Mi9uNWtHclJCOXJHT1pzeEtOc1JzN2ZjcjhRM0FaR0JqK28iLCJtYWMiOiIwODY3MTc1NjMyNDQ4NGQ0OTUyZTM3MTA4ZTk5ODFlOTc4MWE5M2I4YzhhNzYzZDIyOTdiOTc3MjEzMWI3ZWIzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:14:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IitXdGs2bHRHNGhPaDhhYzlFSlNpVFE9PSIsInZhbHVlIjoicTBzRHNFVHIyazNlTk1FbUVySmhycmxDN3pybWpqblppWmdrVDZLVkFvR2pNRUhJV1RnNDBpYTdjRlJMZmxudWlBTmpiVlBJUlZnRWJSWm93K2JpU2pEbEpyUlZEcjAvK1gvckgrZFpOb0VoWCtWNkNpbGdqaHNvRC9QTGJTZWVtVk1xMEhSNnJCTWxIVU5veHRGRkdYbG1xdmI3VUZUaU9GNXZqaEExUkJZMUxSOXhxbTNWV2tjbGx6S0I2UXozV0t4WEI5QUozeHpkM1lpSi9YNHc5RmxybnZPaUhCWTNCbGllWXdFSmhTUmYwL0lQMGFyR1NnSUxQMzMwNUgvdUtCZlFrR0ZBMDRyeHlFYlJnWUR4UWVkcDRpTHJ5M1p6MlpFVG0ydUt4N01OZnZkOGZpb3czVWJHUzFiVnNITDNmVXhZOTBhZG1FaWhJbUZwT2F0cXBGTklZd3V4Mi9DVVVObUxaeEJBVjVhR1Y5SmdnRWJjaE1mb0JPWU1Kc2hvTkF2Szhvd3hNeFhzWVZUNmRKNXRNanorRHIybGxNUE1HR1R2REZGMWR5MUxsaWt4eWRPblpvcFhRdTBLREJPeHNLTnJCdGZnN2tsb05rNmY1OEhLeTVvdUVGTlJlMTdLZmIycUpwbWhOK2ltS1lUbHlNZUdZbDBXTXZ0cE9GdjEiLCJtYWMiOiJlMWM1OWVlNjhiMTBmMDhhYWQ4OGYyNjZmYjE5YjdjZGUwZjY0M2VjNmQyYzYwMjBlNjY3OWM3OWZlYTBmMjQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:14:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBZaGJzQWVuU2xPNC84ZWphMDdhU0E9PSIsInZhbHVlIjoibGE0ZGpVSW40Nk52dGJYV2JRWDl1THFWRUJLanNSZVhxNUJQR2NKdTlLMkk2NDRaQnZMZkM2MDJYc1Z1VnpvcDRDSG9kVkV0STE3RVpjYUs1NGJDK3ZUWmUrVkVOMGI4RkdaMkc4dGVYZHNVNmJUT0hzc1hUY1ZUZ3lHdTdVVFdHQm5ERnhqVWdlbDRVQUxJbkk3ek02N0dJazdmMWtlbERFOU42RHpGdVVvS3lKZVhTM2pQRU1BWk9aOS9vRmgxVVUwdXYyQWlod1IrWkN0Yll6YndSQTFoN1QzZjgxcVZPQ01EZExvSnkvSFlCWXI3SjdzNnphVlVkbnF5TzJvT1RRRWZ1MzJsaTlrSnNCcnl6K0hNMC9HWFZzZUpHeXJLWmxweEpkVGtFN2NQcGduSGhEUGh1WEh6M3NUUUVhRDI5azhHWldlemRJMXVTZ25oSU9Ga0JQVVBtUlhleUN2eWhlUXdtYzdNaVlqWmVtdXlLc3Y5VWxobC9nN0tJMFl3TlEwYzJjZkFsUG9EYkw0WGRRdmpaU1U2QWZLME1ES1JsQTVNUWpPZ2RNbm8wVllPQk93eDFrRUJQd09KNnprK2NvWFRSaVNvdXN4TytBQ29Qd3ZHaERRa2NHbnB3Mi9uNWtHclJCOXJHT1pzeEtOc1JzN2ZjcjhRM0FaR0JqK28iLCJtYWMiOiIwODY3MTc1NjMyNDQ4NGQ0OTUyZTM3MTA4ZTk5ODFlOTc4MWE5M2I4YzhhNzYzZDIyOTdiOTc3MjEzMWI3ZWIzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:14:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IitXdGs2bHRHNGhPaDhhYzlFSlNpVFE9PSIsInZhbHVlIjoicTBzRHNFVHIyazNlTk1FbUVySmhycmxDN3pybWpqblppWmdrVDZLVkFvR2pNRUhJV1RnNDBpYTdjRlJMZmxudWlBTmpiVlBJUlZnRWJSWm93K2JpU2pEbEpyUlZEcjAvK1gvckgrZFpOb0VoWCtWNkNpbGdqaHNvRC9QTGJTZWVtVk1xMEhSNnJCTWxIVU5veHRGRkdYbG1xdmI3VUZUaU9GNXZqaEExUkJZMUxSOXhxbTNWV2tjbGx6S0I2UXozV0t4WEI5QUozeHpkM1lpSi9YNHc5RmxybnZPaUhCWTNCbGllWXdFSmhTUmYwL0lQMGFyR1NnSUxQMzMwNUgvdUtCZlFrR0ZBMDRyeHlFYlJnWUR4UWVkcDRpTHJ5M1p6MlpFVG0ydUt4N01OZnZkOGZpb3czVWJHUzFiVnNITDNmVXhZOTBhZG1FaWhJbUZwT2F0cXBGTklZd3V4Mi9DVVVObUxaeEJBVjVhR1Y5SmdnRWJjaE1mb0JPWU1Kc2hvTkF2Szhvd3hNeFhzWVZUNmRKNXRNanorRHIybGxNUE1HR1R2REZGMWR5MUxsaWt4eWRPblpvcFhRdTBLREJPeHNLTnJCdGZnN2tsb05rNmY1OEhLeTVvdUVGTlJlMTdLZmIycUpwbWhOK2ltS1lUbHlNZUdZbDBXTXZ0cE9GdjEiLCJtYWMiOiJlMWM1OWVlNjhiMTBmMDhhYWQ4OGYyNjZmYjE5YjdjZGUwZjY0M2VjNmQyYzYwMjBlNjY3OWM3OWZlYTBmMjQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:14:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398103213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-835810554 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835810554\", {\"maxDepth\":0})</script>\n"}}