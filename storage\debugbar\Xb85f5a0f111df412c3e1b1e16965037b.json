{"__meta": {"id": "Xb85f5a0f111df412c3e1b1e16965037b", "datetime": "2025-07-31 11:20:18", "utime": **********.441034, "method": "GET", "uri": "/invoice/contact-details?contact_id=lead_11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960817.757256, "end": **********.441054, "duration": 0.68379807472229, "duration_str": "684ms", "measures": [{"label": "Booting", "start": 1753960817.757256, "relative_start": 0, "end": **********.358616, "relative_end": **********.358616, "duration": 0.6013600826263428, "duration_str": "601ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.35863, "relative_start": 0.6013739109039307, "end": **********.441056, "relative_end": 1.9073486328125e-06, "duration": 0.08242607116699219, "duration_str": "82.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46309136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1593\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1593-1651</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00669, "accumulated_duration_str": "6.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4058259, "duration": 0.00481, "duration_str": "4.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 71.898}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4225452, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 71.898, "width_percent": 19.731}, {"sql": "select `id`, `name`, `email`, `phone` as `contact` from `leads` where `id` = '11' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["11", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1624}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.427615, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1624", "source": "app/Http/Controllers/InvoiceController.php:1624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1624", "ajax": false, "filename": "InvoiceController.php", "line": "1624"}, "connection": "radhe_same", "start_percent": 91.629, "width_percent": 8.371}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-737661293 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-737661293\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-488137259 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lead_11</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488137259\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1177384151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1177384151\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-544038315 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlJZZlhKak5KNkVrQkd1WnpXSzdHU0E9PSIsInZhbHVlIjoiV01EdEFwallkVStKckNqSEpCdzdXd05ULzdjejN5c0NreEZjcno3UkJlOXpEK1k2aDN1TnpYaXRWaTFoaCtHWjFtd2NKdG45WUhIVm00UzBwTXFhc1FMSWp5Y3ViUXRsSjRKRGpzNlpQM1lzRDVueitaK3d5NTRhS2RGUms1SzZVL3NVWERPZUlwZGdCWlg5NXBNWnd2Mk8wY0YvY2pERDhoOXlsU2VaSXZFemRONTJsS2hPamxPTXR5c3JBbkpIWThjUmZIY25BcmhTNlFoVnNzMFNjNmgyNWcveFVyUm1JWEFUYWc3cHVyK1p2Q0Y2VUdHYW8wRHdmeFYvc3VMc1FBcFVTZ0Q1MmJzck9WTHVDQlZ5VHFDWHlKQlNHVitBM0dFUkxJQVZtT29nOWYwSjFHelVUamFrQnZhcldpTGxlR0NrYUJ1TlNIcmttZ3VhbFNxaFJKVnFoTjFlQ205cTQzNTBHUFoxYnliWG5id01kZFNHOEdrMDRGNmVkV05GU0ZEVVhxUEFzOGdoTXhGZXZOeEFFWGh5YVBJN2NwNXJIRnh3V0dSZGkvVFBWaUlTeGUvY3J4OVJYTjcrdjlkend1NWZ0TWFNMHdhazBEemFtUmlvcUdOM3BPbVVENXdlWTFwMWFZRGFIYjVMMzdVb1FwT2svZHNvZnVubUhVNkYiLCJtYWMiOiI5N2UzMWUzZDFiZjQ4ZjJiZTFjMTM3MjUxMzYwMmMxZjYzNzA3ZmQ2NTc1MjBkMzEwMDE3NTI1ZjU3ZmRiZWZlIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkU1SGQ4K2dFUVZlUmhiYjd6dE9NTVE9PSIsInZhbHVlIjoiNGRPQU5PSXBRUlhGcnVUdTJEclZHb3BtWTk1STUrNTF2VFJVcGttOVUyY0FRbXBCVWo5Sk5iTElWRTFKNWF0UkhoNHNmZmdtZDdzeTRDVXVLSDJtcDViSWN4QmROZXppTmZ0VEJOQVhHWHdoOTN3clpYa3h1aXMyUmE5cnUyRUlLUU1JU3R2UFczQUwxbjRkR3dhdWY3d1ZxNFlOaDA2N1B4cDlCWHBqd01rWEg3UTFWS1V1UVFBVmdKMUc3bjg1RkZmUUpiWFBEaDFqdHFKZTlGaWdvK3BqQkVTU3IzaExxZkd1ekpZWlU4QWt6MXNmV25Nck1xMjNnRXJ5MlJYckQvdUpxVUJCb0tRZzI3TS9QOGZONG50OGdkbnNYcTQ2aFRla245K0tDWGUwdXVwYUVXT2ZhK0l2ZnVJY0ZpRDVIY09wdXgyRWtuNnpIRGY1OHhzQ2xWeDdkRGoyS0owRUJmYzYxWkMxQi81Rk5GaWFUclpqTUNXYkVnQjd3TUxFTlJFeFI5N01BVElRNXozTWo5UGkyN3VFcmt6VjhOTVplRXdyUTR4QWwwZjNLY0R0MDJHaWErVUkvL3lPTEZiaHRUZDJIWHFhNUswUEw4Sml4TkZvMEJyTXQ3Z1Nna3g4bm94TzNWeitWUFU3R0t6MlhsQjlCdUVYMC9TS3dDVGMiLCJtYWMiOiI2M2I2M2VjYmQ1YjMzYzA5ZmU4NTdjNWQ4ODM5NWE0NDgxNzBiMGEwODlhOWI4NDc1YmNjYmYzZTU4NzkyOTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544038315\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-60859783 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-60859783\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:20:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklWYlhJMjB6ZllYeS9XYTJwbEQrcUE9PSIsInZhbHVlIjoiWW5PUHhJYzFqcXdIK0RNaC9MZTNlSVBtNVVkYVBvTGVBclFGdGo0VTVUWXZlL1oxUnhldjRTcW1lNVIwRzRWalJucFlhK2ZpWm4rNlBJaDVDcjJxOFdlV0kwSG9QWmVyMXFuRUNMb3VQQ3dQeGZKZjd2WlZGQ2tuYysxaWowc25RRjBwb200NVRnbUY2SnhSSERqazljb1V3WjVsdy9KTjJmYmVMWnRuSk5ZckJDM1dLNGo3aGQxbzhNM1RmbEZkSnRnQ3g3UUI4TDVMdkwyUFdXN0lCSHk4Y1pSa2xuTEJ3Nkpqb2NQTzd0UGtGdm1FeGg3U2h2NWJDOXlhV0htdm5NVzdmUFlyZVRxMlNUZldFWHdmWC9yKy9zZE85Z3QzaFpSdGR4bEVETDdkWG9vNFdqMkNDN05ReVZINmtLYmZrS0hYZEl1TEtUaDlTcU1GRmVxL1FtOVVydFZKZHFqRTRsVjdlOFd0aTltWVBVbGc3aVFHVUdCNjhkVlI3d3JKSFJTUXFPRHZ1bW0yZ1pNczI1ZkVDb0Y3RUlIS05VS3FOY3VzNmdmaGJHSnpIaVpHWWFIbGl4LzVtaWs2ODdRZ0ZhWk1YTHllclFKQ0xvamliaFlsY2hBTzJsR1NSTVQ5TituQ1pGVHI5V1dSSnI5clE2NmZOSnF6enZqWXM2RVYiLCJtYWMiOiI1MDY4YTUyZjY2MDQxYTcyODA4NmUyMGQ1OWU0OWZmYjZmODM3NWU3ODZjYmUwNWIzMjc0MWVlNWNmY2ZjNDg5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:20:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6InVjOFg1UXZGWkIrd29pUkRqRVRWdFE9PSIsInZhbHVlIjoiSGNFVmZnTVZUdDc3OGM0RmJxREhZcEFXbm8wRUFnZWQ1b0pvdDBhQ1R5am1PczhUc3BvNllEeG0wdGttamJubEVGSG9aZ0JBcXRlMlhvVi9rbGVId2lZV2lsTFNNcjBzQnpGZWI3aEJTa0QwTloxaStXTDVmK3QyeFJLalR1UVlaVHdHN2xCSGROektMa2xEVC9qUys4NFRFMEd3cmVXZ0hrcitnSUVOa241VGpEZWJlVlVCUDY3YzBnWVlsNWh3WUNCUWZHUkptb3dqbGViMEEvZXdGVzBhNVFFLzVqNXFvYVlhNWdXSnpOUnQ4bDd1L1BRRDc0N24xV2tkRUIwMlBXR243OVVqNDlDN1FVaHUvRFJUMHJydDIyWW1wM2RISGd2K0JZL2MwTTJLbmt5SGkrd0Q3Tjd5RmU3R2VOdDNWdTBFTWpSYkRGVW1qOTZ2bnhzR2MrSHpNM09xb1lXSVFNK2o3MjRjMXhpNTJac2ZieVhqNldOMmFhejZzZVJXcGVMTmdlQ00wNTlrWlRCcG1vQjhMWmEzQ3FyLytJcXB1a2J0NVhhN1F1YXczaWsxcTFDZ3B4N1RzMXh3eUtpMW5NRFZteExCV1hPeDFVREY0ZFlUb1NBMG5nc2U4cXpkMEd5L2lEd2o2R0pLaXgvWGZoOFlhanQrdW1OeG1HSysiLCJtYWMiOiI5NGJkM2NhZDUzZmNjOWJhYWEzZjdmZThhZTE0MjQ2MTIyOGVmNDMwZTNhZDIwYWM2YjE1ZDQ2N2ZhMzQ5NThmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:20:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklWYlhJMjB6ZllYeS9XYTJwbEQrcUE9PSIsInZhbHVlIjoiWW5PUHhJYzFqcXdIK0RNaC9MZTNlSVBtNVVkYVBvTGVBclFGdGo0VTVUWXZlL1oxUnhldjRTcW1lNVIwRzRWalJucFlhK2ZpWm4rNlBJaDVDcjJxOFdlV0kwSG9QWmVyMXFuRUNMb3VQQ3dQeGZKZjd2WlZGQ2tuYysxaWowc25RRjBwb200NVRnbUY2SnhSSERqazljb1V3WjVsdy9KTjJmYmVMWnRuSk5ZckJDM1dLNGo3aGQxbzhNM1RmbEZkSnRnQ3g3UUI4TDVMdkwyUFdXN0lCSHk4Y1pSa2xuTEJ3Nkpqb2NQTzd0UGtGdm1FeGg3U2h2NWJDOXlhV0htdm5NVzdmUFlyZVRxMlNUZldFWHdmWC9yKy9zZE85Z3QzaFpSdGR4bEVETDdkWG9vNFdqMkNDN05ReVZINmtLYmZrS0hYZEl1TEtUaDlTcU1GRmVxL1FtOVVydFZKZHFqRTRsVjdlOFd0aTltWVBVbGc3aVFHVUdCNjhkVlI3d3JKSFJTUXFPRHZ1bW0yZ1pNczI1ZkVDb0Y3RUlIS05VS3FOY3VzNmdmaGJHSnpIaVpHWWFIbGl4LzVtaWs2ODdRZ0ZhWk1YTHllclFKQ0xvamliaFlsY2hBTzJsR1NSTVQ5TituQ1pGVHI5V1dSSnI5clE2NmZOSnF6enZqWXM2RVYiLCJtYWMiOiI1MDY4YTUyZjY2MDQxYTcyODA4NmUyMGQ1OWU0OWZmYjZmODM3NWU3ODZjYmUwNWIzMjc0MWVlNWNmY2ZjNDg5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:20:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6InVjOFg1UXZGWkIrd29pUkRqRVRWdFE9PSIsInZhbHVlIjoiSGNFVmZnTVZUdDc3OGM0RmJxREhZcEFXbm8wRUFnZWQ1b0pvdDBhQ1R5am1PczhUc3BvNllEeG0wdGttamJubEVGSG9aZ0JBcXRlMlhvVi9rbGVId2lZV2lsTFNNcjBzQnpGZWI3aEJTa0QwTloxaStXTDVmK3QyeFJLalR1UVlaVHdHN2xCSGROektMa2xEVC9qUys4NFRFMEd3cmVXZ0hrcitnSUVOa241VGpEZWJlVlVCUDY3YzBnWVlsNWh3WUNCUWZHUkptb3dqbGViMEEvZXdGVzBhNVFFLzVqNXFvYVlhNWdXSnpOUnQ4bDd1L1BRRDc0N24xV2tkRUIwMlBXR243OVVqNDlDN1FVaHUvRFJUMHJydDIyWW1wM2RISGd2K0JZL2MwTTJLbmt5SGkrd0Q3Tjd5RmU3R2VOdDNWdTBFTWpSYkRGVW1qOTZ2bnhzR2MrSHpNM09xb1lXSVFNK2o3MjRjMXhpNTJac2ZieVhqNldOMmFhejZzZVJXcGVMTmdlQ00wNTlrWlRCcG1vQjhMWmEzQ3FyLytJcXB1a2J0NVhhN1F1YXczaWsxcTFDZ3B4N1RzMXh3eUtpMW5NRFZteExCV1hPeDFVREY0ZFlUb1NBMG5nc2U4cXpkMEd5L2lEd2o2R0pLaXgvWGZoOFlhanQrdW1OeG1HSysiLCJtYWMiOiI5NGJkM2NhZDUzZmNjOWJhYWEzZjdmZThhZTE0MjQ2MTIyOGVmNDMwZTNhZDIwYWM2YjE1ZDQ2N2ZhMzQ5NThmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:20:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}