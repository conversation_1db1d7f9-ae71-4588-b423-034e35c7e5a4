{"__meta": {"id": "X7a108842ef42a2122a8f907b4026f3a2", "datetime": "2025-07-31 12:30:34", "utime": **********.495379, "method": "GET", "uri": "/invoice/contact-details?contact_id=lead_11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965032.322124, "end": **********.49543, "duration": 2.1733059883117676, "duration_str": "2.17s", "measures": [{"label": "Booting", "start": 1753965032.322124, "relative_start": 0, "end": **********.257962, "relative_end": **********.257962, "duration": 1.935837984085083, "duration_str": "1.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.257991, "relative_start": 1.9358670711517334, "end": **********.495436, "relative_end": 5.9604644775390625e-06, "duration": 0.23744487762451172, "duration_str": "237ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46317360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1633\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1633-1691</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00804, "accumulated_duration_str": "8.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4041002, "duration": 0.00538, "duration_str": "5.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 66.915}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.44097, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 66.915, "width_percent": 14.055}, {"sql": "select `id`, `name`, `email`, `phone` as `contact` from `leads` where `id` = '11' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["11", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1664}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.455516, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1664", "source": "app/Http/Controllers/InvoiceController.php:1664", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1664", "ajax": false, "filename": "InvoiceController.php", "line": "1664"}, "connection": "radhe_same", "start_percent": 80.97, "width_percent": 19.03}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-1698507348 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1698507348\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1656309437 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lead_11</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656309437\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1537651517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1537651517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1805421573 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkI4ZTVmcytYdTh1M0hMRkhyc3lxK1E9PSIsInZhbHVlIjoiRSsvNC9JUGcvN2VoUVBQaEI2S1dOVVEraUIvaTJhTWJtaGZGK3dpK3NqRSs1VGZWZjJvaHZKdGNxZTVRa0pnQnhHZlE3Q0xQRE44QlVzU2VoUHl3UExuMzBTYklsdkdwK0UwMHJjL3FTQ0U1MnZaenc2UzVZU3BqOEFtK3BCY3dGN2xubXg3VGhXMHZiUlluOHpsV1V5Y3VpY01USk5Dc0JGWE9tV1V5dlJ0WGN3Vzk0SkFnN1ppZEtnQVNxcDRqbnJHSjhuSDJJMXFpSnZCV25pS2orQXU5SDFQN2lUaXFtdkFYQzRYUWFaYUNJNEtIVUdBaEhVZk96SGc3eFdwQlM3NnB0eEVFS3ZYVkIrVkRDTXFyVzllYkFRTm9JTVV2bGo3QmZxQVhiZVpiUktIa1FHbkt1bmZUL2pVc3NadXFZZG1EYi9EUTVjMHFPYUdHa3AvUXBZamMrUFM5eW55disvbEFjbWR3dGVTYnNWNlErTFk1WE1jWlVVclpnZjB3Z016VEtnbGE5NkVlbXNCV1pSNEtLTWhsL2wwLzlvdGdzVFdzQjU3cXZBNEdKYXlTMWRLTW9SY0FCNU1NRTJOaDVZZmMwbkYvY2txZWxXOGJUT3RpelFWQzE1WmlxUU1QVGJyNm9odmpabjR1SWVJUGFUMVN0NDBVdHpLWEg4dUciLCJtYWMiOiIwNzRjYWNiNDU3MjI5OTZkNTZmMjFlNzZjNjZmN2UzMDZkZmMxOThjNDg2ZmUyODg3NGE5MjM5NDRkZWJkZTBmIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IncvTExYNFhBVmpENDhveWhKTGZWamc9PSIsInZhbHVlIjoiN0dLN2JSYXQvb1FLWFgwblVoWnVpM2w2WjdwNkRlU3lSbldUNXMyeGoxdnB6R2xyME9jd0ZLV0hUMHZBY2NWQ2x6UVVMcVFWQTd1VkthLzZ5dzhpeDBiNFVNbmkzYzRFdERVVmt2cFN1SnZ4YWdiYWlWOW55dllMdjEvRmtUVFVlSzRIMW9CRTlJUXlRVzAwdStleFAvdmkzd3pxdDR1azNxamdqUU12MkRnRjcyY3lIZ2w4MlhsYUljYm40djdmSE1GQktRZ0gwNVFZU1E5R3VaMm9YeTVMbmRaNkFNQTRBKzFWem9CVnl0L1ArQXRQRVdLOWpNSnZscWd0aElKMWFoMUhWMGQvMHFWQ0dpcmdSMW91eTNCaWVmekpYSzczM0QvbS8vMlVYNU9yQmo0a29YT3hhZ3pod2xPdE5KVGNJNWFMb3B4TkdrZXhDd0pLWDVlODRrY01QRHFndzBEMzJaVGc3VzdZVkE1Z2U2TFBiYSs2aWZkRUt0UHlSUXloNkxwSThqL3FyNlBEL2kwcUE1MlFOTDZETjgwaUh5aXpwTzY3dVRyVHJReWc0S3ppWHZlZXpseVFqV2hBQTM2K2h1TmRhRHdNSlhXUU9TcWdRY1JtdGVuNkdJeG5pcU5Id2w1U2RlYkYwdUNVdmFUQno0V3ZUbkd4YVZMRkIyRzQiLCJtYWMiOiI0ZmU3MTVhNjEyYzVjMTY1YmI0NjFkMjcxOThjMjg0OWE4ZDdkZjMwOTBjNjRlNTczMzE4NGM5YmUxMjIxNTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805421573\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1677432638 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677432638\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2064302265 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:30:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlGdWMvNmFOZCt0YTliLzc5ZncrQVE9PSIsInZhbHVlIjoiTHZKbVZvT2ZsKzBKSTFrc0Y5M0prUit4RDczdDliSW82VENiTlNMbkdlUlM2OU96K2ZWMmpXY082R29sNHI4dUdXbldua2tPaXMxMVRST1lKckl5djlra1c1VEZsc05Bak94MzN1UzIyLzU2OGt5dVd4ZmRKdURGZ05WM0EyUHlsbU92ZlM5VkgxdE5YNk9iTS9KT2hRK3VvbTMzbnpBOUcxaThFZ1VGRGNEWnVxN2IrcWg3QkxuaTJhNkRKbDhRRzd5YllNaWt6eUkxZ0pSVjZJaVlreGRqSjM5RXA1dUlUOWp1d3k2MS8rZmM0WDR0RGNXdDE0NXE5MGV2dVVMUFpqOTdNN3dUY3A4clJ0N3VqQWRvWlE3dHdRQW1nTndBU3FmZGZFRXplZENuYnBVbnNnT0h3bEwzZnpuRFFiQzJCOWw3OSt6NjEyZzVtVWRXbytSSnFJTHVuM0JsVFZEdXZnUDM2N1lWTGRnSjhSdTE0Vm45amN3RVY4YmtmU01sUkwvbUZXMVltcy8xNWwyV3VkY09wUWFzanNRcVE1eEM3MVJySjZZdW1kcWhxSFlSRklvd0x6Nk1aTGtRQVBPd0dWQk1NdTdmMXI0YS9TMmhFb2pGSStxeXRLZEhiYm5DSk1IQldtdGsvMSt3MkJCaWl1UmNlTVpKTkFMcTNCdXciLCJtYWMiOiI2ZjU0OGYwOWI4ZmQzNjUzYjMwM2Y1YWI1MzdlYzM5ZGUzOWYzNzM5NTY0ZWI0NDQ5ZTRhNGJhNmQxMDA4MGZmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:30:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ii8zMkhaL3JNa2lKaG5BVVVzd3M3Mmc9PSIsInZhbHVlIjoiMTlzMHdZZjgwU0tyNUQ1dUxPNllnd05hU0htcXV5YitiaDZ6cVpBRTZEL0R0MzhFakVnRXp0M2kzTnpKaFI5d3JMRWQ3K005R0lnSjFoMzE2c3dQK0gyLzFlN2hYa284OEZLd2FHTEh3ZUwxZjNBK0R3NHdGQnovZlV6TFhFcHRqSFlCZnh0SnRPWm5sZUdKb0JwQ0FyVmUzQ3hsUTNlWmxhUGIxVUVWa05wRlFjK1JjKyt6bkh1OEZwUC9ibTRpV2ZndVowY0QxOXcrbHhCRXVqbnVnN0J3S1Vtc0tyV1ZNTTB1OW5vZC8rV1lvUUNwVmZhZDI0RTl1VE94RUFFNTcvMVpPbzBhZlJaM2x6RS9zdk11Qk1pZHZNRytIZXpNSHBHZXlacGI4MjVyanJDbjdVbE5BTXVDOXdJR2dWVUVHNXlkeG9WZmFYRFRVeFJ2RjFZbHhQRmdVbzlNbEFscmVZd1RlZTlNbHRYZ1ZYcTN1blNnUWhQNVByRjE0RWpBa0ZHQ2l6ZzNMUmlXWmxVbzZoRlFNNXU2SmVQT0phejVUYkVQR0lBNDlSNTFzVHM1ZDB1V0kzYXl4ZGVlVVlmQ3k3Z2MvdlFOTkNBWnJJbk5jQmtLdXRxSnE0VDNJcSsySGFqaWpjS0oydUFPTzRGL3NwdysxYTU5U3F1RlVhZHAiLCJtYWMiOiI4YWVmMDkzNmZmZWYwZjViN2E0M2IyYjA3NjFlN2NjMDVlNWFjOGYwOTQ4OTU4ODdjZjcxYjlhOWEyY2I1YjEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:30:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlGdWMvNmFOZCt0YTliLzc5ZncrQVE9PSIsInZhbHVlIjoiTHZKbVZvT2ZsKzBKSTFrc0Y5M0prUit4RDczdDliSW82VENiTlNMbkdlUlM2OU96K2ZWMmpXY082R29sNHI4dUdXbldua2tPaXMxMVRST1lKckl5djlra1c1VEZsc05Bak94MzN1UzIyLzU2OGt5dVd4ZmRKdURGZ05WM0EyUHlsbU92ZlM5VkgxdE5YNk9iTS9KT2hRK3VvbTMzbnpBOUcxaThFZ1VGRGNEWnVxN2IrcWg3QkxuaTJhNkRKbDhRRzd5YllNaWt6eUkxZ0pSVjZJaVlreGRqSjM5RXA1dUlUOWp1d3k2MS8rZmM0WDR0RGNXdDE0NXE5MGV2dVVMUFpqOTdNN3dUY3A4clJ0N3VqQWRvWlE3dHdRQW1nTndBU3FmZGZFRXplZENuYnBVbnNnT0h3bEwzZnpuRFFiQzJCOWw3OSt6NjEyZzVtVWRXbytSSnFJTHVuM0JsVFZEdXZnUDM2N1lWTGRnSjhSdTE0Vm45amN3RVY4YmtmU01sUkwvbUZXMVltcy8xNWwyV3VkY09wUWFzanNRcVE1eEM3MVJySjZZdW1kcWhxSFlSRklvd0x6Nk1aTGtRQVBPd0dWQk1NdTdmMXI0YS9TMmhFb2pGSStxeXRLZEhiYm5DSk1IQldtdGsvMSt3MkJCaWl1UmNlTVpKTkFMcTNCdXciLCJtYWMiOiI2ZjU0OGYwOWI4ZmQzNjUzYjMwM2Y1YWI1MzdlYzM5ZGUzOWYzNzM5NTY0ZWI0NDQ5ZTRhNGJhNmQxMDA4MGZmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:30:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ii8zMkhaL3JNa2lKaG5BVVVzd3M3Mmc9PSIsInZhbHVlIjoiMTlzMHdZZjgwU0tyNUQ1dUxPNllnd05hU0htcXV5YitiaDZ6cVpBRTZEL0R0MzhFakVnRXp0M2kzTnpKaFI5d3JMRWQ3K005R0lnSjFoMzE2c3dQK0gyLzFlN2hYa284OEZLd2FHTEh3ZUwxZjNBK0R3NHdGQnovZlV6TFhFcHRqSFlCZnh0SnRPWm5sZUdKb0JwQ0FyVmUzQ3hsUTNlWmxhUGIxVUVWa05wRlFjK1JjKyt6bkh1OEZwUC9ibTRpV2ZndVowY0QxOXcrbHhCRXVqbnVnN0J3S1Vtc0tyV1ZNTTB1OW5vZC8rV1lvUUNwVmZhZDI0RTl1VE94RUFFNTcvMVpPbzBhZlJaM2x6RS9zdk11Qk1pZHZNRytIZXpNSHBHZXlacGI4MjVyanJDbjdVbE5BTXVDOXdJR2dWVUVHNXlkeG9WZmFYRFRVeFJ2RjFZbHhQRmdVbzlNbEFscmVZd1RlZTlNbHRYZ1ZYcTN1blNnUWhQNVByRjE0RWpBa0ZHQ2l6ZzNMUmlXWmxVbzZoRlFNNXU2SmVQT0phejVUYkVQR0lBNDlSNTFzVHM1ZDB1V0kzYXl4ZGVlVVlmQ3k3Z2MvdlFOTkNBWnJJbk5jQmtLdXRxSnE0VDNJcSsySGFqaWpjS0oydUFPTzRGL3NwdysxYTU5U3F1RlVhZHAiLCJtYWMiOiI4YWVmMDkzNmZmZWYwZjViN2E0M2IyYjA3NjFlN2NjMDVlNWFjOGYwOTQ4OTU4ODdjZjcxYjlhOWEyY2I1YjEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:30:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064302265\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2012211337 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012211337\", {\"maxDepth\":0})</script>\n"}}