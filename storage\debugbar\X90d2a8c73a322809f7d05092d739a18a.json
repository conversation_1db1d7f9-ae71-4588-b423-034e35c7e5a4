{"__meta": {"id": "X90d2a8c73a322809f7d05092d739a18a", "datetime": "2025-07-31 12:38:55", "utime": **********.412319, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965532.612248, "end": **********.412402, "duration": 2.800153970718384, "duration_str": "2.8s", "measures": [{"label": "Booting", "start": 1753965532.612248, "relative_start": 0, "end": 1753965534.897987, "relative_end": 1753965534.897987, "duration": 2.285738945007324, "duration_str": "2.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753965534.898014, "relative_start": 2.285766124725342, "end": **********.412411, "relative_end": 9.059906005859375e-06, "duration": 0.5143969058990479, "duration_str": "514ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47346872, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.033, "accumulated_duration_str": "33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.154103, "duration": 0.0268, "duration_str": "26.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 81.212}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.275036, "duration": 0.00309, "duration_str": "3.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 81.212, "width_percent": 9.364}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.305448, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 90.576, "width_percent": 9.424}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1216402423 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1216402423\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-850985069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-850985069\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1769982995 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1769982995\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-87212516 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Iks1NGhjUmtDQUpsZ1pJdkx3QmlFWmc9PSIsInZhbHVlIjoieEFPaEdMZHBqbThnRkNMZ2dFSm5FVmJwam9aVE5wZG12S1JzOHN1WVRVcyt0TXdVYVFFKzlQZnFqUVE1UmhDRWhDYUlvc0E1Vi9QQVQvaG1VdGtHT1FuczFYUmp1N0J3TGoxKytVQTRtelNsY1BTWDRXZVFmQk1TeHFiNm83TVc3MXdCcUNrVGNDQVlJV2Q0Q0FVb2RseDZGd1dCdDI1ZGdCd05VVXZQdWhXYzJIdGpqWEVyZFBabmp0Y0ovZjY4bk1JK1J0VWpvdXdJalUycjI3WnFXaWQ3aG82TWYrR0RPMnVHM3VGWFhudUN1QUlHRURPUTArVmUwQXhRY2JyNlpTc0VmaG5BdDRrZlhMbE1PMFFUNUhUeHl2ZXhjeVhLa2RsU3hWVFJKQTNmVFVhVGF1WDhWWU1KQ1UxdWZLZldVdVJOVktGSlBQMW1oMWFQWFRDcUM2N2RyYXpheXg1dnI0SVpXVk52UXJRT2p1eVh5WE54MmUvUFUvWElSaHQxbS9jZmluYWdIMGZFRldPQXByUzIzMU1KQUh4S2hCSURneHM5bkNiMEpmWTRjMVB4ZXBLVjloTXl3RkFtT1ZYSzZjbm9qKzI0WWt6bzhYY3BjYkhlVm1KYW1odG1UT05LRUNDT2hGcXZlTTBRWDhqK3pWVU1DaW5DWlpEZjlqU20iLCJtYWMiOiJhNTljNjBjZDUwYTNjOTg3Yzc3YTQyMTZmZDg0Y2MzYzA2NTlkMTcxNmRhYTM4ZGI5NzY4NTQ4Njk5Yzc3MzI3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlVjb2FMNnJFTHFMcldicFkyS21HTmc9PSIsInZhbHVlIjoib0ZPaFIzZmF2N1BZbFA2TDJsYW5pOXQ4Qm5uYUNYNnI3QXl6RFRscDF1WnZ1WUtoZzBUdmNrM1FCdnZIeGtRNFZvRDIvb2JwT3Z2SXFwRGJjNWsvcUZrQjB3NlFuU2VEaXNwK0kvL0QwQUY2b1JwSUxObHIwbmc1QW1yLzVIYm1STzFKbnMvSzNxYmoxQngvWEF1Q3dJMUtGMVBSV0FKSUZvUDkxNU05WmpJZThtYmZpMTNPU2kxb2ZZQ3pmdEJZcVNvRlBuWG5vYXFteDBtYWx1ZW1NRWh1RnllMWNsUzVyWW10TzVabUtNZEUxRGpabzBOOEZtVHErdVFDZFMrbDVxcEVPT2VNNDZEWm5hUVBKL2IxT1hhV3FoUjV3QUdDZHhybmxmTXh0dStNRXJ5NGxTNnExTnI0Y0lLb0cxTTE3cWNTMFB1dkYwUnAyVy9KamFKUWxUaXB1TzZkcXpuZ29MYVlkWEcyMFJ0Njlkak9aRG9qbjZ3NkQ2RjlaWmlNMW1SNmZmQ1ZOQUpzZ0xrWWNiSXpQT21oL2w5NjkwRTRYN3BtQXRNSDdralQvRGNQVXg1dUplaG1xbmN4L0ZYMWQxekNibUFvVXplU2xXenVhV3gxK0gwY0NSbzNqUXZWbExJVTM4akltamFqZTkrc3M5RWI4TmQ0T2ZFWHRRNzgiLCJtYWMiOiI5MjhiZGYwZDJhMzNiZGUxZGRhZmY3N2ZjZjY3NzJiYWFhMjAwNzFkOWU1OWUxNDBlOGE2ZjQ5YTM4NDYxNjk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87212516\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1956658421 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956658421\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:38:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im44eXA4c0VOYjhpU20ySU5XQmJlK0E9PSIsInZhbHVlIjoiU2RuWkVmQlc2djZDaUhCZGM4T3N1V1RZQ1JMU3l2TWNDem1lS3A1bGMzK2xreWs4Tk5tY2p4ai9OMWZaK2NKS1JES202MllFeDJYa3FQcXR5ZVJDdldQZ0hBOW5qSWxNWHRSRE0vNWJtclhVWnFvVk1uT1Z2cCtlaVA3ZVMzTjU2c1hrMTJyRk9FcldpTkg3eFEzTjZuM0VObDRoL0VkQ1JJUlY1aHdrSjN4R21VS0RwdHkzWWduRXFRb2hoeTNraWZSYWNGZGFvUUFBOGJVcUJ4eE5VUjUyNVQ3cURzRW9CdFZueG1xZUUrN0xDKzZNTWRvVXlyR3YyMGU1a3RmVE9tNVg3ellhZC9xN2N2cHlKKy9jcEQxVnhYQ3R4a1J6aFNvSGh2b05uUWc1OHFGNXFPSFdoMkNEL0VuckdSWE5zWnkxUUw1cGttTWxJNmJkN3g0QjFEcXNrOE1WRTU1MU1OdGJsdVB2WWN0RUpSTEIvblo3L3crZW5lQTgzMVlnb0tKdDdoRk9XYzBQTnM0NTMrNXNGMnVWdjMwSXBPLzZTejJDM1BQd202QlRrMm56MHI1SVFtMzRtdkh1MzBoUUV2YWl1OUVPTFliV2hua2ZkNm9jZ2F2QzdqR1ovWm5vRjJSRmJwdXFsTlhJMHVFY3F3Skh6dnZvRG95bHRVRW0iLCJtYWMiOiJkMmQxYzExMWYxMGZjNTAxODIyZThiN2Q2ZGEzYjI4MDViYzg3ZGY3ODFlMjZkZTNmMzc1ZDU2MTQ3OTEzNGI0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:38:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Im1nRWxsd0sxUkZjdlVuV05vUGhocnc9PSIsInZhbHVlIjoiL1N1emZuTWRxc280RE81VlErQUVXTjV2TVo3ek9qUzRBUG1TK28rZTJGNGE0aVNFZzd2Y1RMNitsV1hPT3MxNUVFSXRJNlNhSWVnYTBjckxvUTVLL1Ezd1hvMkZMejhRS1pFbjhlRy95ZTFtQStLRmVOb1hKN3lhNXlJRjJIdElLQWx2NXJuV09kbGhDSXU5cHFFQmh0dlNMbTNpUVhHejRMZmRpazI4N1dPR2h6d0tSelVWb1krWjBTV00zWVJIbS96dms2TUZpa0VBaWxlR0FxNXBseTR5NGV4Rkp5OGcrWFdZaERvcWN4SUdTS1pibUNJVXpBTVNXbDZJaVhrTXFpdlZBdmpwTHpHaG04MEFZMmVLS0FoQmdzUnA5R3h6dzlRWnF1NGozdUl3T05WZGtqbm9XVmxjTDluTUE4U2liWmZNN0ZzYW9ORXBJR21qRkNCdDdyWWh3cloybGI2Wi9Gc2pOcHYzTEFmek8yNCtEOUFtSUt2dHQvQi9VMkFlZUZFY0pTQ1B6aXNzeVlvZU0wWlhhd2lMQjdVc0dnL2dsTE1rV2xGc0VMSnhlVUtocUY4azhZWUxjbEkvVUlrQXZWeE9SSk1HNHQvQUxERnRhRUJpQjExc1RUSHY3aVJ4S2FudWdkYVVXUEpLVG5tYXp6OU5QVTFPWEZqWlpGTisiLCJtYWMiOiIwNmYwNzk3MzdjMTgwOTQ2MjE3YzA1NzMxNjkxMzMzY2UwMmI3NDM2NzU0MWRmYTU3MzMzM2IyM2IwYjQwNTM5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:38:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im44eXA4c0VOYjhpU20ySU5XQmJlK0E9PSIsInZhbHVlIjoiU2RuWkVmQlc2djZDaUhCZGM4T3N1V1RZQ1JMU3l2TWNDem1lS3A1bGMzK2xreWs4Tk5tY2p4ai9OMWZaK2NKS1JES202MllFeDJYa3FQcXR5ZVJDdldQZ0hBOW5qSWxNWHRSRE0vNWJtclhVWnFvVk1uT1Z2cCtlaVA3ZVMzTjU2c1hrMTJyRk9FcldpTkg3eFEzTjZuM0VObDRoL0VkQ1JJUlY1aHdrSjN4R21VS0RwdHkzWWduRXFRb2hoeTNraWZSYWNGZGFvUUFBOGJVcUJ4eE5VUjUyNVQ3cURzRW9CdFZueG1xZUUrN0xDKzZNTWRvVXlyR3YyMGU1a3RmVE9tNVg3ellhZC9xN2N2cHlKKy9jcEQxVnhYQ3R4a1J6aFNvSGh2b05uUWc1OHFGNXFPSFdoMkNEL0VuckdSWE5zWnkxUUw1cGttTWxJNmJkN3g0QjFEcXNrOE1WRTU1MU1OdGJsdVB2WWN0RUpSTEIvblo3L3crZW5lQTgzMVlnb0tKdDdoRk9XYzBQTnM0NTMrNXNGMnVWdjMwSXBPLzZTejJDM1BQd202QlRrMm56MHI1SVFtMzRtdkh1MzBoUUV2YWl1OUVPTFliV2hua2ZkNm9jZ2F2QzdqR1ovWm5vRjJSRmJwdXFsTlhJMHVFY3F3Skh6dnZvRG95bHRVRW0iLCJtYWMiOiJkMmQxYzExMWYxMGZjNTAxODIyZThiN2Q2ZGEzYjI4MDViYzg3ZGY3ODFlMjZkZTNmMzc1ZDU2MTQ3OTEzNGI0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:38:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Im1nRWxsd0sxUkZjdlVuV05vUGhocnc9PSIsInZhbHVlIjoiL1N1emZuTWRxc280RE81VlErQUVXTjV2TVo3ek9qUzRBUG1TK28rZTJGNGE0aVNFZzd2Y1RMNitsV1hPT3MxNUVFSXRJNlNhSWVnYTBjckxvUTVLL1Ezd1hvMkZMejhRS1pFbjhlRy95ZTFtQStLRmVOb1hKN3lhNXlJRjJIdElLQWx2NXJuV09kbGhDSXU5cHFFQmh0dlNMbTNpUVhHejRMZmRpazI4N1dPR2h6d0tSelVWb1krWjBTV00zWVJIbS96dms2TUZpa0VBaWxlR0FxNXBseTR5NGV4Rkp5OGcrWFdZaERvcWN4SUdTS1pibUNJVXpBTVNXbDZJaVhrTXFpdlZBdmpwTHpHaG04MEFZMmVLS0FoQmdzUnA5R3h6dzlRWnF1NGozdUl3T05WZGtqbm9XVmxjTDluTUE4U2liWmZNN0ZzYW9ORXBJR21qRkNCdDdyWWh3cloybGI2Wi9Gc2pOcHYzTEFmek8yNCtEOUFtSUt2dHQvQi9VMkFlZUZFY0pTQ1B6aXNzeVlvZU0wWlhhd2lMQjdVc0dnL2dsTE1rV2xGc0VMSnhlVUtocUY4azhZWUxjbEkvVUlrQXZWeE9SSk1HNHQvQUxERnRhRUJpQjExc1RUSHY3aVJ4S2FudWdkYVVXUEpLVG5tYXp6OU5QVTFPWEZqWlpGTisiLCJtYWMiOiIwNmYwNzk3MzdjMTgwOTQ2MjE3YzA1NzMxNjkxMzMzY2UwMmI3NDM2NzU0MWRmYTU3MzMzM2IyM2IwYjQwNTM5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:38:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-523122136 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523122136\", {\"maxDepth\":0})</script>\n"}}