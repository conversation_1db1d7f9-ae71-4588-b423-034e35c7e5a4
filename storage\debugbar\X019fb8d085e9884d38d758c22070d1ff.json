{"__meta": {"id": "X019fb8d085e9884d38d758c22070d1ff", "datetime": "2025-07-31 12:30:06", "utime": **********.228829, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965004.069985, "end": **********.228869, "duration": 2.158884048461914, "duration_str": "2.16s", "measures": [{"label": "Booting", "start": 1753965004.069985, "relative_start": 0, "end": 1753965005.805943, "relative_end": 1753965005.805943, "duration": 1.7359580993652344, "duration_str": "1.74s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753965005.805987, "relative_start": 1.736001968383789, "end": **********.228873, "relative_end": 4.0531158447265625e-06, "duration": 0.4228861331939697, "duration_str": "423ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47358776, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0439, "accumulated_duration_str": "43.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.070069, "duration": 0.00694, "duration_str": "6.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 15.809}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1129742, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 15.809, "width_percent": 3.44}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.12751, "duration": 0.03545, "duration_str": "35.45ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 19.248, "width_percent": 80.752}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-971174260 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-971174260\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1035034181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1035034181\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-866097631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-866097631\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1339182829 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNudG1PdWpENEU3bTBHK05PaVR0T1E9PSIsInZhbHVlIjoiNWEzVnRSak5zVHgxbFNuODk0WCtrRXlvNjB4SjRmbHZwSU53dFNSWE1aNlJzNS81cW9qWEZVRnRRZ0puRFJpU2tZa1ZucWwvUnQrMkp2SGhlTXIyWnpZNmNwS2dmRlRxeFh4ZXZCMWlwd1VnMjhDeWlsYVB3aHRaVFA2eFNkSTgxUUdpbWM1cGthYlNDaStBRDR5dFg2aHZwbWFDRkxGaFZlbDhvK1ZockFsd0RKbU9iZVozY1FUbFMxYXVJSmxGOXNYZWkrTnJxNVFFdUx6N1NaM2pReC95eXpGSGVONEhjbmc5a05PMnhYSVp1QTZlRzVVTmRHMURJdkZsWllWalFjRlVVWWNLc29USnJHcTEycVRwSmIzcGZBM0lXY2JCbjk0dSs3T2lYeHhkdVY3T1Qyd3BHRGJLL1ZpU2s3S3BSZFd4TWM0Q0JidENKYmUyTDFHbURERFRFMHdMelJIQ3dzdWtLNFA2VTRzMTFwZ29Fa28vWDQ3M0xuaVpjVTlKVmpWUXNiZndGQkc1QWx3TkpIOWtjT0pxeUZtR0JlTEp6dmR6My9RbXR4YVVCUFZPbXg4N3IyWjJkL3BBNzRIZDBEU1J1aUZYcXdETVdXbDVRUHpYM2FuSWxKSCt5dUFUTE1XU3dTVnVYNHJjTHFJbmNtSVhsZk9pWlhGQ2JKbzIiLCJtYWMiOiJmNDY0YjViNTZjY2M5NGNiMzQwYzRiMDBiNTEwNDM4MmE2ZGQ1MzE3ZDE5ZWRjYWE4NjU1OTMzYmFlYjFmYmFlIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkhMZ0h4eVdHYWR1UzBlUCtaUDVDV1E9PSIsInZhbHVlIjoib09kc0xDS1BiSmRQUEY1ZkJKaitwMlJqMzYyQk9ZeFBlbTBCQ1NqUU01cFBTQ2lIZHBBYUZ3MHJFTFBGUEczcDJXWWc5aUhXUTN4Y0Rhc0VCaHZtZmZCV3ZYZG4xdWl1YXFqUkYrc0NOV0owZzY1Y29JQjlKMUVHRkhCeEk1WUVwYUR1OUNWRGYxczdJQ1JhUjkxMmpVdXZkSktSc3VrUmFpcnhPbjV2MnFwbGFVdUtNc2VXNVh2TEhqbElsSThKVGhkL0xxWUlQY1Byei8raE4xcDkxaHYxakpDdTdDajRKYVBJR21rSE1raDNMdk9vZk9kVFZrcWNia2hUREI4aW5UTEt4NjRPb2M2Q2xFcDVmL01ZcmJONXFDU1F3OHRZbWNha3lzdDlKWFVaTksxNXg5NWRuWlJZRFhQNlhoRzg1RzlZZmZIMXgvQkFVU2pIQ2t6WExhOHIvQzY0NmxiNmJrSXQvRnRxUzNoaUpMVTg0S3EremJoYklzNDYxblc4R0ZsSjhzY0RSdTZ5N0dCK29jc2dsV2dhaDdwdFNwcDFvNmlEdDEwMWR2czNxRW5EcFNCWGJVRHVUOG1aQ1pTQ1BiOUZ3MjBEdmkwVEE1YWt6dzkyOElrcWFDRkk3RWxUeTdKb29nMlZtb3Q3SlhwdFNqNWJ0ZnlodXUzbXZuSnUiLCJtYWMiOiJkNjg5Y2IyMWQ3OGJiMDQ4YzI2OGU2MmIyNTE0NmY4NzM3ODZhMzdjNzA2NTg3ZWNlMmM2ODc1MmE0YjU3MDdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339182829\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1144346478 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144346478\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1082263829 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:30:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Illrd0IzcURXa2hkVkZYUnpub3RmbHc9PSIsInZhbHVlIjoiMmt3M2ZpYkRNYTk3eUpjRERsZ0lTMkFHdmIvMS9uOC9aRlpWRDFJYzd0RyswUHhTR0N2a1Z5Z2NBbGlYblVYWURBdy9UT29DY3QydHdVaVJiRmIvTGV1cHlzMkhrYUVFc0E3V2NjSy9VSmUzWktHejhxQjRCR29kQ1Z4TUlnWUNueHFXZ2NuTFJYNTh3Y05NVXAvemR0UkVSWFFnck83elBkRzFuTVNtWjFmOXRIeC9ybWNBaWx0ZGxYdHpmc2pvUVY1dkVzZjR2S0VEdS9UWHpuNFdvVFlzY0syMkhxNjlpWGt2ZFlsZWY4OEZsMzFPaU9palFaVCtHcWQ5WXluR3pvczAxa2JWd1VobDZRZXNSR3JYRlZxOWEwQTMzMThsaVdWdnlXU0VrQUV4RDU3VmZja05HRjU5VnNHUmY1M3dXN0hZeU5xUzZ6TzlJNU5kanU1ZGJmRDVBT1NJbUxVTVRLSGN1UXpoTEpvZUdDMzBqOHZObjYwR0tmcFNIRTJtOEdBdW9tRmErd1paZFNsMkFyOTErK2JFUk1JMlRQZFY3ZDNKampHTFFjMXFyVnJmQXVzTnZKa0dOYS9rQWx3KzRLVzZjaGp5SWo2ZkQ5dHRuRlp2MTVDTEVIUDV6cGkzVmtCbzk2Rm9Ba0lNOXlBSEFESjZQU2x1WnJtQWIyWlYiLCJtYWMiOiI3ZTRlOWM3ODJkMjIzYTdhY2I4YzJiOTBkMjEwMzUxYTAxOWRmYmRhYTU1YjJlNjY0NTU1NzY0NTA2ZTI2MDg5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:30:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ik82VjF3KzV6dG5tNjFHVE5sS213V2c9PSIsInZhbHVlIjoiSURaYnBBcHVkWldJUGxVOU45SkhyMGh1cmhwd0VzTW10TlkvWVlSbjY1bEs3b3I2RVlvUkoyYzJnYjVPYTI0U2VyVEcwbXVTRDEyM3dYcU1OcnM5ZXFKYmhxaFlUeGhqdENOcktXcVNleG96WVJhTXhtS05zS014ZFo1TUxpaGpJMUsyaGFQR1VOTS9paDBnV1JpQXVMRTk0dlBPNFI2NkcweFZGOThPdUdHWkROZ2lxRlFiRHp3dTZyTEZZblBORkpxcmp6TnhFcDVRcUExMU42Zzl4RTZOMjZ5RVI2OTgxbUhhTDJRUThCdDBialhDOVI2MW1VYXRLcWJ5V3l5TDlpL1g2YXFIRXRHVDF3REpMSTRBTGoxb2lxMllHMm9rNmVRUmJrdTdwTjNqU0V3Ly9WdWFzT2gxMWRlRllCYUhKRmY4aXF5T0JSWlpUMHUwR3NzNk13TXhuMnRkN2pBUWJ0RFpOT1dVNml1QVBQNDRpVGpNVnFDbkRBY0tzbnB4WW9iZWtuTUtvNG96TGR5VjEyNm5OMTl6U1dpSDlFNmlGVUZKN1lrWGNnaVZGNFJqblY1SCsxUDcvU1dBMjVJVlRZci9iN3ZtVytpZ0xpT0E1Y1VKaFpUNlZLRmtRTWRKd2RoRmRSM2F6RnpQOUhhWkhYamhUbXZ6bUJJdkNmUXIiLCJtYWMiOiI4M2I0MGM5YzAyMjU3ODJjYjQ4NjliMGM5MjE2ZGM0NTVhYTUzMjY4YjVjODQwMWFkZGZiNjZhNmNlYTdjOGYwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:30:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Illrd0IzcURXa2hkVkZYUnpub3RmbHc9PSIsInZhbHVlIjoiMmt3M2ZpYkRNYTk3eUpjRERsZ0lTMkFHdmIvMS9uOC9aRlpWRDFJYzd0RyswUHhTR0N2a1Z5Z2NBbGlYblVYWURBdy9UT29DY3QydHdVaVJiRmIvTGV1cHlzMkhrYUVFc0E3V2NjSy9VSmUzWktHejhxQjRCR29kQ1Z4TUlnWUNueHFXZ2NuTFJYNTh3Y05NVXAvemR0UkVSWFFnck83elBkRzFuTVNtWjFmOXRIeC9ybWNBaWx0ZGxYdHpmc2pvUVY1dkVzZjR2S0VEdS9UWHpuNFdvVFlzY0syMkhxNjlpWGt2ZFlsZWY4OEZsMzFPaU9palFaVCtHcWQ5WXluR3pvczAxa2JWd1VobDZRZXNSR3JYRlZxOWEwQTMzMThsaVdWdnlXU0VrQUV4RDU3VmZja05HRjU5VnNHUmY1M3dXN0hZeU5xUzZ6TzlJNU5kanU1ZGJmRDVBT1NJbUxVTVRLSGN1UXpoTEpvZUdDMzBqOHZObjYwR0tmcFNIRTJtOEdBdW9tRmErd1paZFNsMkFyOTErK2JFUk1JMlRQZFY3ZDNKampHTFFjMXFyVnJmQXVzTnZKa0dOYS9rQWx3KzRLVzZjaGp5SWo2ZkQ5dHRuRlp2MTVDTEVIUDV6cGkzVmtCbzk2Rm9Ba0lNOXlBSEFESjZQU2x1WnJtQWIyWlYiLCJtYWMiOiI3ZTRlOWM3ODJkMjIzYTdhY2I4YzJiOTBkMjEwMzUxYTAxOWRmYmRhYTU1YjJlNjY0NTU1NzY0NTA2ZTI2MDg5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:30:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ik82VjF3KzV6dG5tNjFHVE5sS213V2c9PSIsInZhbHVlIjoiSURaYnBBcHVkWldJUGxVOU45SkhyMGh1cmhwd0VzTW10TlkvWVlSbjY1bEs3b3I2RVlvUkoyYzJnYjVPYTI0U2VyVEcwbXVTRDEyM3dYcU1OcnM5ZXFKYmhxaFlUeGhqdENOcktXcVNleG96WVJhTXhtS05zS014ZFo1TUxpaGpJMUsyaGFQR1VOTS9paDBnV1JpQXVMRTk0dlBPNFI2NkcweFZGOThPdUdHWkROZ2lxRlFiRHp3dTZyTEZZblBORkpxcmp6TnhFcDVRcUExMU42Zzl4RTZOMjZ5RVI2OTgxbUhhTDJRUThCdDBialhDOVI2MW1VYXRLcWJ5V3l5TDlpL1g2YXFIRXRHVDF3REpMSTRBTGoxb2lxMllHMm9rNmVRUmJrdTdwTjNqU0V3Ly9WdWFzT2gxMWRlRllCYUhKRmY4aXF5T0JSWlpUMHUwR3NzNk13TXhuMnRkN2pBUWJ0RFpOT1dVNml1QVBQNDRpVGpNVnFDbkRBY0tzbnB4WW9iZWtuTUtvNG96TGR5VjEyNm5OMTl6U1dpSDlFNmlGVUZKN1lrWGNnaVZGNFJqblY1SCsxUDcvU1dBMjVJVlRZci9iN3ZtVytpZ0xpT0E1Y1VKaFpUNlZLRmtRTWRKd2RoRmRSM2F6RnpQOUhhWkhYamhUbXZ6bUJJdkNmUXIiLCJtYWMiOiI4M2I0MGM5YzAyMjU3ODJjYjQ4NjliMGM5MjE2ZGM0NTVhYTUzMjY4YjVjODQwMWFkZGZiNjZhNmNlYTdjOGYwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:30:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082263829\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1173863339 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173863339\", {\"maxDepth\":0})</script>\n"}}