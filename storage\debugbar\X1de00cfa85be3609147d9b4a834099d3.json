{"__meta": {"id": "X1de00cfa85be3609147d9b4a834099d3", "datetime": "2025-07-31 12:33:08", "utime": **********.593934, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965186.612682, "end": **********.593976, "duration": 1.9812939167022705, "duration_str": "1.98s", "measures": [{"label": "Booting", "start": 1753965186.612682, "relative_start": 0, "end": **********.360946, "relative_end": **********.360946, "duration": 1.7482638359069824, "duration_str": "1.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.360966, "relative_start": 1.748283863067627, "end": **********.59398, "relative_end": 4.0531158447265625e-06, "duration": 0.23301410675048828, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936848, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1532\" onclick=\"\">app/Http/Controllers/FinanceController.php:1532-1599</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.012400000000000001, "accumulated_duration_str": "12.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4675052, "duration": 0.00607, "duration_str": "6.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 48.952}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5401402, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 48.952, "width_percent": 15.323}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1548}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.553245, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1548", "source": "app/Http/Controllers/FinanceController.php:1548", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1548", "ajax": false, "filename": "FinanceController.php", "line": "1548"}, "connection": "radhe_same", "start_percent": 64.274, "width_percent": 18.065}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1572}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5653849, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1572", "source": "app/Http/Controllers/FinanceController.php:1572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1572", "ajax": false, "filename": "FinanceController.php", "line": "1572"}, "connection": "radhe_same", "start_percent": 82.339, "width_percent": 17.661}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1246188511 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1246188511\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1987399317 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987399317\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1419143392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1419143392\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-329454502 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjFndG42dmVmK2ZRRlZjbVI5WlJ4ZUE9PSIsInZhbHVlIjoiRW9BcXVxdllHbXZRcFQ5ZTgyNWIvdnpFMlEwTkdubG5yOXpDUWhtQXJtbGNjNTRleWl4R0NyWjVqb3oxcTA2VzI3cllJMkRSZzVwRnlPczExcXpidGV1bmhiekNKWTFZeDdWTzlUYWVPRytYVlM1d0kyODFocnhQNU9vVjlDM3dyMTRSRkdkcTloTHZ6NEJjS0pkbDlTY1lnb0V5MWRCb0ZSaWJUYlN0MzU4L2VEMW1lYld4NHlERDhMTUptbTZ5ZG10RU5KcVQ5TU52ekRlSHhWOWJwaXQwd3hRSFBoRWN6bUdqUkJobXBRQ1hxR2ZOQkt1QzhaV0ZPTExRNkI3REtLYkVLcFp1TklVYWJCdTFQUmNHV2Y0MFlsZjNhS1Ixdi83WkZmaWZSdkNXWDhhb0hBQ28yOVc3OE9mcFBjanY2aVFHZ05zZDZ0QXhXN1pOcWdjSFdjSzRlbjRBblNnTnNPY1FmUFZNNWdUcUlVZW1Fd1UyQ2tkZWorVU9IclJwd3l6RnRhaGhyYXdCbWlmanE1L2JxS0U0NVovKzQ5NGNyUStFd1J2OUFqWkx1SWZSek01dndUanpmNVlmYVpuWit2RWY2Z0JxQ1FjYXBYeDZ1dm1kR3piMVBPYnhQQVhpZG9pbXBXenhQWmdHOVFmK24zdlJyTE13eThMaGtKOEUiLCJtYWMiOiI2NmM2MTIxODJkOWRjNDQ2MjgzOGQzYWQxNzQ2ODhlMDk5ZDE3ZTgzYTIxYjQ5MDFkNTdmNTM5MWVlNmI0ZTlhIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IjFxQXJTaTVCazJ1a2E3dlI3NWhCRkE9PSIsInZhbHVlIjoiTlVsRlhidUpHVzRLRzFzQUdCYU12K2VRdDM2M3pMM3ZweGlzbDdtdlFJSmJ6aEUva1ZmeWZPWDU1TU9vYWVXTHhKSXh2b3gyek1IbkJSRnlxVGliSkVoTURYbWFuVm1WUzNISkJIdGR0WEcrbVBEdjBSbzJzNGl4MUZQL2dPQVVqRkQrQ0p6Z3JhVE9jeTF0M1phVFVkMEhpUzAwZ1VVWnV5NXhUVlVTcWNiQlFKSXpGQVVkY3NGeFI5YlhmSStoakpjNXA3K2dwZUF2eXdQaDVQYWg4NExoNGNNNitVc0RhWThqNDk2NUhaclczWXVaYlVZOGxzYU01ZE9idzB4Slk0MkE1bWJMdktoRzJTNFNzZmpta09pTWs2MEtzM1pxYWVjNEFnQ3d5dUZFOFRwQkMxOTJHbHlsM29HWkt0Y1k0dlJ4VThvc2FuN0J5MXB4dmJqTWRiQS9ueTJzRHRJdHFkbjdDVG9Za2NMVXZwRzlFaGJTOUpPSGxJTUY1eW1DQ1F0cVFyUk44MXVrdTl5cm43OEg4YU96RHZkTzhsd1VkeDR3QUF0MzRLaVNQWlJ4L0lVYnFKVjhzcjZlTno0WGQ5ZXFJdnpxenpkRUtDcml0QjU1cC9mZTRDQzlxSENVVjc0N0lORGtRNEJjVVI4emJNNG5ibXVXZXprMTY1a28iLCJtYWMiOiIxOTIyMjJlOGY2ZDRiODgxMGExMjQxNDlkNGI5MDdmZTIxNDliMDU4ZjAxODRkNjUzMTZlMzc0Yjc0ZGMwN2YxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-329454502\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-667712355 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667712355\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-835232846 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:33:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh6YkZFTnJYYnYxdEY0dDlpVlJEOWc9PSIsInZhbHVlIjoiMkJwYXdPZkc1ODZaeG9GMEN2bmt1QXlOL1pWVk8rSHNnSG9NZkdGbDRRNWRNbncwUWtSYzYycFh2YTZDWTVzaElhMWcxeUJlaDVwNi9XSlAwQnhaOFVOaTc3WEM3THlGLzVWQ1pVRUZzZitadFlBVzF2RUhwSE5RZkkrRkFBL2o3Q3dFaTA3KzNhcWZlaXYrMDZudTdUQTdROW9QYmpTaWU3UVRXUjU3REV2bGdOZ0VvY1phelk2ZWQyeG1zV1JMTDkydTVJSlprM05TNGQ4U0hwb29qRDF0Y2hhTTFoYW1hZFFrUnIxQnZjWXRWNVFtTld6Y0tFTlZEY0h0dkhHLzVCU2kyeGpqVkExY3FLbTZRdlo3TjNHaTJwemc1VW1EK3NCQWltaXljVGgrMnBrMHl4aWZrQXA1aHBOSHhZdldzNWZWUVVPK3hvS09DNEtnUEF3NTQweFNnL0JDRWZwaFl2QTZOM09pU1c5SVpSYzZLU0xzN2JvaFpDZy9JSVpwR3FwbmVaY0ZtMUdGakpLNDk5bnJYVFgxWVc2aTNscE15aHozQ3VZTVRtdnNjUWRaVSs3WjBQVjl4WXhXNi8wTFo4OC9mOGtRemRvNnFHdG1qL3dJLzFCdWdNbndjR0l1OEQ4ekpITHcxYUlVVWZvbWV0a0NkZkhKODBtanBmWDgiLCJtYWMiOiJiODUzZWFkNTg0MWZiNTgxZTI3YzMyZTdkNjgwZDllN2VmY2I3Mjc3MzgwYmZlN2JkODk4NDNiYjdiMjNjN2Y0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:33:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjNRb2ViUlVIZkNzcml0TGRVUEZpT3c9PSIsInZhbHVlIjoiK1hmY1NET3NaaHpXRHFWcjFRbGh2MHp4VmVyelhqN0doaWZLbmpTRlRsdzcwKzVZTTFLU1dGc0hobEc5TXBIMVh3ZmVlMHlrNXFmVDA1V1h4YnJjK0szMVFaRGRTNUZ6Ylk1a3p2cFR1YUZaeFZmS2VrSHdEYU41RU1yT08yd1lMUGtFc1dkNGY5d2RkZk42b2dKdGhlN1dEMDJ6eGM0TDBzbHlYM0E2OGlwZlY5akk0M2ZUanc2aXVxVUhPSUJPNTdhYjNEdHp2dzFDaVduUjdDcFJ0TCtBSlpTcUJ2djdhYnBmVnc5ZytzaFZpRHV1Nnc1L3c5U2hSTXhsMlRIcTNtRG9LcHFNSjR0VitSRC9Wa1h1SitsUmlzWllRbFR5aVZiSlQwajlzS09GOTdNSlI0b0JyWTN6Sm9rSXN5dDlmVzVaM2NLNndXVWlaYnJ6VWkvWXIyR0JxUk5kQkMzTmhoM0Zib29zUDJKU3BOMmxUeENyUjRORGFTK2tic3F6SS8rd0tPeEJ6dFREc2xEYVpjL29yNEJoeit5Zklpa3hkN3RhenRCNk9jbWJnYnVuZlYyd2ZpNXZDRDY2SzNOMnBHLytQbTgzZUU1Uis2cS9Fb1Y0QjRISnBlQXBza1RxK2lUWEF5cEN0NjRCejhONlpGZ0VoNUhHYXN0QkE0RTYiLCJtYWMiOiIzNDUyNDRiNTVmYTBmYWIwZDhhOWNhM2Y1Mzc4ZmMyNWJhYzQ4Njk2MGE3ZTVkNWNkNWJjYThkNGExYTEyOWJiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:33:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh6YkZFTnJYYnYxdEY0dDlpVlJEOWc9PSIsInZhbHVlIjoiMkJwYXdPZkc1ODZaeG9GMEN2bmt1QXlOL1pWVk8rSHNnSG9NZkdGbDRRNWRNbncwUWtSYzYycFh2YTZDWTVzaElhMWcxeUJlaDVwNi9XSlAwQnhaOFVOaTc3WEM3THlGLzVWQ1pVRUZzZitadFlBVzF2RUhwSE5RZkkrRkFBL2o3Q3dFaTA3KzNhcWZlaXYrMDZudTdUQTdROW9QYmpTaWU3UVRXUjU3REV2bGdOZ0VvY1phelk2ZWQyeG1zV1JMTDkydTVJSlprM05TNGQ4U0hwb29qRDF0Y2hhTTFoYW1hZFFrUnIxQnZjWXRWNVFtTld6Y0tFTlZEY0h0dkhHLzVCU2kyeGpqVkExY3FLbTZRdlo3TjNHaTJwemc1VW1EK3NCQWltaXljVGgrMnBrMHl4aWZrQXA1aHBOSHhZdldzNWZWUVVPK3hvS09DNEtnUEF3NTQweFNnL0JDRWZwaFl2QTZOM09pU1c5SVpSYzZLU0xzN2JvaFpDZy9JSVpwR3FwbmVaY0ZtMUdGakpLNDk5bnJYVFgxWVc2aTNscE15aHozQ3VZTVRtdnNjUWRaVSs3WjBQVjl4WXhXNi8wTFo4OC9mOGtRemRvNnFHdG1qL3dJLzFCdWdNbndjR0l1OEQ4ekpITHcxYUlVVWZvbWV0a0NkZkhKODBtanBmWDgiLCJtYWMiOiJiODUzZWFkNTg0MWZiNTgxZTI3YzMyZTdkNjgwZDllN2VmY2I3Mjc3MzgwYmZlN2JkODk4NDNiYjdiMjNjN2Y0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:33:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjNRb2ViUlVIZkNzcml0TGRVUEZpT3c9PSIsInZhbHVlIjoiK1hmY1NET3NaaHpXRHFWcjFRbGh2MHp4VmVyelhqN0doaWZLbmpTRlRsdzcwKzVZTTFLU1dGc0hobEc5TXBIMVh3ZmVlMHlrNXFmVDA1V1h4YnJjK0szMVFaRGRTNUZ6Ylk1a3p2cFR1YUZaeFZmS2VrSHdEYU41RU1yT08yd1lMUGtFc1dkNGY5d2RkZk42b2dKdGhlN1dEMDJ6eGM0TDBzbHlYM0E2OGlwZlY5akk0M2ZUanc2aXVxVUhPSUJPNTdhYjNEdHp2dzFDaVduUjdDcFJ0TCtBSlpTcUJ2djdhYnBmVnc5ZytzaFZpRHV1Nnc1L3c5U2hSTXhsMlRIcTNtRG9LcHFNSjR0VitSRC9Wa1h1SitsUmlzWllRbFR5aVZiSlQwajlzS09GOTdNSlI0b0JyWTN6Sm9rSXN5dDlmVzVaM2NLNndXVWlaYnJ6VWkvWXIyR0JxUk5kQkMzTmhoM0Zib29zUDJKU3BOMmxUeENyUjRORGFTK2tic3F6SS8rd0tPeEJ6dFREc2xEYVpjL29yNEJoeit5Zklpa3hkN3RhenRCNk9jbWJnYnVuZlYyd2ZpNXZDRDY2SzNOMnBHLytQbTgzZUU1Uis2cS9Fb1Y0QjRISnBlQXBza1RxK2lUWEF5cEN0NjRCejhONlpGZ0VoNUhHYXN0QkE0RTYiLCJtYWMiOiIzNDUyNDRiNTVmYTBmYWIwZDhhOWNhM2Y1Mzc4ZmMyNWJhYzQ4Njk2MGE3ZTVkNWNkNWJjYThkNGExYTEyOWJiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:33:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835232846\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1398239027 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398239027\", {\"maxDepth\":0})</script>\n"}}