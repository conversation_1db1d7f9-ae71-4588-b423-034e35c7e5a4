{"__meta": {"id": "Xb52c904bbea9745ffa6f95e156532178", "datetime": "2025-07-31 12:32:23", "utime": **********.172223, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965140.874886, "end": **********.172291, "duration": 2.2974050045013428, "duration_str": "2.3s", "measures": [{"label": "Booting", "start": 1753965140.874886, "relative_start": 0, "end": 1753965142.946802, "relative_end": 1753965142.946802, "duration": 2.071915864944458, "duration_str": "2.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753965142.946824, "relative_start": 2.****************, "end": **********.172296, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3060\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1892 to 1898\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1892\" onclick=\"\">routes/web.php:1892-1898</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00787, "accumulated_duration_str": "7.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.102439, "duration": 0.00787, "duration_str": "7.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-445872222 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-445872222\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1292957459 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1292957459\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1302354613 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1302354613\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-416147239 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImIyRWZKeDRvYlhESTVJbkQ0amRCekE9PSIsInZhbHVlIjoiM3dXVUtzWkZ2SXlUVVJzV2xUWHR3aDFyRzkzRStzZzZ1UWdabFljSldjb1ZIMHd3dFVCLzFxVlNXYkRZcVdMbWQrb0NudFNJL09mWVdaV1RGNnJwaE9QdDN3M21ZMm55ejlYV2tlMGppeUJZaWxCVUFtVHdDcmJhdUtkQkdxdmlMV29Ua0xkMmRvTFZzR3dvY1pYaFE1dCtiQUJNTENsUnd4b0RBMVpwUVZQUEZ5N3pGMXZJcXpmOHZoNnZDZU8zM0pubDM2NEtGTWxkMUFzYVQ3L1hwbXFDZEZGbmRMN0swWWJXRzE5c1lwOVdWSzYrUmNoMUY1NG0xL3JyOCs0MkpRdFZzR2lTemlxTkdoMExXWUdZNlFUTGoydVJiK3U1OThFMm51aUNDRktveTZ1ejdZT1pFL0hkQ01yQVVWOVNXODdJRGdES1JvOUdyVzJ2OFV3cGE4dEpDMklobVZRNlY2ZkhnZUt2eTM0bUU4YXJjdnZQWG1wNTNLRUFNUHFia0NMRHEzeExCTkUvckc3M09NYkVmRS9LVE9EMkU5WFJpOUZNMHZ2QzNlQ3h1UnBkTG0vbGZ5V25TZGRVd3gwOTl1YXdvOEhZMGw3RVE2dnRLSlIyOVlJeGZCM2pEenV3dmJNZiswU2xMeTVxUk1YdkNLTzl5c29MdUdkWkJ1b1QiLCJtYWMiOiIzODJlMmQyYTMwZDUxYjEzMGQzNWVmZTI2NjFiZjcwYzQ0NTUzOWUyZjE3MTIxZjI2NjI3MWNiOGZiZGJiNmZhIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImJuejhtSllKbVk0TUR5ZlFCNUd5NUE9PSIsInZhbHVlIjoid1hRVlRRbFU4alZVMWlsbXVDTENhb2dGdk03M0JOSGpGNlRtZ0ExK2NjUWc1ajhyR3pLN29tRmkvNXVpNUM1UnVxVmlPbUEwV0JaMmlxWTJhaUtBVmMwOHcrM0JIeklkV0ptWWxwMk1hUW84KzhmV0s5enE3b0RSRmRNeDl6WGpzeEw3R0hrT29Cc1krSlJhaVAxNFRZSkw5SjNjY3hrMnp2RE9BS2hZbXJEM3I3QkpFQnFFNDd2TmtSWXBrcVMrWU1UcE9nZlB4eFBLbmRjWkVlR2YvNlFwa29QbVdHbEJENnhFVnhuV2NRb1d1Qmc0WmVET25ic05lSk5QSS9NZkxSYno4K3haMnYwVkVERytnTjh4bFhnL3lvelBKU2NuVUNFOEVHZm90QzQwekNJUVN3dlpGU1l2cGxaYkdMUjBsN0FzcG5XVVB5TU1MRUdpUkVmNDllcXBrUTY5QUZHeWV1YWxsbFRXSEl5cy90VzczMGwrT3NOMU1ObDZOdUlmSW1hcVVodmpDZ2REUk9HcWVFT1JJSXloaE9xa3NLYmpvbmFTQVZtK2NXQ0VYNkNBdGpJMFBFZEpZbXpoMzdUSEc4ZmR5UWpUN2dxUUk4U1RYYjVzWFdzY0V3dEl2Ym4xYzlxZUFjRG0zNTIxT2VpTWdmUHB3d1A1NC9odFhTSmciLCJtYWMiOiIwNTI1MGM4NTBmNWFkZTM4NDBkOWU3YjQ2NmM2MTBmNzEwZTg2OWUwZTU3MGU5YTNjMDQyMmJlYzgxNDVmNTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-modified-since</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-416147239\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1993369880 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:32:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjQweVU0TURjVjgxSHBrcnR1K2hVaHc9PSIsInZhbHVlIjoiTkFvWkE3VGNPeUFHVVVzUW02UVJrVGlNcHlLTWlnRU0vMVZHaE5pWG14U0NEcTROWnJKL21uMFRvQmljSHl0UjJlL01URS9uN2VKWElXQmlDQVlOaGFsSEUwckhsS3ZWeEtKell2UGtyZVM1elpKWmFDWDVmcEVwMm1kSndzaXo3UnAvbjVrazZtb2tadHc5NjJneWdiTE1ReUNaOGs4WkUrS01OQU5kSWlSMDBOT0NFNTA4VDdhWDJhTG1DNENIeERseEFKeGhhalpLTHlZbUpMdTlyeEVGL2c3ZEJuYkt0NG15c2d0Nm5XOTQwUjIwakk3alU3aXF1L1E2TUVKUVhLYmhrbWI0WVJqZytNa2xoSGhVZ2NQTHZlcHVxdWpyQWRGUG5XYWxZSGxpbnRqUGhKY01BY1NHd3hHeW1SM0hlbjJ6NzR0azNEb0kwTEF1OWJoV056WEcrdUlGNXFJeGZLWDRYVWNYTXhHdCt6OTF2eHJKZjE3TE0rb1ZkQkttWEVvWWtkMkZENUFmV29PaS9na2VXNGFMTWw2WElsTjFmL2JqcGNVKyswTjNqNHpGdWE5cmhOaFVQYlRCUForUzFnOTJERXhWczZqTVhSTFBoa2pIR0VjUnpDd09nclUyMndYemhyVVhML3ZDQ1JGQStGVjZ3Wm9CSzcvY3JRNTAiLCJtYWMiOiI4NTkyMmEyZWQyZGIyNTI2M2U2YTIwODhmYjI3NGI5ZDViNjEwYTdiM2JjYWRmYTY5YjJiZjUzZWYwYTM4OTA5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:32:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImFtaUlxY2RybDBYZksrelZpVzZwQXc9PSIsInZhbHVlIjoiUHVsVGdXNjVWdjRzTm94RjJSVG5hQ0lqNDVSajVLTkVNZTY3NFpOVzlMdjB3NEhvYVFReU92d2pTR0ZWYzVvTWl5Ym5aTHZyajYxd1RJdm5YVm4xR1hoT3lrQWpBbEw3Ri9nR2xkQkpOcjg2M29pcWJoVUI5MzEzN3JnNjQ5ZDhuOUJHWUUrT3hBT3VSLzFpV0JaUktwM3YrdFpvQStDOWQwT3VKcjRTZHM0ZkJhV1c3SGJiTU1NZjRRTkZ0aFlBbktrNnR6YTlXY0ZHRDVvamNpbXcvTWdnY3RiSlBNcmZBNWY5cVhuRHVhSmVZczZyZHlzRm1tTWNLT01qR3pQdmxPNndLUG1TT0E0dEx2aTl0c2Z0TkNlRE9pbXFJcnI1dmIyWWhGWXBWVUhxTk13YVdGajJaR2RSZ2FMK0FNSlRDY2p4NXlwVFE1c2Z4U3d4RlpHbTNaK0FYMzdsdklJRUl1eFVrbHowZnBIRWpQMDl3ME15ZVYvMWpVRlZrRzJMOGxJK1p0dmdpS2tWN3JqR0dXUy9vdk9BQ3phZXJTdG9mVzJiQ1Rqbmg4c2JBNEFoNGFEWVhvcGIyRXJuaTZqelA1cmYxYmVGNmpLNHRua3F6b3RBVktpbFliZDdvNEtTakd4WHI3ZzR5Q0txeFVYbk1IenhhZHpRbTNqSk9Da2siLCJtYWMiOiJlMTljZGQ5YTQ2NTM4NzhlNWY0NzVhZmViYTU3ZjM4OTkyZDM5YzBkMTcwZTAzNTdlYjgyYjQ2Mzk4OTEzNjEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:32:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjQweVU0TURjVjgxSHBrcnR1K2hVaHc9PSIsInZhbHVlIjoiTkFvWkE3VGNPeUFHVVVzUW02UVJrVGlNcHlLTWlnRU0vMVZHaE5pWG14U0NEcTROWnJKL21uMFRvQmljSHl0UjJlL01URS9uN2VKWElXQmlDQVlOaGFsSEUwckhsS3ZWeEtKell2UGtyZVM1elpKWmFDWDVmcEVwMm1kSndzaXo3UnAvbjVrazZtb2tadHc5NjJneWdiTE1ReUNaOGs4WkUrS01OQU5kSWlSMDBOT0NFNTA4VDdhWDJhTG1DNENIeERseEFKeGhhalpLTHlZbUpMdTlyeEVGL2c3ZEJuYkt0NG15c2d0Nm5XOTQwUjIwakk3alU3aXF1L1E2TUVKUVhLYmhrbWI0WVJqZytNa2xoSGhVZ2NQTHZlcHVxdWpyQWRGUG5XYWxZSGxpbnRqUGhKY01BY1NHd3hHeW1SM0hlbjJ6NzR0azNEb0kwTEF1OWJoV056WEcrdUlGNXFJeGZLWDRYVWNYTXhHdCt6OTF2eHJKZjE3TE0rb1ZkQkttWEVvWWtkMkZENUFmV29PaS9na2VXNGFMTWw2WElsTjFmL2JqcGNVKyswTjNqNHpGdWE5cmhOaFVQYlRCUForUzFnOTJERXhWczZqTVhSTFBoa2pIR0VjUnpDd09nclUyMndYemhyVVhML3ZDQ1JGQStGVjZ3Wm9CSzcvY3JRNTAiLCJtYWMiOiI4NTkyMmEyZWQyZGIyNTI2M2U2YTIwODhmYjI3NGI5ZDViNjEwYTdiM2JjYWRmYTY5YjJiZjUzZWYwYTM4OTA5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:32:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImFtaUlxY2RybDBYZksrelZpVzZwQXc9PSIsInZhbHVlIjoiUHVsVGdXNjVWdjRzTm94RjJSVG5hQ0lqNDVSajVLTkVNZTY3NFpOVzlMdjB3NEhvYVFReU92d2pTR0ZWYzVvTWl5Ym5aTHZyajYxd1RJdm5YVm4xR1hoT3lrQWpBbEw3Ri9nR2xkQkpOcjg2M29pcWJoVUI5MzEzN3JnNjQ5ZDhuOUJHWUUrT3hBT3VSLzFpV0JaUktwM3YrdFpvQStDOWQwT3VKcjRTZHM0ZkJhV1c3SGJiTU1NZjRRTkZ0aFlBbktrNnR6YTlXY0ZHRDVvamNpbXcvTWdnY3RiSlBNcmZBNWY5cVhuRHVhSmVZczZyZHlzRm1tTWNLT01qR3pQdmxPNndLUG1TT0E0dEx2aTl0c2Z0TkNlRE9pbXFJcnI1dmIyWWhGWXBWVUhxTk13YVdGajJaR2RSZ2FMK0FNSlRDY2p4NXlwVFE1c2Z4U3d4RlpHbTNaK0FYMzdsdklJRUl1eFVrbHowZnBIRWpQMDl3ME15ZVYvMWpVRlZrRzJMOGxJK1p0dmdpS2tWN3JqR0dXUy9vdk9BQ3phZXJTdG9mVzJiQ1Rqbmg4c2JBNEFoNGFEWVhvcGIyRXJuaTZqelA1cmYxYmVGNmpLNHRua3F6b3RBVktpbFliZDdvNEtTakd4WHI3ZzR5Q0txeFVYbk1IenhhZHpRbTNqSk9Da2siLCJtYWMiOiJlMTljZGQ5YTQ2NTM4NzhlNWY0NzVhZmViYTU3ZjM4OTkyZDM5YzBkMTcwZTAzNTdlYjgyYjQ2Mzk4OTEzNjEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:32:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993369880\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1555261097 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555261097\", {\"maxDepth\":0})</script>\n"}}