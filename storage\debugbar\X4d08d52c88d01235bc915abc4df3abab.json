{"__meta": {"id": "X4d08d52c88d01235bc915abc4df3abab", "datetime": "2025-07-31 12:16:24", "utime": **********.50518, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964183.209059, "end": **********.505236, "duration": 1.2961769104003906, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1753964183.209059, "relative_start": 0, "end": **********.321705, "relative_end": **********.321705, "duration": 1.1126461029052734, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.321723, "relative_start": 1.112663984298706, "end": **********.505241, "relative_end": 5.0067901611328125e-06, "duration": 0.1835179328918457, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47344912, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01952, "accumulated_duration_str": "19.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.401259, "duration": 0.016059999999999998, "duration_str": "16.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 82.275}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.444322, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 82.275, "width_percent": 9.887}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.459126, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 92.162, "width_percent": 7.838}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1240736270 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1240736270\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-467604918 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-467604918\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-628750939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-628750939\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-771143714 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw0V0lXWmRKQU9iMjBrRVFHcXdjQnc9PSIsInZhbHVlIjoiZFVDV3o2WVlqWXQwblNTYnFvaFdUWVpaV3pzbkJ2Z0NnWUdsOVFUdndLMGZ1WjJuUzd3c3Y0K1B6U2UzanRBdkdxQW4wcmEwZVU5TFFJZjdvTkpNSFJpUU1CVEUxMFg0bnkzSDlKV2JSVSs2cENrekNLbGtQdWJMU3FlT2tXZzBCV29wWCsxdnFEUENpM3dhb2ZXYkRKQ3hwV1FINHhzcktOWDUwdzdaRDY3TWZJWG81RWxJYXdYdXl6NkNJT2lwNVFKelgrYm5RVU1BYkR4T3pZeDROeWlyWlg0NlhUbWtGTWduRC9XTHMwc1JZclBpbDliTTZkbUF0MU12V1IxUXpTNjNhUXI2bk5HTHZ0REFWRXBBZ2hHS1p5cUp3SkV5UkxRZkpvRE9PT1N3VVpUZUYxMmpPMjB2enMwMGJtTDJlSkRCMFoxYWNNWDY4RHFxODVqT0t2cndLSk1mWkRpRVcvSWVkU0xHYmhNcnhoRnY0TWo4QUNkd0xScjFUbmE5RkE1MDJzTWlXbDRXeXNvU1A0dEZ6Ui8yeXg0cHgyN3c3UnhqeFU0YUQwYVI5dkRSNmJ6MlYxVWF3UFJRMHB4SDFYS2hGaWdmdmxuc3B3d0ZnSlZzRUNKYWlXMm93NXJUdHczOEs5WGc1VmpFREdyc3V6aTN0YTZzbncxUHgxb3ciLCJtYWMiOiJiYmRlNzA2YjI1Nzc3MTllZmE3ODM5MWNlZmU2ZjQwNjk4ZTA0MjUxMTk1MjJjYTcyNjVkOGVkY2U2M2FjYjc0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlRYMU9IOGpURi9xR2w0SWNGdTk2QlE9PSIsInZhbHVlIjoiRDJwQ2x3QUwwbFNaSzFqYkYxWSs5Y0YwREh4OHNwSDlXQWNYTmVKK29MSVlqN0lEcHh6S05weXkwenRNNmZYeEprK0VTMHpFMlFWNDhOeG5VZm9uNlRHZW5mR3lxNTlsK1NZTDhBRXduS0llK0RVZUFFWnhhaVdWTWV5L1NQdzZ5YUFaS1pCVGRQSUxHMHBlSVc3TWpwOWZuanBFdHFsVEpCWGNzQmc3QlEyRVJneENYM0pONUMzS0svUmM4MjFCN0RURENvVDB0UlFkL2VCMnhmL3RiU1NpMEtZMnowZmZGbCtDZ0FCdE1adE5UNXhrNzBMVHFUdFB4STg5NTN6NmdaN0VMUFNkODRabm5zRmM4aVFXUk92MDFsY2IweWxmRExTL1ZpN1lvNm1RZFJpOXplYld3NlNBZFgrU0ZFcHhoVGVEVFE5OVFUZVFOVVNuUmoxM280T3o0YXQ0SUNoNFZqTDNPMUxJcGErUjhCdHI3d0t5Y0tPamZXdGVISFZvaE4wcWRscUdWN0EyOXZFdDUzSG96eXdRUnZLUU9tcUV3MDh6VFVLc2dqTWZsdUVDdk9QMVZ5ODFQRXQxMHQ5TFRtUFVZbXdiWHdxQnhlK2FBRS9FMG1qYVBxbm1PVkE0eXZnM1VRSThBN3BpRmRHZlEzNjZUc1RKeVFzdnhMcE8iLCJtYWMiOiIwODcxY2RlZmYyMjc5ZTVhOTliMDhlYWNkM2NjYmEyMmM2MzMxODQ0ZWViMzdlMTA5ZTQzOWYwMjM2M2M4NjNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-771143714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1635739440 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635739440\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-249856019 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:16:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InE1THFLS1dQQXh4S3Q2aStBWGxGakE9PSIsInZhbHVlIjoiK3p2eGIvZzdyUm8yNW8rMElIUzg2VUhLb1RKWGtPbXZ4WVRNUVN4YWxURlIwU3NOdXhKazdHaS85ekdDNzM1QllHODZQZGorWXlTQzJEazFxMUplR01rMVBEd2JJNXBodVJ1dzNMcGhGNjE4N0V0RHI2cmFLWERGNUxlempzM0hXRmQ3MTJLTktQL0o3bzgxcWcvTmhYRk9ERFdtWG5nLzE5OU5MNUpCMTFyRjZwWkZuaERGUXA3YVpPbHF3Ui9YM2VPUW5jVnkvK1JDNEJoQlA2NUJidERobXlPc0ZubFdyWGlpVDRRbmVRQU80UEFSMzVGSm5Sa0l6cVBZRXhFMnpybURiTklyaW0zSkNUL2NZaGZKVzZIT1MvK0RHd2pWUFlhVEVZYS83WFVieDB3eGphaW85dkJ1ZVNPNDNYcjJ3S3dHTUJYbUlyQitJQ0FjQS9jNUlBc01nZVRzRitLdUU2cmhCTGp2bWJHM0NmV01RRUJIMmpRdzFPaTlsRUtVZU5jYWlDT3d0dkNaYUl4ZTArbWhuOGY0djhBUWZkUHBSbWlLb256VkZSWTNEWU91UU4rT2VPclIyUEtSazBNZjJnMjArb3puYk55REFRN2lJVmFXczRsckxqcHY3YmNiY2FWNFpiN2hvSWw2bU9HWm4veWFLdlk5cDJaNHkxMGQiLCJtYWMiOiIwZmE4MDYwMTA0ODRlYzNjMzliMmFjMGIxODhjNWVhODBhMTg0MWNlMWUyMDAxYmY5Y2I2MzY1ZjJlNDliNDQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkJWYjR0eitDSjdEdjBwenQ3K3gyQlE9PSIsInZhbHVlIjoiYVRxYkFBYVlKUHFGOTNxMTZFci9GODEwOGR6cERqK2ZqMW1xc0JMRWVyZFRGVS9CZUl5TkFHUXd4NGREcUwxZlBOSCtUQVVtWEUrOXlyZDZCeFdSbTZEbjlzM01OdDhkbWQwTXpYVTc1b09sWVpDWXBZK1diUDNoVllWZGV0eXMzdDU0bzBIZmIvaU1OTFpkZWNwTFVDcHVibzRMelBrREFoU3lOVWs0MVBzN0JEMkhXdlFUeC9TRGtsb0gxektkK0JBZU5GZjUwS1Q4T1d0NFBLZzJhLzhMcXZaRkJYQnBldWk4eEJKTXZyVTM1ZVBiOTF1SzlkOWFxcGVIZUNXbUplRFVhcUg3TTgrcGs2Q2w1cDNIci8yb3MvaDQ0NytnalV5bG9DbGsrNkcyUTZTcjR0OFVDSXhkWlVPU1JxMVM4RUxUczVCY3VrOHlUQXNKcHY1RnJyaStCbjYrWHFWcllpYlBUSUFXaFVxUU1Ib2xrT2IyTHpCd3NCdmQxQkprTlZuTE1WWEZkT2R3OTRMeHFyZW9MNkJub3JRa0pFOFJEejBtcW1KNTdIQmlWd3AvSWFKVFI2N1V2UkNNbTJWV2ZlNER5eFFhOUNHeFZuMkdwL2diVzR4SUJvY2dGRFB3K0lJK2RqQVJBclJ5bmZGbnRDV1JvQnowekhuZ3F4N04iLCJtYWMiOiI3ZWNlZjMxZDJkNGYzNDliODkxOTE5NjU2ZjY0NTg1MTQ1NDc2OTViYjFkOGE2ODZiOGE0ZjM3M2U3NWMyYzZiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InE1THFLS1dQQXh4S3Q2aStBWGxGakE9PSIsInZhbHVlIjoiK3p2eGIvZzdyUm8yNW8rMElIUzg2VUhLb1RKWGtPbXZ4WVRNUVN4YWxURlIwU3NOdXhKazdHaS85ekdDNzM1QllHODZQZGorWXlTQzJEazFxMUplR01rMVBEd2JJNXBodVJ1dzNMcGhGNjE4N0V0RHI2cmFLWERGNUxlempzM0hXRmQ3MTJLTktQL0o3bzgxcWcvTmhYRk9ERFdtWG5nLzE5OU5MNUpCMTFyRjZwWkZuaERGUXA3YVpPbHF3Ui9YM2VPUW5jVnkvK1JDNEJoQlA2NUJidERobXlPc0ZubFdyWGlpVDRRbmVRQU80UEFSMzVGSm5Sa0l6cVBZRXhFMnpybURiTklyaW0zSkNUL2NZaGZKVzZIT1MvK0RHd2pWUFlhVEVZYS83WFVieDB3eGphaW85dkJ1ZVNPNDNYcjJ3S3dHTUJYbUlyQitJQ0FjQS9jNUlBc01nZVRzRitLdUU2cmhCTGp2bWJHM0NmV01RRUJIMmpRdzFPaTlsRUtVZU5jYWlDT3d0dkNaYUl4ZTArbWhuOGY0djhBUWZkUHBSbWlLb256VkZSWTNEWU91UU4rT2VPclIyUEtSazBNZjJnMjArb3puYk55REFRN2lJVmFXczRsckxqcHY3YmNiY2FWNFpiN2hvSWw2bU9HWm4veWFLdlk5cDJaNHkxMGQiLCJtYWMiOiIwZmE4MDYwMTA0ODRlYzNjMzliMmFjMGIxODhjNWVhODBhMTg0MWNlMWUyMDAxYmY5Y2I2MzY1ZjJlNDliNDQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkJWYjR0eitDSjdEdjBwenQ3K3gyQlE9PSIsInZhbHVlIjoiYVRxYkFBYVlKUHFGOTNxMTZFci9GODEwOGR6cERqK2ZqMW1xc0JMRWVyZFRGVS9CZUl5TkFHUXd4NGREcUwxZlBOSCtUQVVtWEUrOXlyZDZCeFdSbTZEbjlzM01OdDhkbWQwTXpYVTc1b09sWVpDWXBZK1diUDNoVllWZGV0eXMzdDU0bzBIZmIvaU1OTFpkZWNwTFVDcHVibzRMelBrREFoU3lOVWs0MVBzN0JEMkhXdlFUeC9TRGtsb0gxektkK0JBZU5GZjUwS1Q4T1d0NFBLZzJhLzhMcXZaRkJYQnBldWk4eEJKTXZyVTM1ZVBiOTF1SzlkOWFxcGVIZUNXbUplRFVhcUg3TTgrcGs2Q2w1cDNIci8yb3MvaDQ0NytnalV5bG9DbGsrNkcyUTZTcjR0OFVDSXhkWlVPU1JxMVM4RUxUczVCY3VrOHlUQXNKcHY1RnJyaStCbjYrWHFWcllpYlBUSUFXaFVxUU1Ib2xrT2IyTHpCd3NCdmQxQkprTlZuTE1WWEZkT2R3OTRMeHFyZW9MNkJub3JRa0pFOFJEejBtcW1KNTdIQmlWd3AvSWFKVFI2N1V2UkNNbTJWV2ZlNER5eFFhOUNHeFZuMkdwL2diVzR4SUJvY2dGRFB3K0lJK2RqQVJBclJ5bmZGbnRDV1JvQnowekhuZ3F4N04iLCJtYWMiOiI3ZWNlZjMxZDJkNGYzNDliODkxOTE5NjU2ZjY0NTg1MTQ1NDc2OTViYjFkOGE2ODZiOGE0ZjM3M2U3NWMyYzZiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-249856019\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1028244862 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1028244862\", {\"maxDepth\":0})</script>\n"}}