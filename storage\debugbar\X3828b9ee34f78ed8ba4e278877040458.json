{"__meta": {"id": "X3828b9ee34f78ed8ba4e278877040458", "datetime": "2025-07-31 11:15:11", "utime": **********.666482, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960510.878749, "end": **********.666506, "duration": 0.787757158279419, "duration_str": "788ms", "measures": [{"label": "Booting", "start": 1753960510.878749, "relative_start": 0, "end": **********.572402, "relative_end": **********.572402, "duration": 0.6936531066894531, "duration_str": "694ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.572416, "relative_start": 0.6936671733856201, "end": **********.666508, "relative_end": 1.9073486328125e-06, "duration": 0.09409189224243164, "duration_str": "94.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46937392, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1604\" onclick=\"\">app/Http/Controllers/FinanceController.php:1604-1663</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01073, "accumulated_duration_str": "10.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6268208, "duration": 0.00744, "duration_str": "7.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 69.338}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.647311, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 69.338, "width_percent": 6.803}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6521082, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1630", "source": "app/Http/Controllers/FinanceController.php:1630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1630", "ajax": false, "filename": "FinanceController.php", "line": "1630"}, "connection": "radhe_same", "start_percent": 76.142, "width_percent": 23.858}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-1223262468 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1223262468\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-436412080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-436412080\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1971609131 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1971609131\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikh3R1JMM2FZUmVwOEJtQThXMFM3TlE9PSIsInZhbHVlIjoiWU1uVXpvL2VaeVN4a2tySnZoY2VVcStMdFJFaEdIb3JrVHJXeXVrc1A5aGozWll1OVg1T0ljVElsVEE3N0JXakpsN2EyMWtYejFUd2hpZ0NZQWphTGQrL3NGT0VobkNISDVmRjlCR0RMSjd0dVEvdUFJQkVidVI1Z1lSWm1YOGZsd3l4YUNNb1p1eGZqODJWRGhNcDVTbklJN3gwSzNuS3pFMXdkNjlQa0QyT1V2NUVRdlc3NnRsUmJ6RDhMMElCSkpveEVWSEZ0eitYQUc4MmNIV1FrbUZ3Znd6Qjg5YzRtZ0s2ZTNGOXdnZkNDdGN3ZXh6VmZxTExiUFUxMjdjYS9vVzV3TlFqSDA1bVNwMjd3NFIwcWRPcXRNak9RaHNUTGlPd0tvL0FOU3hJVHNGTlpKVnVEdlFEbVo5SHRVdkljSXBydzZld0FpMDVadHlGTTZJZUVFbjdVSkN1Q08xOHkxMEpycm9sWm9zMWZiYkhydXNOMitBdHo0THhKcXpGRGFkRGI3RFRvR3psOXRvZVluTUhOVVdYSHI3Skd6RzRvRWwxdEhtN1hRWUNwZjNnZTh3TWgyOTdqU3dqQmQ1TlNxdGlJYjlGWUo3MFR1UndIcEVDUThJUGMvYjkrVEl0Z2ZncEIrVzhBYjBMejJnR0V1ejE2T00vNmVWUDVyZnUiLCJtYWMiOiI1OTg4YjEwY2I1Y2E4N2FjYTdhZWZmOTBjNWFmMWE0YWQ4ZTMxYzEwN2E2NTMwZjhjMzc2ZGMxZDJkYzViMTk0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Im5kQ29oaVdLZ2NiTHY3MEJ6RmNBVmc9PSIsInZhbHVlIjoiQktpSzRTTnEvVzF6ZmZMUUpnRTBvWVQ5Q1hGM21YREVMcWROOU5haFI3b2tIck01d2s5VGV6VUVWdUxpdU1xT2ROVUJYQkMzbGtRcmNFSmxFUjRhNGlXbXc4Wmp1aEhpMWVWaGtXbG5Lc1FZZHk4SGFXVWtXN0ZuRzJHZEpoYzN1ajcxTDU3UG0yemczWTl5KzUwRkhFN2dmMXFYTWxIYmRESjdsQ3BZbWhPMjQrOFhWUkRxbEVrTlF6VU9SUy9Tam1FU3ZjbldGMEZ6MWpmL3dFQ0NGcDZaZVZ3VkhEWk0yRGFKaDlSZmgxWE9pUTkyc2FOejRLbTlGTnN6aEN6YXdHQzdUNUNIRktiWWdyblhKVnk4c1dNS01yNjRLK0VxY2J0WGRORWZudDlYdWxZNHd5S0JQdnVLNW50VmZvekZYak5LVktNbHNxT1pRb0xuZzlEMkVRL091L2s4NHR4UXVWTjhpaTFKa2p1dGFOS0RVRDBuZGx4RUhxb2hLSUlzMTk1RzE3ZHZZNHNBV01iK3dXekJHaFpiOU03YWNhSFRBSW13QXVETS9KWnB3T1FWUUwyV2poT3o0MGFLVEpRdVg1VmxreVlyS3k5YlZodjNtWnlSR1RnVjBMQWdaem9GSVhCWW80enRHL1lBNFhEbXpmT2FSdnpzVjBwMkg0dHAiLCJtYWMiOiI2NmY0OWQxZjQxNWY2YTNmMzliZDE5MTg2ODJjN2M0YTFmMmY3YmY1ODExYTkyZmMwMmU2Njg2MTk4MjNlYjgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-695207013 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695207013\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1628005319 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:15:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJqeGp0ZytuSW1XTDdTWTFWTVJDTXc9PSIsInZhbHVlIjoiVWR1UDhzTkxDd3IzL1U5TjhIWktDUkJBamVoM2dadGl5dEE2V2hBekovQ1dkQmZ2TUg5SkdSNWVYdVNQMjlpZEFManFVSHZxRHhxc2VJbHBEMnV2NVdJUkFUMlV2RG0wYUs4cHEwaS9abkVVdXB5YUtrZm1CTFAvZWtyRDdLQWdodDVidUNhdm9RemhLM3hZV2JwSm1ZOTgzd0pxbU02YitDOW5tQTB4T3piTWRXQUNJSk9VaTBqVWZYbzVnR0lzMCtLL1pOVTJzY0VCYVQrUGRJZzc0ZW1QTVdVNGNlTEk5eElqTW5zZ3pWc05BQXRoVzRoMDNFR3FvdndsQnJYSTN1Tnp0cXJCalQwZURaUzh1NkdCNWUrblNQSERvRDFuWS9xektIeDJSVUFVTDhDOFZOdXNGQzhZbXRocCt3SWNLaStnOEhaTnUyZXpiWXJpK2NuRjZ3MDNCWkZONCtja0tWQUg3TGxuY09EWVpqZ0NMTk5lVy9ZYWZoTWJpNjdkS1creGtMcTk3TXJoSk9sdkxiWWZWQUp2bUlmUE5STGFiQXAxY21VcjdYMmc1TUg1MzdsbnErVHNSYVgvVVN4SzM4YjJqckFXTHVpbThNYUNDRkx1Q250VEZIU3dudEtGK1BIZ1ZKWU5iYVp2aHRpS0kzWk5MMTg3aXVGWkh3ZGIiLCJtYWMiOiIyZWFlNDBlNmRkMzNhNjA3OWZiMzc5Njk5NDdmMTcxNDgzNDAxOTM1NTk5NGJjZGI1MmE2MjRiYzYyOTY3OGM4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IitvS3dJRFZjeXFaUWU3UVpMdTJvK1E9PSIsInZhbHVlIjoiUWxhazV2ZGtFWmIzWk5hUGpIemd4RmI4TlNYUEo5NVNyYTkxb1JLd0owRTZWWmJrbERoOW9Dc0lMN3IrSTlpSmtKeXVQRkE0VlYwd0NrSHI4M2NpTGVQRmE2SU5nVHRvM3BoZ2M2SHdDUlN4TCs3QXNTbERIb2FjQkljbXJpazZYQUhhcUU4T2JkNlFhaHY3blIyTlNiNVdRdk5IY2dxQlR0QVVvZXdGOXByMndaaUhKVGFpQnJoa2V0azVLUWtwdXJTeThhUExucWJlc1ZMeGNzZi9jZlZkeXhOS1BrZndLN1JkZEFMeCtuU1NSMmw2K2VhYStJRC9pNWg0aWFnQnZSSjBOdGtZK29ndlJJYWRyTkNhaTg5RHpnOVhpU3RTVFF5MlBkblhwWDVaNDZ0YkxaclhCM01lK1R0dzlTUVVuNG1Tc0dMTk1hdlQzN1F2d21rRjZuM0lQZldFdzJGS0xKVmNucVFKNFZIWG4wai9iRmg0U1VkMyt0ZVNrMjJETUYwRWRteEZlMW1LeCttcDlENHlqQ0hXUWFpRmNvWW9qWXZLVGJPOEw1UmMvUlVtSWlJcHRZaTZLWFlTMWdvRWVTSGthOWx1U2syenNNdFJ6U2NBUkJab2w3WVpad2hSMUhYRjltWG1vT2dvSXN0QlZWSXZBanMrbG9VakFwbC8iLCJtYWMiOiJmZWUwYWZlYWViMGI1OGQyOWVlZDg3ZDg0YWIwMWZmNWY3NjBhN2U1MGNlODhjNjI2YmU2NTg3Y2U1ZGE1ZDE0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJqeGp0ZytuSW1XTDdTWTFWTVJDTXc9PSIsInZhbHVlIjoiVWR1UDhzTkxDd3IzL1U5TjhIWktDUkJBamVoM2dadGl5dEE2V2hBekovQ1dkQmZ2TUg5SkdSNWVYdVNQMjlpZEFManFVSHZxRHhxc2VJbHBEMnV2NVdJUkFUMlV2RG0wYUs4cHEwaS9abkVVdXB5YUtrZm1CTFAvZWtyRDdLQWdodDVidUNhdm9RemhLM3hZV2JwSm1ZOTgzd0pxbU02YitDOW5tQTB4T3piTWRXQUNJSk9VaTBqVWZYbzVnR0lzMCtLL1pOVTJzY0VCYVQrUGRJZzc0ZW1QTVdVNGNlTEk5eElqTW5zZ3pWc05BQXRoVzRoMDNFR3FvdndsQnJYSTN1Tnp0cXJCalQwZURaUzh1NkdCNWUrblNQSERvRDFuWS9xektIeDJSVUFVTDhDOFZOdXNGQzhZbXRocCt3SWNLaStnOEhaTnUyZXpiWXJpK2NuRjZ3MDNCWkZONCtja0tWQUg3TGxuY09EWVpqZ0NMTk5lVy9ZYWZoTWJpNjdkS1creGtMcTk3TXJoSk9sdkxiWWZWQUp2bUlmUE5STGFiQXAxY21VcjdYMmc1TUg1MzdsbnErVHNSYVgvVVN4SzM4YjJqckFXTHVpbThNYUNDRkx1Q250VEZIU3dudEtGK1BIZ1ZKWU5iYVp2aHRpS0kzWk5MMTg3aXVGWkh3ZGIiLCJtYWMiOiIyZWFlNDBlNmRkMzNhNjA3OWZiMzc5Njk5NDdmMTcxNDgzNDAxOTM1NTk5NGJjZGI1MmE2MjRiYzYyOTY3OGM4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IitvS3dJRFZjeXFaUWU3UVpMdTJvK1E9PSIsInZhbHVlIjoiUWxhazV2ZGtFWmIzWk5hUGpIemd4RmI4TlNYUEo5NVNyYTkxb1JLd0owRTZWWmJrbERoOW9Dc0lMN3IrSTlpSmtKeXVQRkE0VlYwd0NrSHI4M2NpTGVQRmE2SU5nVHRvM3BoZ2M2SHdDUlN4TCs3QXNTbERIb2FjQkljbXJpazZYQUhhcUU4T2JkNlFhaHY3blIyTlNiNVdRdk5IY2dxQlR0QVVvZXdGOXByMndaaUhKVGFpQnJoa2V0azVLUWtwdXJTeThhUExucWJlc1ZMeGNzZi9jZlZkeXhOS1BrZndLN1JkZEFMeCtuU1NSMmw2K2VhYStJRC9pNWg0aWFnQnZSSjBOdGtZK29ndlJJYWRyTkNhaTg5RHpnOVhpU3RTVFF5MlBkblhwWDVaNDZ0YkxaclhCM01lK1R0dzlTUVVuNG1Tc0dMTk1hdlQzN1F2d21rRjZuM0lQZldFdzJGS0xKVmNucVFKNFZIWG4wai9iRmg0U1VkMyt0ZVNrMjJETUYwRWRteEZlMW1LeCttcDlENHlqQ0hXUWFpRmNvWW9qWXZLVGJPOEw1UmMvUlVtSWlJcHRZaTZLWFlTMWdvRWVTSGthOWx1U2syenNNdFJ6U2NBUkJab2w3WVpad2hSMUhYRjltWG1vT2dvSXN0QlZWSXZBanMrbG9VakFwbC8iLCJtYWMiOiJmZWUwYWZlYWViMGI1OGQyOWVlZDg3ZDg0YWIwMWZmNWY3NjBhN2U1MGNlODhjNjI2YmU2NTg3Y2U1ZGE1ZDE0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1628005319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1212359974 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212359974\", {\"maxDepth\":0})</script>\n"}}