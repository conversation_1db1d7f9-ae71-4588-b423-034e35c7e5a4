{"__meta": {"id": "X9523a73421e79571736d5399d1844784", "datetime": "2025-07-31 12:39:16", "utime": **********.846348, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965555.229221, "end": **********.84641, "duration": 1.6171889305114746, "duration_str": "1.62s", "measures": [{"label": "Booting", "start": 1753965555.229221, "relative_start": 0, "end": **********.619015, "relative_end": **********.619015, "duration": 1.389793872833252, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.619049, "relative_start": 1.3898279666900635, "end": **********.846416, "relative_end": 5.9604644775390625e-06, "duration": 0.22736692428588867, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46937392, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1604\" onclick=\"\">app/Http/Controllers/FinanceController.php:1604-1663</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026860000000000002, "accumulated_duration_str": "26.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.742234, "duration": 0.02404, "duration_str": "24.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 89.501}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.802385, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 89.501, "width_percent": 5.585}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8161082, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1630", "source": "app/Http/Controllers/FinanceController.php:1630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1630", "ajax": false, "filename": "FinanceController.php", "line": "1630"}, "connection": "radhe_same", "start_percent": 95.086, "width_percent": 4.914}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-471263427 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-471263427\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1841999264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1841999264\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-837630279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-837630279\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1341459101 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilg4ekIrSjhUbHVRN2Yrb1UyU2JzMlE9PSIsInZhbHVlIjoidkplRnFjd0laVGhYd1ZTSWxuKzhIOUdLMk5LYW1tOXRwa1NRUXhpRTducGM3OU96YmdENlcxNFVnSnhqUUgxbHVhcXFlR1BtdExNYTVndWFFWEkzL0FCby9PM0pSRlYzNDlLM29nZHlITU5RWDFOTmdiWWFqQW5GbTgrRktHblVUcEwrdnhBSCs3Mmd6VDl0SVl0bW81aDlZUjFxT3prK09LbE5Xd0dhUzkwdVRyYlk5VGtmclV3Vk1wZldhWFg5YWhvcDZmVGRJb1FUSjI2bEtLM1ZHenB2VmxGazF3UWtWSkNEdi9INENENVljeGJGdXVPUUtwaGRMblBBOGFjeGJXcEprUW9YK3BUckh5RVF4bWlGaUUrOHpXU01xdXhJYlJVVmg2MDdIYjV6Yjd4ZDZjR1hSZDhLekdlRk16cUt6Zy9DOWVTditlUFNNVE1qT0ZSaWludzB0Rk9ia1Ezakswc1pyTkhRMnpQMUVGV0tUNm42VEI0S2UrcGh0VU5HYm85dW0zNGVQYW1NU1hmNFEzMkpCc2hhMDVhK3I3SGY2RnVNbTV6L2l1VDVXOXFuS3dwN0JRb3hoYzRuOHNsT0tGaFdRdGpVeGo4djhySnU1K0NVdVI2UGlFNFNUU3lvYUdRSjI0RFhWUlh6YzRsU2Q5YTJMeStTVnEvSWFuSnMiLCJtYWMiOiJjMjZhMjUwYjIyOWRhYjkzYTVhYWM0ZTRlOTEyNTk1YWM0ZmNkZWRhMDBkOTdhYWM4MDU4M2JkMTcxYzdhODczIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlBqbUFzQTl2VnRhUjgrN2dmeGYveHc9PSIsInZhbHVlIjoibkFYRHVTT3FiU2VRdmt4RkNldFFhblFTdTBHaHBiVkJpbkdVNlNPNFArUVRlcHo0bGhFaGRWWHpkdW96MzNpMHlKVDhyYTZWOGtmcEVGam5HWnZEQ09HTWhONlhzVG50RDRDZnlLejdFVG43bnhJaGF4RTdqUkZjVU01QmVzWEtxVlJrL21nVkhBN2VNMTZuTEtVMFNncVVOdWVENmQ2SEdYVDE0dE1xUVVUZXZOaVBLY2grcW5tTjRjak5pWXpyaGU2SDJTZmNjaFF6eStjY2hUVTlJaTNMS2FlOFlLUDM4dEp1a3JVYXhmQW9OQXlsZGlEUnN5c29vU1g3V1ZTZk9vN1p5U3lDdWVuUVptT1hsay9Sczg3NUd0OGd0akduWDNQMDlURUtKV0owci9KMEtLbGdtMHZHcHZMYko4U1AzL3I0RWxXN0JqNFpZeVpKcm1mNnpVVGExWDhLU3liMlhhVWt2Rm9ad0tVOVFqYlljUmJzRVpJay9jUW5VMloyM1ZObDU0ZG9xMHJBek5wcFEzc3gwN21LWVRIdG4xQTNkTmlMckZlYW1vT0tYM0d6a043VlA5OXJrb2pWTFkybldTWkhYT3pVNGhYUTNJR25FbXZhNHNHR0hlaHlYZUU0NVJNN0ppUDBoRjFPR0VnZVIydGJJeWh5eDAxbE5DclciLCJtYWMiOiI4Mzc3OTgyNDVmYTg0YzU0ZjZkM2NlN2RmZjM2ZTc2YzcyZmIxOTNiOTBlOGZiODdhYTkzMmVhZWU2YTE1OWY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341459101\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1985211887 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985211887\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-311853842 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:39:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ0S3JUTDN1a2VHZUhaN1hFSGtmQmc9PSIsInZhbHVlIjoiZy94U2JBS1Q3NmdaWjVSbkJKa0JiRzRsR1EybDVXdmQ1NG5PUVNJVVJDQm1BY2NtdDBYcDhVaGZGTW5EUmJCYjYyS1dDVFRNWHZjK3FJZmlCcTRmdTlRNlR6OG1aWWdWWlQ0M0NKVUdLS0Qva29TdmFlaHB6anBSQktad0dxZGFUTk9hVFk0WHRicm5uLzJSaGtvMW1QN1dJYnJPenYrQmN2R2haVUoybDRjbW1tQWo3YXpTbFNGVFF5ZGtNMDIra1JxWEVScUFNdUJTSlJTdmR5OU1wQ20yVVl6V0JnU1R0d0VpWFZiSmREdG9LN3hIMVNiR2hjL0FoRWF5MzdaMURIVkZJb1FLV0VQQXNXa0hTYzlUakVxbE9ZOHFHOENHL2VwU0hWcUlRdlc0SXhPb2NTUmV2RHBwVDV4TjFwYzRIN2pmaWhUOXBMMlhzOTY2VEtxT29QaGpPWUNFUUlIc2lNelJ6MjF5MFVUeDY2ekMway9OOHQ3SWwwZkd6VHdCVkIydk1PaW1scUdaL0p2NDVLS3dTMndOK0FScVNlZDdWWml6U2NpcGF1RGJRREtaTmZnYldwYUZRSzhWdUVoNXgyYlA3R1RpVkV1dXVtYWM1Mk43c2pFTEd3UkpCZXl3dmgyZDRQU0ZTa2hwSFlLSUFzNkpkQ05GcjBGR3ZmbW4iLCJtYWMiOiJlNzMxZWMyY2MzYjcyYTY1NmQ2MmRmYWI5MjdmNTZmMTI5NDMzZmZmZTNiYzcwNTI1MDNlNmFmN2RkZDAyNWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:39:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6InlDU0wwQ1VOeEsrRTF0Y2V1Qy83c0E9PSIsInZhbHVlIjoiejBTdkV1V2NKZUo3cFhEZmZZMGg5aVFlOFJqZWhJZEdKZTVGbTZHbmEwaGZDMXBZWHFzd0ZxQzM5UkpVOU1xVVR2enNKQkVwcGlyamxRd1FQQ2RaQm15ZnJYbHlnbHI3SVhBb1dNTFdMSndRYll5dnFERWMrTXJGdStXenVxcWJYZUkvTGlWeDdqdGZvWEZmNzB0ODRFV2RXNS90dW1jcjdKMjZSTDh3NkdaWk9LZEJqaGJsb2Q1WXhnSkZoS0hQT0VOdGRZbUlnWmtjbXhzZHZXNFRWcndvODBmbm4xZURRRjljOWVZdkhHVFFQcVZsbHJCSGF2UENZUkZYSHhoTUg0aS9SaWFVT0dPLzE4YWlvRzBBc0hLSE1LYk1pbUtFc29Bb0pLSHNIWjRsR05PZXVrWE4yV2VXTTVWM0hjb0VpTWhvb28vUHZvTEVscDdrU1RVMnRLY0o2SE9Yb3lmcjd5SENDWmFxNGtGRlpKMjFaUWtmTzIzUUl0RUtWWmxUNDA4SUJDV2pqamdJcndLUmVsMHl2VVp1K3Y4RHNDaUd1TUhBS2RWWjFJdlFQNm11L1ovczBZK3Q0alMzekZTbTV5TkNQSGlxcDY2cXpSNVljbVFFZEgrTFl4UGdEZ2UvVkUwUUJTOUpRSG5NNGc4N2pjaGV2VlBwYldETWFGY24iLCJtYWMiOiJjMjlhMTFjMzU0MGIxMmIyMzkyYzczMTFmNjNlNzdiNWNmNDJjZDVhYTM2M2IzOTQwNTRmMjU3NWU3M2NjZGM2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:39:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ0S3JUTDN1a2VHZUhaN1hFSGtmQmc9PSIsInZhbHVlIjoiZy94U2JBS1Q3NmdaWjVSbkJKa0JiRzRsR1EybDVXdmQ1NG5PUVNJVVJDQm1BY2NtdDBYcDhVaGZGTW5EUmJCYjYyS1dDVFRNWHZjK3FJZmlCcTRmdTlRNlR6OG1aWWdWWlQ0M0NKVUdLS0Qva29TdmFlaHB6anBSQktad0dxZGFUTk9hVFk0WHRicm5uLzJSaGtvMW1QN1dJYnJPenYrQmN2R2haVUoybDRjbW1tQWo3YXpTbFNGVFF5ZGtNMDIra1JxWEVScUFNdUJTSlJTdmR5OU1wQ20yVVl6V0JnU1R0d0VpWFZiSmREdG9LN3hIMVNiR2hjL0FoRWF5MzdaMURIVkZJb1FLV0VQQXNXa0hTYzlUakVxbE9ZOHFHOENHL2VwU0hWcUlRdlc0SXhPb2NTUmV2RHBwVDV4TjFwYzRIN2pmaWhUOXBMMlhzOTY2VEtxT29QaGpPWUNFUUlIc2lNelJ6MjF5MFVUeDY2ekMway9OOHQ3SWwwZkd6VHdCVkIydk1PaW1scUdaL0p2NDVLS3dTMndOK0FScVNlZDdWWml6U2NpcGF1RGJRREtaTmZnYldwYUZRSzhWdUVoNXgyYlA3R1RpVkV1dXVtYWM1Mk43c2pFTEd3UkpCZXl3dmgyZDRQU0ZTa2hwSFlLSUFzNkpkQ05GcjBGR3ZmbW4iLCJtYWMiOiJlNzMxZWMyY2MzYjcyYTY1NmQ2MmRmYWI5MjdmNTZmMTI5NDMzZmZmZTNiYzcwNTI1MDNlNmFmN2RkZDAyNWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:39:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6InlDU0wwQ1VOeEsrRTF0Y2V1Qy83c0E9PSIsInZhbHVlIjoiejBTdkV1V2NKZUo3cFhEZmZZMGg5aVFlOFJqZWhJZEdKZTVGbTZHbmEwaGZDMXBZWHFzd0ZxQzM5UkpVOU1xVVR2enNKQkVwcGlyamxRd1FQQ2RaQm15ZnJYbHlnbHI3SVhBb1dNTFdMSndRYll5dnFERWMrTXJGdStXenVxcWJYZUkvTGlWeDdqdGZvWEZmNzB0ODRFV2RXNS90dW1jcjdKMjZSTDh3NkdaWk9LZEJqaGJsb2Q1WXhnSkZoS0hQT0VOdGRZbUlnWmtjbXhzZHZXNFRWcndvODBmbm4xZURRRjljOWVZdkhHVFFQcVZsbHJCSGF2UENZUkZYSHhoTUg0aS9SaWFVT0dPLzE4YWlvRzBBc0hLSE1LYk1pbUtFc29Bb0pLSHNIWjRsR05PZXVrWE4yV2VXTTVWM0hjb0VpTWhvb28vUHZvTEVscDdrU1RVMnRLY0o2SE9Yb3lmcjd5SENDWmFxNGtGRlpKMjFaUWtmTzIzUUl0RUtWWmxUNDA4SUJDV2pqamdJcndLUmVsMHl2VVp1K3Y4RHNDaUd1TUhBS2RWWjFJdlFQNm11L1ovczBZK3Q0alMzekZTbTV5TkNQSGlxcDY2cXpSNVljbVFFZEgrTFl4UGdEZ2UvVkUwUUJTOUpRSG5NNGc4N2pjaGV2VlBwYldETWFGY24iLCJtYWMiOiJjMjlhMTFjMzU0MGIxMmIyMzkyYzczMTFmNjNlNzdiNWNmNDJjZDVhYTM2M2IzOTQwNTRmMjU3NWU3M2NjZGM2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:39:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311853842\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-345897954 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345897954\", {\"maxDepth\":0})</script>\n"}}