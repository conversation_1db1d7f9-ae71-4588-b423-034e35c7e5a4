<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Invoice;
use App\Models\Bill;
use App\Models\Revenue;
use App\Models\Expense;
use App\Models\Payment;
use App\Models\Transaction;
use App\Models\Customer;
use App\Models\ProductService;
use App\Models\Product;
use App\Models\TaxSlab;
use App\Models\ProductEmiOption;
use App\Models\ProductShippingField;
use App\Models\ProductBumpOffer;
use App\Models\Coupon;

class FinanceController extends Controller
{
    /**
     * Display the Finance dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard()
    {
        if (!Gate::check('manage customer') && !Gate::check('manage vender') && !Gate::check('manage invoice')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Get data for the product modal in plan tab
        $category = \App\Models\ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())
            ->where('type', '=', 'product & service')->get()->pluck('name', 'id');
        $unit = \App\Models\ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');
        $tax = \App\Models\Tax::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');

        return view('finance.dashboard', compact('category', 'unit', 'tax'));
    }

    /**
     * Display the Plan section (Products, Coupons, Links).
     *
     * @return \Illuminate\Http\Response
     */
    public function plan()
    {
        if (!Gate::check('manage product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // Get data for the product modal
        $category = \App\Models\ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())
            ->where('type', '=', 'product & service')->get()->pluck('name', 'id');
        $unit = \App\Models\ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');
        $tax = \App\Models\Tax::where('created_by', '=', \Auth::user()->creatorId())
            ->get()->pluck('name', 'id');

        return view('finance.plan.index', compact('category', 'unit', 'tax'));
    }

    /**
     * Store a new product from the modal form
     */
    public function storeProduct(Request $request)
    {


        if (!\Auth::user()->can('create product & service')) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }

        $rules = [
            'name' => 'required|string|max:255',
            'nickname' => 'nullable|string|max:255',
            'price' => 'required|numeric|min:0',
            'striked_price' => 'nullable|numeric|min:0',
            'tax_slab' => 'nullable|exists:tax_slabs,id',
            'hsn_sac_no' => 'nullable|string|max:50',
            'redirect_url' => 'nullable|url|max:255',
            'product_description' => 'nullable|string',
            'invoice_footer_description' => 'nullable|string',
            'restrict_one_time' => 'nullable|boolean',
            'is_free_trial' => 'nullable|boolean',
            'trial_duration_type' => 'nullable|in:day,month,year',
            'trial_duration' => 'nullable|integer|min:0',
            'trial_price' => 'nullable|numeric|min:0',
            'is_subscription' => 'nullable|boolean',
            'is_cancellable' => 'nullable|boolean',
            'billed_every' => 'nullable|integer|min:1',
            'billing_cycle_type' => 'nullable|in:forever,limited',
            'billing_cycle_limit' => 'nullable|integer|min:1',
            'emi_options' => 'nullable|boolean',
            'emi_options_value' => 'nullable|array',
            'emi_options_value.*' => 'integer|between:1,15',
            'down_payment' => 'nullable|numeric|min:0',
            'show_shipping_field' => 'nullable|boolean',
            'required_shipping_field' => 'nullable|boolean',
            'payment_gateway' => 'nullable|boolean',
            'payment_gateway_link' => 'nullable|string',
            'custom_gateway_url' => 'nullable|url',
            'skip_gst_form' => 'nullable|boolean',
            'pro_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {


            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            \DB::beginTransaction();

            // Create the product
            $product = new \App\Models\Product();
            $product->name = $request->name;
            $product->nickname = $request->nickname;
            $product->is_one_time_only = $request->boolean('restrict_one_time');
            $product->is_free_trial = $request->boolean('is_free_trial');
            $product->trial_duration_type = $request->trial_duration_type ?: 'day';
            $product->trial_duration = $request->trial_duration ?: 0;
            $product->total_trial_price = $request->trial_price ?: 0;
            $product->is_subscription = $request->boolean('is_subscription');
            $product->is_cancelable = $request->boolean('is_cancellable', true);
            $product->billing_every = $request->billed_every ?: 1;
            $product->billing_cycle_type = $request->billing_cycle_type ?: 'forever';
            $product->billing_cycle_limit = $request->billing_cycle_limit;
            $product->product_price = $request->price;
            $product->striked_price = $request->striked_price;
            $product->downpayment = $request->down_payment ?: 0;
            $product->show_shipping_field = $request->boolean('show_shipping_field');
            $product->require_shipping_field = $request->boolean('required_shipping_field');
            $product->payment_gateway = $request->payment_gateway_link ?: ($request->custom_gateway_url ?: null);
            $product->skip_gst_form = $request->boolean('skip_gst_form');
            $product->hsn_sac_no = $request->hsn_sac_no;
            $product->redirect_url = $request->redirect_url;
            $product->product_description = $request->product_description;
            $product->invoice_footer_description = $request->invoice_footer_description;
            $product->tax_slab_id = $request->tax_slab;
            $product->is_active = true; // Set as active by default

            // Handle image upload
            if ($request->hasFile('pro_image')) {
                $image = $request->file('pro_image');
                $fileName = time() . '_' . $image->getClientOriginalName();
                $imagePath = $image->storeAs('products', $fileName, 'public');
                $product->product_image = $fileName;
            }

            $product->save();

            // Handle EMI options
            if ($request->boolean('emi_options') && $request->has('emi_options_value')) {
                $emiOptions = is_array($request->emi_options_value)
                    ? $request->emi_options_value
                    : [$request->emi_options_value];

                foreach ($emiOptions as $emiMonth) {
                    \App\Models\ProductEmiOption::create([
                        'product_id' => $product->id,
                        'emi_month' => $emiMonth
                    ]);
                }
            }

            // Handle shipping fields
            if ($request->boolean('show_shipping_field')) {
                \App\Models\ProductShippingField::create([
                    'product_id' => $product->id,
                    'is_required' => $request->boolean('required_shipping_field')
                ]);
            }

            // Handle bump offers (if any are submitted)
            if ($request->has('bump_offers')) {
                $bumpOffers = $request->bump_offers;
                foreach ($bumpOffers as $offer) {
                    if (!empty($offer['title']) && !empty($offer['price'])) {
                        \App\Models\ProductBumpOffer::create([
                            'product_id' => $product->id,
                            'is_optional' => $offer['is_optional'] ?? false,
                            'title' => $offer['title'],
                            'price' => $offer['price'],
                            'description' => $offer['description'] ?? null
                        ]);
                    }
                }
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('Product successfully created.'),
                'product' => $product->load(['taxSlab', 'emiOptions', 'shippingFields', 'bumpOffers'])
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Product creation failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * Display the Sales section (Subscription, Installment).
     *
     * @return \Illuminate\Http\Response
     */
    public function sales()
    {
        if (!Gate::check('manage customer')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return view('finance.sales.index');
    }

    /**
     * Display the Invoices section.
     *
     * @return \Illuminate\Http\Response
     */
    public function invoices()
    {
        if (!Gate::check('manage invoice')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('invoice.index');
    }

    /**
     * Display the Transactions section.
     *
     * @return \Illuminate\Http\Response
     */
    public function transactions()
    {
        if (!Gate::check('manage transaction')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('transaction.index');
    }

    /**
     * Display the Expenses section.
     *
     * @return \Illuminate\Http\Response
     */
    public function expenses()
    {
        if (!Gate::check('manage expense')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('expense.index');
    }

    /**
     * Display the Reports section.
     *
     * @return \Illuminate\Http\Response
     */
    public function reports()
    {
        if (!Gate::check('income vs expense report')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return view('finance.reports.index');
    }

    /**
     * Display the Payment Gateways section.
     *
     * @return \Illuminate\Http\Response
     */
    public function paymentGateways()
    {
        if (!Gate::check('manage company settings')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        return redirect()->route('settings')->with('tab', 'payment');
    }

    /**
     * Display the Business Info section.
     *
     * @return \Illuminate\Http\Response
     */
    public function businessInfo()
    {
        if (!Gate::check('manage company settings')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $businessInfo = \App\Models\BusinessInfo::getByUser(Auth::user()->creatorId());

        // If no business info exists, create a default one with some sample data
        if (!$businessInfo) {
            $businessInfo = \App\Models\BusinessInfo::updateOrCreateForUser(Auth::user()->creatorId(), [
                'company_name' => Auth::user()->name . "'s Company",
                'company_email' => Auth::user()->email,
                'company_phone' => '',
                'country' => 'India',
                'currency' => 'INR',
                'business_state' => 'West Bengal',
                'street_address' => '',
                'city' => '',
                'pincode' => '',
                'state_prov_region' => '',
                'address_country' => 'India',
                'time_zone' => 'Asia/Kolkata',
                'contact_person_name' => Auth::user()->name,
                'job_position' => 'Owner',
                'contact_email' => Auth::user()->email,
                'contact_phone' => ''
            ]);
        }

        // Debug: Log the business info data
        \Log::info('Business Info Retrieved:', [
            'user_id' => Auth::user()->creatorId(),
            'business_info' => $businessInfo ? $businessInfo->toArray() : null
        ]);

        return view('finance.dashboard', compact('businessInfo'));
    }

    /**
     * Update business information.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateBusinessInfo(Request $request)
    {
        if (!Gate::check('manage company settings')) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }



        $rules = [
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email|max:255',
            'company_phone' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'currency' => 'required|string|max:10',
            'business_gst' => 'nullable|string|max:50',
            'business_state' => 'required|string|max:100',
            'business_logo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'agree_gst_change' => 'nullable|in:0,1',
            'street_address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'pincode' => 'required|string|max:20',
            'state_prov_region' => 'required|string|max:100',
            'address_country' => 'required|string|max:100',
            'time_zone' => 'required|string|max:100',
            'contact_person_name' => 'required|string|max:255',
            'job_position' => 'required|string|max:100',
            'contact_email' => 'required|email|max:255',
            'contact_phone' => 'required|string|max:20',
        ];

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $data = $request->except(['_token', 'business_logo']);

            // Handle checkbox properly - ensure it's 0 or 1
            $data['agree_gst_change'] = $request->input('agree_gst_change', 0);

            // Filter out empty values (except for checkbox fields)
            $filteredData = [];
            foreach ($data as $key => $value) {
                if ($key === 'agree_gst_change' || (!empty($value) && trim($value) !== '')) {
                    $filteredData[$key] = $value;
                }
            }
            $data = $filteredData;

            // Handle business logo upload
            if ($request->hasFile('business_logo')) {
                $file = $request->file('business_logo');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs('business_logos', $fileName, 'public');
                $data['business_logo'] = $filePath;
            }

            $businessInfo = \App\Models\BusinessInfo::updateOrCreateForUser(
                Auth::user()->creatorId(),
                $data
            );

            return response()->json([
                'success' => true,
                'message' => __('Profile updated successfully'),
                'data' => $businessInfo,
                'saved_fields' => count($data)
            ]);

        } catch (\Exception $e) {
            Log::error('Business Info Update Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * API endpoint to get business information details
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBusinessInfoApi()
    {
        try {
            $businessInfo = \App\Models\BusinessInfo::getByUser(Auth::user()->creatorId());

            if (!$businessInfo) {
                return response()->json([
                    'success' => false,
                    'message' => __('No business information found'),
                    'data' => null
                ]);
            }

            // Format the business information for API response
            $formattedData = [
                'general' => [
                    'company_name' => $businessInfo->company_name,
                    'company_email' => $businessInfo->company_email,
                    'company_phone' => $businessInfo->company_phone,
                    'country' => $businessInfo->country,
                    'currency' => $businessInfo->currency,
                    'business_gst' => $businessInfo->business_gst,
                    'business_state' => $businessInfo->business_state,
                    'business_logo' => $businessInfo->business_logo ? asset('storage/' . $businessInfo->business_logo) : null,
                    'website' => $businessInfo->website,
                    'business_type' => $businessInfo->business_type,
                    'industry' => $businessInfo->industry,
                    'established_date' => $businessInfo->established_date,
                    'business_description' => $businessInfo->business_description,
                    'agree_gst_change' => $businessInfo->agree_gst_change
                ],
                'address' => [
                    'street_address' => $businessInfo->street_address,
                    'city' => $businessInfo->city,
                    'pincode' => $businessInfo->pincode,
                    'state_prov_region' => $businessInfo->state_prov_region,
                    'address_country' => $businessInfo->address_country,
                    'time_zone' => $businessInfo->time_zone
                ],
                'contact_person' => [
                    'contact_person_name' => $businessInfo->contact_person_name,
                    'job_position' => $businessInfo->job_position,
                    'contact_email' => $businessInfo->contact_email,
                    'contact_phone' => $businessInfo->contact_phone,
                    'registration_number' => $businessInfo->registration_number,
                    'tax_id' => $businessInfo->tax_id
                ],
                'metadata' => [
                    'id' => $businessInfo->id,
                    'created_at' => $businessInfo->created_at,
                    'updated_at' => $businessInfo->updated_at,
                    'last_updated' => $businessInfo->updated_at ? $businessInfo->updated_at->diffForHumans() : null
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => __('Business information retrieved successfully'),
                'data' => $formattedData,
                'raw_data' => $businessInfo->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('Business Info API Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => __('Error retrieving business information'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get total revenue for the creator.
     *
     * @param int $creatorId
     * @return float
     */
    private function getTotalRevenue($creatorId)
    {
        // Calculate invoice total by getting all invoices and summing their totals
        $invoices = Invoice::where('created_by', $creatorId)
            ->where('status', '!=', 'Draft')
            ->get();

        $invoiceTotal = 0;
        foreach ($invoices as $invoice) {
            $invoiceTotal += $invoice->getTotal();
        }

        $revenueTotal = Revenue::where('created_by', $creatorId)->sum('amount');

        return $invoiceTotal + $revenueTotal;
    }

    /**
     * Get total expenses for the creator.
     *
     * @param int $creatorId
     * @return float
     */
    private function getTotalExpenses($creatorId)
    {
        // Calculate bill total by getting all bills and summing their totals
        $bills = Bill::where('created_by', $creatorId)->get();

        $billTotal = 0;
        foreach ($bills as $bill) {
            $billTotal += $bill->getTotal();
        }

        $expenseTotal = Expense::where('created_by', $creatorId)->sum('amount');

        return $billTotal + $expenseTotal;
    }

    /**
     * Get total number of invoices for the creator.
     *
     * @param int $creatorId
     * @return int
     */
    private function getTotalInvoices($creatorId)
    {
        return Invoice::where('created_by', $creatorId)->count();
    }

    /**
     * Get recent transactions for the creator.
     *
     * @param int $creatorId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRecentTransactions($creatorId)
    {
        // Get recent revenue and payment transactions
        $revenues = Revenue::where('created_by', $creatorId)
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($revenue) {
                return [
                    'type' => 'income',
                    'description' => $revenue->description ?? 'Revenue',
                    'amount' => $revenue->amount,
                    'date' => $revenue->date,
                ];
            });

        $payments = Payment::where('created_by', $creatorId)
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($payment) {
                return [
                    'type' => 'expense',
                    'description' => $payment->description ?? 'Payment',
                    'amount' => $payment->amount,
                    'date' => $payment->date,
                ];
            });

        return $revenues->merge($payments)->sortByDesc('date')->take(10);
    }

    /**
     * Get monthly financial data for charts.
     *
     * @param int $creatorId
     * @return array
     */
    private function getMonthlyFinancialData($creatorId)
    {
        $months = [];
        $revenues = [];
        $expenses = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $months[] = $month->format('M Y');

            // Calculate monthly revenue
            $monthlyRevenue = Invoice::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->where('status', '!=', 'Draft')
                ->sum('getTotal');

            $monthlyRevenue += Revenue::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('amount');

            $revenues[] = $monthlyRevenue;

            // Calculate monthly expenses
            $monthlyExpense = Bill::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('getTotal');

            $monthlyExpense += Expense::where('created_by', $creatorId)
                ->whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->sum('amount');

            $expenses[] = $monthlyExpense;
        }

        return [
            'months' => $months,
            'revenues' => $revenues,
            'expenses' => $expenses,
        ];
    }

    /**
     * Get finance overview data for API/AJAX requests.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFinanceOverview()
    {
        $user = Auth::user();
        $creatorId = $user->creatorId();

        $data = [
            'total_revenue' => $this->getTotalRevenue($creatorId),
            'total_expenses' => $this->getTotalExpenses($creatorId),
            'total_invoices' => $this->getTotalInvoices($creatorId),
            'net_profit' => $this->getTotalRevenue($creatorId) - $this->getTotalExpenses($creatorId),
            'monthly_data' => $this->getMonthlyFinancialData($creatorId),
        ];

        return response()->json($data);
    }

    /**
     * Store a new coupon from the modal form
     */
    public function storeCoupon(Request $request)
    {
        // Temporarily disable permission check for testing
        // if (!\Auth::user()->can('create coupon')) {
        //     return response()->json(['error' => __('Permission denied.')], 403);
        // }

        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:coupons,code',
            'discount' => 'required|numeric|min:0',
            'discount_type' => 'required|in:percentage,fixed',
            'limit' => 'required|integer|min:1',
            'product_id' => 'nullable|exists:product_services,id',
            'start_date' => 'nullable|date|after_or_equal:today',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'description' => 'nullable|string|max:1000',
        ];

        // Additional validation for percentage discount
        if ($request->discount_type === 'percentage' && $request->discount > 100) {
            return response()->json([
                'success' => false,
                'errors' => ['discount' => [__('Percentage discount cannot exceed 100%.')]]
            ], 422);
        }

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get product name if product_id is provided
            $productName = null;
            if ($request->product_id) {
                $product = \App\Models\ProductService::find($request->product_id);
                $productName = $product ? $product->name : null;
            }

            $coupon = new \App\Models\Coupon();
            $coupon->name = $request->name;
            $coupon->code = strtoupper($request->code);
            $coupon->product_id = $request->product_id;
            $coupon->product_name = $productName;
            $coupon->discount = $request->discount;
            $coupon->discount_type = $request->discount_type;
            $coupon->limit = $request->limit;
            $coupon->start_date = $request->start_date;
            $coupon->end_date = $request->end_date;
            $coupon->description = $request->description;
            $coupon->is_active = $request->has('is_active') ? 1 : 0;
            $coupon->created_by = \Auth::user()->creatorId();
            $coupon->save();

            return response()->json([
                'success' => true,
                'message' => __('Coupon created successfully.'),
                'coupon' => $coupon
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

  public function editCoupon(Request $request)
    {
        $couponId = $request->coupon_id;

        // Check permission if required
        // if (!\Auth::user()->can('edit coupon')) {
        //     return response()->json(['success' => false, 'message' => __('Permission denied.')], 403);
        // }

        $coupon = \App\Models\Coupon::find($couponId);

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => __('Coupon not found.')
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $coupon->id,
                'name' => $coupon->name,
                'code' => $coupon->code,
                'product_id' => $coupon->product_id,
                'discount_type' => $coupon->discount_type,
                'discount' => $coupon->discount,
                'limit' => $coupon->limit,
                'start_date' => $coupon->start_date,
                'end_date' => $coupon->end_date,
                'is_active' => $coupon->is_active,
                'description' => $coupon->description,
            ]
        ]);
    }

    // Update Coupon Code
    public function updateCoupon(Request $request)
    {
        $coupon = \App\Models\Coupon::find($request->coupon_id);

        if (!$coupon) {
            return response()->json(['success' => false, 'message' => 'Coupon not found']);
        }

        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:255|unique:coupons,code,' . $coupon->id,
            'discount' => 'required|numeric|min:0',
            'discount_type' => 'required|in:percentage,fixed',
            'limit' => 'required|integer|min:1',
            'product_id' => 'nullable|exists:product_services,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'description' => 'nullable|string|max:1000',
 ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
  // Get product name if product_id is provided
            $productName = null;
            if ($request->product_id) {
                $product = \App\Models\ProductService::find($request->product_id);
                $productName = $product ? $product->name : null;
            }

            $coupon->name = $request->name;
            $coupon->code = strtoupper($request->code);
            $coupon->product_id = $request->product_id;
            $coupon->product_name = $productName;
            $coupon->discount = $request->discount;
            $coupon->discount_type = $request->discount_type;
            $coupon->limit = $request->limit;
            $coupon->start_date = $request->start_date;
            $coupon->end_date = $request->end_date;
            $coupon->description = $request->description;
            $coupon->is_active = $request->has('is_active') ? 1 : 0;
            $coupon->save();

            return response()->json([
                'success' => true,
                'message' => __('Coupon updated successfully.'),
                'coupon' => $coupon
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Update failed'], 500);
        }
    }


    /**
     * Store a new subscription
     */
    public function storeSubscription(Request $request)
    {
        $rules = [
            'customer_id' => 'required',
            'customer_type' => 'required|in:customer,lead',
            'customer_email' => 'required|email',
            'product_id' => 'required|exists:product_services,id',
            'start_date' => 'required|date',
            'product_price' => 'required|numeric|min:0',
            'down_payment' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'total_emis' => 'required|integer|min:1',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly',
            'payment_method' => 'required|in:offline,online',
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get customer/lead and product details
            $customer = null;
            $customerName = '';

            if ($request->customer_type === 'customer') {
                $customer = \App\Models\Customer::where('id', $request->customer_id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();
                $customerName = $customer ? $customer->name : '';
            } elseif ($request->customer_type === 'lead') {
                $customer = \App\Models\Lead::where('id', $request->customer_id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();
                $customerName = $customer ? $customer->name : '';
            }

            $product = \App\Models\ProductService::find($request->product_id);

            if (!$customer || !$product) {
                return response()->json([
                    'success' => false,
                    'message' => __('Customer/Lead or Product not found.')
                ], 404);
            }

            // Calculate amounts
            $productPrice = $request->product_price;
            $downPayment = $request->down_payment ?? 0;
            $discountAmount = $request->discount_amount ?? 0;
            $totalEmis = $request->total_emis;

            $remainingAmount = $productPrice - $downPayment - $discountAmount;
            $emiAmount = $remainingAmount / $totalEmis;
            $pendingAmount = $remainingAmount;

            // Calculate next EMI date
            $startDate = \Carbon\Carbon::parse($request->start_date);
            $nextEmiDate = $startDate->copy();

            switch ($request->billing_cycle) {
                case 'monthly':
                    $nextEmiDate->addMonth();
                    break;
                case 'quarterly':
                    $nextEmiDate->addMonths(3);
                    break;
                case 'yearly':
                    $nextEmiDate->addYear();
                    break;
            }

            // Create subscription
            $subscription = new \App\Models\Subscription();
            $subscription->subscription_id = \App\Models\Subscription::generateSubscriptionId();
            $subscription->customer_id = $customer->id;
            $subscription->customer_name = $customerName;
            $subscription->customer_email = $request->customer_email;
            $subscription->customer_phone = $request->customer_phone;
            $subscription->product_id = $product->id;
            $subscription->product_name = $product->name;
            $subscription->product_price = $productPrice;
            $subscription->down_payment = $downPayment;
            $subscription->paid_amount = $downPayment; // Initially only down payment is paid
            $subscription->pending_amount = $pendingAmount;
            $subscription->discount_amount = $discountAmount;
            $subscription->status = 'active';
            $subscription->next_emi_date = $nextEmiDate;
            $subscription->start_date = $startDate;
            $subscription->emi_count = 0;
            $subscription->total_emis = $totalEmis;
            $subscription->emi_amount = $emiAmount;
            $subscription->billing_cycle = $request->billing_cycle;
            $subscription->notes = $request->description;
            $subscription->created_by = \Auth::user()->creatorId();
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Subscription created successfully.'),
                'subscription' => $subscription
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * Get subscription details
     */
    public function getSubscription($id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            return response()->json([
                'success' => true,
                'subscription' => $subscription
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong.')
            ], 500);
        }
    }
  
    public function deleteCoupon(Request $request)
    {
        $coupon = \App\Models\Coupon::find($request->coupon_id);

        if (!$coupon) {
            return response()->json(['success' => false, 'message' => 'Coupon not found']);
        }

        // Check if coupon belongs to current user
        if ($coupon->created_by != \Auth::user()->creatorId()) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        try {
            $coupon->delete();

            return response()->json([
                'success' => true,
                'message' => __('Coupon deleted successfully.'),
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Delete failed'], 500);

        }
    }

    public function cancelSubscription($id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->status = 'cancelled';
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Subscription cancelled successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to cancel subscription.')
            ], 500);
        }
    }

    /**
     * Update subscription notes
     */
    public function updateSubscriptionNotes(Request $request, $id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->notes = $request->notes;
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Notes updated successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update notes.')
            ], 500);
        }
    }

    /**
     * Update subscription
     */
    public function updateSubscription(Request $request, $id)
    {
        $rules = [
            'status' => 'required|in:active,cancelled,paused,expired',
            'next_emi_date' => 'nullable|date',
            'paid_amount' => 'nullable|numeric|min:0',
            'pending_amount' => 'nullable|numeric|min:0',
            'receipt_url' => 'nullable|url',
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->status = $request->status;
            if ($request->next_emi_date) {
                $subscription->next_emi_date = $request->next_emi_date;
            }
            if ($request->paid_amount !== null) {
                $subscription->paid_amount = $request->paid_amount;
            }
            if ($request->pending_amount !== null) {
                $subscription->pending_amount = $request->pending_amount;
            }
            if ($request->receipt_url) {
                $subscription->receipt_url = $request->receipt_url;
            }
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => __('Subscription updated successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update subscription.')
            ], 500);
        }
    }

    /**
     * Delete subscription
     */
    public function deleteSubscription($id)
    {
        try {
            $subscription = \App\Models\Subscription::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => __('Subscription not found.')
                ], 404);
            }

            $subscription->delete();

            return response()->json([
                'success' => true,
                'message' => __('Subscription deleted successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to delete subscription.')
            ], 500);
        }
    }

    // ==================== INSTALLMENT PLAN METHODS ====================

    /**
     * Store a new installment plan
     */
    public function storeInstallmentPlan(Request $request)
    {
        $rules = [
            'customer_id' => 'required',
            'customer_type' => 'required|in:customer,lead',
            'customer_email' => 'required|email',
            'product_id' => 'required|exists:product_services,id',
            'start_date' => 'required|date',
            'product_price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
            'down_payment' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'total_installments' => 'required|integer|min:1',
            'payment_frequency' => 'required|in:weekly,monthly,quarterly,yearly',
            'payment_method' => 'required|in:offline,online',
            'installments' => 'required|array|min:1',
            'installments.*.payment_date' => 'required|date',
            'installments.*.amount' => 'required|numeric|min:0',
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get customer details
            $customerType = $request->customer_type;
            $customerId = $request->customer_id;

            if ($customerType === 'customer') {
                $customer = \App\Models\Customer::find($customerId);
                $customerName = $customer ? $customer->name : 'Unknown Customer';
            } else {
                $lead = \App\Models\Lead::find($customerId);
                $customerName = $lead ? $lead->name : 'Unknown Lead';
            }

            // Get product details
            $product = \App\Models\ProductService::find($request->product_id);
            $productName = $product ? $product->name : 'Unknown Product';

            // Calculate amounts
            $productPrice = floatval($request->product_price);
            $quantity = intval($request->quantity);
            $downPayment = floatval($request->down_payment ?? 0);
            $discountAmount = floatval($request->discount_amount ?? 0);
            $totalAmount = ($productPrice * $quantity) - $discountAmount;
            $installmentTotal = $totalAmount - $downPayment;

            // Validate installment amounts
            $installments = $request->installments;
            $installmentSum = array_sum(array_column($installments, 'amount'));

            if (abs($installmentSum - $installmentTotal) > 0.01) {
                return response()->json([
                    'success' => false,
                    'message' => __('Total installment amounts must equal the remaining amount after down payment.')
                ], 422);
            }

            // Create installment plan
            $installmentPlan = new \App\Models\InstallmentPlan();
            $installmentPlan->plan_id = \App\Models\InstallmentPlan::generatePlanId();
            $installmentPlan->customer_id = $customerId;
            $installmentPlan->customer_name = $customerName;
            $installmentPlan->customer_email = $request->customer_email;
            $installmentPlan->customer_phone = $request->customer_phone;
            $installmentPlan->product_id = $request->product_id;
            $installmentPlan->product_name = $productName;
            $installmentPlan->product_price = $productPrice;
            $installmentPlan->quantity = $quantity;
            $installmentPlan->down_payment = $downPayment;
            $installmentPlan->paid_amount = $downPayment; // Down payment is considered as paid
            $installmentPlan->pending_amount = $installmentTotal;
            $installmentPlan->discount_amount = $discountAmount;
            $installmentPlan->total_amount = $totalAmount;
            $installmentPlan->status = 'active';
            $installmentPlan->start_date = $request->start_date;
            $installmentPlan->next_installment_date = $installments[0]['payment_date'] ?? null;
            $installmentPlan->total_installments = count($installments);
            $installmentPlan->paid_installments = 0;
            $installmentPlan->installment_amount = $installmentSum / count($installments); // Average amount
            $installmentPlan->payment_frequency = $request->payment_frequency;
            $installmentPlan->description = $request->description;
            $installmentPlan->payment_method = $request->payment_method;
            $installmentPlan->created_by = \Auth::user()->creatorId();
            $installmentPlan->save();

            // Create installment payments
            foreach ($installments as $index => $installment) {
                $payment = new \App\Models\InstallmentPayment();
                $payment->installment_plan_id = $installmentPlan->id;
                $payment->payment_id = \App\Models\InstallmentPayment::generatePaymentId();
                $payment->installment_number = $index + 1;
                $payment->due_date = $installment['payment_date'];
                $payment->amount = $installment['amount'];
                $payment->status = 'pending';
                $payment->save();
            }

            return response()->json([
                'success' => true,
                'message' => __('Installment plan created successfully.'),
                'installment_plan' => $installmentPlan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Something went wrong. Please try again.')
            ], 500);
        }
    }

    /**
     * Get installment plan details
     */
    public function getInstallmentPlan($id)
    {
        try {
            $installmentPlan = \App\Models\InstallmentPlan::with(['payments'])
                ->where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$installmentPlan) {
                return response()->json([
                    'success' => false,
                    'message' => __('Installment plan not found.')
                ], 404);
            }

            return response()->json([
                'success' => true,
                'installment_plan' => $installmentPlan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to fetch installment plan.')
            ], 500);
        }
    }

    /**
     * Cancel installment plan
     */
    public function cancelInstallmentPlan($id)
    {
        try {
            $installmentPlan = \App\Models\InstallmentPlan::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$installmentPlan) {
                return response()->json([
                    'success' => false,
                    'message' => __('Installment plan not found.')
                ], 404);
            }

            $installmentPlan->status = 'cancelled';
            $installmentPlan->save();

            // Update pending payments to cancelled
            $installmentPlan->payments()
                ->whereIn('status', ['pending', 'partial'])
                ->update(['status' => 'cancelled']);

            return response()->json([
                'success' => true,
                'message' => __('Installment plan cancelled successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to cancel installment plan.')
            ], 500);
        }
    }

    /**
     * Update installment plan notes
     */
    public function updateInstallmentPlanNotes(Request $request, $id)
    {
        $rules = [
            'notes' => 'nullable|string|max:1000'
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $installmentPlan = \App\Models\InstallmentPlan::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$installmentPlan) {
                return response()->json([
                    'success' => false,
                    'message' => __('Installment plan not found.')
                ], 404);
            }

            $installmentPlan->notes = $request->notes;
            $installmentPlan->save();

            return response()->json([
                'success' => true,
                'message' => __('Notes updated successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update notes.')
            ], 500);
        }
    }

    /**
     * Update installment plan
     */
    public function updateInstallmentPlan(Request $request, $id)
    {
        $rules = [
            'status' => 'required|in:active,cancelled,paused,completed,overdue',
            'next_installment_date' => 'nullable|date',
            'paid_amount' => 'nullable|numeric|min:0',
            'pending_amount' => 'nullable|numeric|min:0',
            'receipt_url' => 'nullable|url',
        ];

        $validator = \Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $installmentPlan = \App\Models\InstallmentPlan::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$installmentPlan) {
                return response()->json([
                    'success' => false,
                    'message' => __('Installment plan not found.')
                ], 404);
            }

            $installmentPlan->status = $request->status;
            if ($request->next_installment_date) {
                $installmentPlan->next_installment_date = $request->next_installment_date;
            }
            if ($request->paid_amount !== null) {
                $installmentPlan->paid_amount = $request->paid_amount;
            }
            if ($request->pending_amount !== null) {
                $installmentPlan->pending_amount = $request->pending_amount;
            }
            if ($request->receipt_url) {
                $installmentPlan->receipt_url = $request->receipt_url;
            }
            $installmentPlan->save();

            return response()->json([
                'success' => true,
                'message' => __('Installment plan updated successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update installment plan.')
            ], 500);
        }
    }

    /**
     * Delete installment plan
     */
    public function deleteInstallmentPlan($id)
    {
        try {
            $installmentPlan = \App\Models\InstallmentPlan::where('id', $id)
                ->where('created_by', \Auth::user()->creatorId())
                ->first();

            if (!$installmentPlan) {
                return response()->json([
                    'success' => false,
                    'message' => __('Installment plan not found.')
                ], 404);
            }

            $installmentPlan->delete();

            return response()->json([
                'success' => true,
                'message' => __('Installment plan deleted successfully.')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to delete installment plan.')
            ], 500);
        }
    }

    /**
     * Search contacts from both Leads and Customers tables
     */
    public function searchContacts(Request $request)
    {
        try {
            $search = $request->get('search', '');
            $contacts = collect();

            // Search in Customers table
            $customers = \App\Models\Customer::where('created_by', \Auth::user()->creatorId())
                ->where('is_active', 1)
                ->when($search, function ($query, $search) {
                    return $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('contact', 'like', "%{$search}%");
                    });
                })
                ->get()
                ->map(function ($customer) {
                    return [
                        'id' => $customer->id,
                        'type' => 'customer',
                        'name' => $customer->name,
                        'email' => $customer->email,
                        'phone' => $customer->contact,
                        'display_name' => $customer->name . ' (Customer)',
                        'display_info' => $customer->email . ($customer->contact ? ' | ' . $customer->contact : ''),
                    ];
                });

            // Search in Leads table
            $leads = \App\Models\Lead::where('created_by', \Auth::user()->creatorId())
                ->where('is_active', 1)
                ->where('is_converted', 0) // Only non-converted leads
                ->when($search, function ($query, $search) {
                    return $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%")
                          ->orWhere('phone', 'like', "%{$search}%");
                    });
                })
                ->get()
                ->map(function ($lead) {
                    return [
                        'id' => $lead->id,
                        'type' => 'lead',
                        'name' => $lead->name,
                        'email' => $lead->email,
                        'phone' => $lead->phone,
                        'display_name' => $lead->name . ' (Lead)',
                        'display_info' => $lead->email . ($lead->phone ? ' | ' . $lead->phone : ''),
                    ];
                });

            // Combine and sort results
            $contacts = $customers->concat($leads)->sortBy('name');

            return response()->json([
                'success' => true,
                'contacts' => $contacts->values()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to fetch contacts.')
            ], 500);
        }
    }

    /**
     * Get specific contact details by type and ID
     */
    public function getContactDetails($type, $id)
    {
        try {
            $contact = null;

            if ($type === 'customer') {
                $contact = \App\Models\Customer::where('id', $id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();

                if ($contact) {
                    $contactData = [
                        'id' => $contact->id,
                        'type' => 'customer',
                        'name' => $contact->name,
                        'email' => $contact->email,
                        'phone' => $contact->contact,
                        'emails' => array_filter([$contact->email]), // Can be extended for multiple emails
                        'phones' => array_filter([$contact->contact]), // Can be extended for multiple phones
                    ];
                }
            } elseif ($type === 'lead') {
                $contact = \App\Models\Lead::where('id', $id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->where('is_active', 1)
                    ->first();

                if ($contact) {
                    $contactData = [
                        'id' => $contact->id,
                        'type' => 'lead',
                        'name' => $contact->name,
                        'email' => $contact->email,
                        'phone' => $contact->phone,
                        'emails' => array_filter([$contact->email]), // Can be extended for multiple emails
                        'phones' => array_filter([$contact->phone]), // Can be extended for multiple phones
                    ];
                }
            }

            if (!$contact) {
                return response()->json([
                    'success' => false,
                    'message' => __('Contact not found.')
                ], 404);
            }

            return response()->json([
                'success' => true,
                'contact' => $contactData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to fetch contact details.')
            ], 500);
        }
        }

    /**
     * Toggle coupon status
     */
    public function toggleCouponStatus(Request $request)
    {
        $coupon = \App\Models\Coupon::find($request->coupon_id);

        if (!$coupon) {
            return response()->json(['success' => false, 'message' => 'Coupon not found']);
        }

        // Check if coupon belongs to current user
        if ($coupon->created_by != \Auth::user()->creatorId()) {
            return response()->json(['success' => false, 'message' => 'Permission denied'], 403);
        }

        try {
            $coupon->is_active = $request->is_active;
            $coupon->save();

            $statusText = $request->is_active ? __('activated') : __('deactivated');

            return response()->json([
                'success' => true,
                'message' => __('Coupon :status successfully.', ['status' => $statusText]),
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Status update failed'], 500);
        }
    }
}
