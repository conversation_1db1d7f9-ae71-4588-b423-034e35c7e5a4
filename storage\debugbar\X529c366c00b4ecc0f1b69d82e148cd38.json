{"__meta": {"id": "X529c366c00b4ecc0f1b69d82e148cd38", "datetime": "2025-07-31 12:15:40", "utime": 1753964140.026354, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964137.122165, "end": 1753964140.026404, "duration": 2.9042389392852783, "duration_str": "2.9s", "measures": [{"label": "Booting", "start": 1753964137.122165, "relative_start": 0, "end": **********.768502, "relative_end": **********.768502, "duration": 2.6463370323181152, "duration_str": "2.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.76853, "relative_start": 2.64636492729187, "end": 1753964140.026411, "relative_end": 7.152557373046875e-06, "duration": 0.25788116455078125, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936960, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1604\" onclick=\"\">app/Http/Controllers/FinanceController.php:1604-1663</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0328, "accumulated_duration_str": "32.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.917737, "duration": 0.03, "duration_str": "30ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 91.463}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9823692, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 91.463, "width_percent": 4.573}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9960709, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1630", "source": "app/Http/Controllers/FinanceController.php:1630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1630", "ajax": false, "filename": "FinanceController.php", "line": "1630"}, "connection": "radhe_same", "start_percent": 96.037, "width_percent": 3.963}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-143525391 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-143525391\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-571143317 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-571143317\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1992511477 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1992511477\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1088973624 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjVoVEkySVVOc0xoVktmVjNHTENBOEE9PSIsInZhbHVlIjoidlBuYkFSZS85MTBXR1dBQ21JTGI2SFc0cXZzcWNkWW5rd3F4T3g1M1NQd2xwbk9iVkRLTWlqb2o2N0o4aWtTZEM3MWhzK2JPNHVISUp1NlZ5QUVGQmJVZDQ5MHBlSnZMeVRpNGNqSWFscndsZi9DbjRlQWQ2N2sxMjdUSU9Cb0RkNi8wZTFaYk5wRUF5ZG9iUzArdXJpeWZDSEZHOFlQY2RqWEpGcm0xaHg3SjNQdVBVaVlJRW9BR29wdnB3QTJSc2h5c2hZbm1zcnhiS1poa2ZhYTQ3MmJ4bWdUUm5VeVNJOWFUZ2tvZGp5dHZuR0JicHEyZm1CY1Ftbld3QzB3d29LZ0RIK2YxMHhNdWZrdnl0OGlVTmpOWXhkTjZPVVhIeElSWERLaE8wN1h4MDc1OFpkVGxXWEYvekxEWkc2eVpITkhhRWZrdWJtTUVsL3BaMlhuaUFiT2Q0VmMvaDZSa1NKT01BZDFFK05EZVhybmQ0RVI3dmQxbHdiV29TZmJFOU9MaFgzbWNNZ24wdUE0YUROdE9qZWtEZitacTRNOU9MVStqcDVLRWFpQkg0MUthS3pLMFZxUEN3Q3V1OVpGWDdFM0w1eG9BNFp4bnkwZDZscDdNZ2tEZ3l3Y2xjeUovUjVxQlVtdkI5bjN5eDZEMkdnVUJ1VEVnVXNBY25NNmgiLCJtYWMiOiIyYTU4NTFiMWVjODNhMjkxNDBiM2ZkMmE1MTE2NDA3ZDMxMDgyZjQ2ZGQ4NTIwYTljN2EyMTY2ZjI0M2QwOWNmIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Inc2b1h4aHJWZlloYlZKNVZkNGRsU2c9PSIsInZhbHVlIjoiV0VtRVJ0OTV3djd5R0YwYmM5SU5oZUMyV3FoYWV4MllCaTlnV1BUQThNdlprdWNvM01XRXFLOUhRbHpLYlRMa1NQM0R5QWhGY1V2UnJxU0VZNStVVWhhaW4wQnhmNDE5b2NncXB1Z1NWbEVBRFhOVlhFY0g2aDRaRDBVU1VyaWthVVhlb3M5bHJ0R0cvSk5YWkJlQ041SG1ucTNRWlUyQkI0dUpoS0FqU1JUTEp3dHBubDdtYkw5QXYwaFlWTUhBMUNJVktTYW92TDExWE92MTdQUk8rdHlkSlZtK0hNMFZ6Nzd0L2N4ZW1yalFkb1dBa1ZybWJNN3VwNHJ3RmptR1FyNUZqVGtZOTZLNWVyd1BBeTdSQ0NrQmFjTVdyeEs2ellHV3pmdXdHWXpCRmFJUzExZ3VkZ1p5L0dDM1dNQ2k3R0JoWW1QbWVhYlVwejc4b0lpaVZTZ0tUZVJNMDhEWXR0ai85Q2FjclcvemlETkZ0Lzh5cWxZTzJSWEtWVXArUWdDWkhzZmtIeVkvU29UZGRaWXZhYmJaa2Z3UnVKcXQrZU1na2lvT3YvNkJ1WStLRk1rZ01uZTd3QkRDcG84RlRERkhRK3YvQXBqZ3NVWjhvK2xxMGNWV2JvSFFDYURzQVpDZlFjUEg1MU9uVjdQUjlQRTExU0xlWWR5U0JjNVkiLCJtYWMiOiI0MmZlNGZkZTA1ZTlhOGVhMGQ0ZmFjZWQxNjM0MDM2MjlhYTE3YThhZmRkZDIyZTYyOTQ3ZjM2MGUyM2RlZjhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088973624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-47476377 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47476377\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:15:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imt2N1pVYTY5UzNFekpjOG1ILy8ybmc9PSIsInZhbHVlIjoiUEFoV1lNN0dKU2ZKMHloMjhRZWNocXhkMk5xUnNTNkYwNDgrRElYQnVPWjJPbDhYeTdxbWVxcXgwUmE3aU1uTWNaWDhvaEVSYnA4Q0dlblh3NG03WGFkL0hMMUhaa3ExRXMwVS9WQjFQOFRIVGJYR1RjMW5NclFEd0VQQUU1TnU0K0FXcy9IeTgxTjd0UlBFMEVvTEcwczFjR1ovR3dJT1VDSVpIT0pYRW1HMXRvSVY4YUtObnVWdTdFTGdmV0lHT1JyNDNRNE1JVVdQaTBxUkxkc09xd2prNjQ4bWRBNHVzTERYK0VKT0FxLzdzdmRQcm9VSXVjOFV6NzlnZ1pwcUppNUVrQWxaUmt6UWlzbUEwMkxlaFkwdUFncjY3SnRSQzNLSXg5REdvUVlPN0FIbFRUSFhGQ3BFa1QwVlFLQkVxaXBqR1JuSXlqc1lsMm8xSDNyMjJ0WTh2RXluNm9OZld3ZG9VUGZsMFp4Wk5zQngycHQvNmpwNkZzMEpKUGRnajFLMGFwWmk5dmxhMmp4WHhtcndQMi9RRjFlY1dBOGdjaC9TUzZDbk1ZTTRPdXdad2JzRDFHS2t1NldOYVJTVDdZVnlOeUk5eTV4bkU4dlYwaGEvQXI4TWpsVW9tOWFYeEVmZG9JU0VtLzExQVlXQTBBRUVObVg2anJQdnorMloiLCJtYWMiOiJhYTQ0NTI5OTE0MDFlYmQ4NTM5MTg2NTRkMzM4OWY1ZjBkYzIzYWViOGQzMmU1ZmU3MzMyNmMzM2QwMmM0NTY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:15:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImlEMlZ6UW1vN3NOZFVOQ0ZjTW1maVE9PSIsInZhbHVlIjoiS21QdzFEVVF0L0FhZVlSVlQ3ekxoWjh1T0xqRlJrNUVNV0NzQmQvOFkxMG1HUDg5YjM1VFI2eEJLR2RmSVBzK3UycXN1bHdoT09yVGxQNzRUTVVzSDJucWJhbDdyb25oSUZldTlOS0pKQXVqVVJHUjZqMXNBUnJubkhQWGtDWEpMVU5sUU16NGR4ZDh2eWJyV1YwbDJLTVpJektBWThzcEpxbGZ4UFNTeUdGbFBJSEVmb0hzRWVYcVNhWmc3WmJ4bXdQUGtTR0E3MzRpOGFXRTRtaXRFaFFPc00wVGNZeW9mSitqYUpRQ3NQc1ArL0tFNkRsNVZyOS9LRUY1T3gzZzkzbjU3SngyNGtJNTBkTlZRZmNtWWJZY2dwN05UOUdBemJFcDZRd1pyLzFOT0RMWnhTM3FzOGZ1K1pnRm5LVWIrSk8xOWRKeGE3S1hWR3laV1I2QkJGY2lnRUFWQVk5SnZ4V1NJL3liUnhWMSt0NXJWTzZwMGcwTmZseXFRc1lya3VIY0xFajBMZFlNZ2dVUWZlSUxoanRvS01vWWRIQUZkOTdXekJqY0VHd3hhenBMbkxPalZBTTF1VnFzL3I1dlE2SU9xSzBOUjdKOWhtYVVrY0VKaUlxa3ArNVVkTFRSbk1BV21IdTZYVjBWTnRoRFprMjRha2tqWjRTdE8zZFEiLCJtYWMiOiIyZmZlNjZkNTMxY2RiOWJmYTJlZDIzMTM0MTY2NGQyYjgxYzkyM2Q4MmMzYzRjNWE3ZGRjYWNjMjUxYmI0ZGY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:15:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imt2N1pVYTY5UzNFekpjOG1ILy8ybmc9PSIsInZhbHVlIjoiUEFoV1lNN0dKU2ZKMHloMjhRZWNocXhkMk5xUnNTNkYwNDgrRElYQnVPWjJPbDhYeTdxbWVxcXgwUmE3aU1uTWNaWDhvaEVSYnA4Q0dlblh3NG03WGFkL0hMMUhaa3ExRXMwVS9WQjFQOFRIVGJYR1RjMW5NclFEd0VQQUU1TnU0K0FXcy9IeTgxTjd0UlBFMEVvTEcwczFjR1ovR3dJT1VDSVpIT0pYRW1HMXRvSVY4YUtObnVWdTdFTGdmV0lHT1JyNDNRNE1JVVdQaTBxUkxkc09xd2prNjQ4bWRBNHVzTERYK0VKT0FxLzdzdmRQcm9VSXVjOFV6NzlnZ1pwcUppNUVrQWxaUmt6UWlzbUEwMkxlaFkwdUFncjY3SnRSQzNLSXg5REdvUVlPN0FIbFRUSFhGQ3BFa1QwVlFLQkVxaXBqR1JuSXlqc1lsMm8xSDNyMjJ0WTh2RXluNm9OZld3ZG9VUGZsMFp4Wk5zQngycHQvNmpwNkZzMEpKUGRnajFLMGFwWmk5dmxhMmp4WHhtcndQMi9RRjFlY1dBOGdjaC9TUzZDbk1ZTTRPdXdad2JzRDFHS2t1NldOYVJTVDdZVnlOeUk5eTV4bkU4dlYwaGEvQXI4TWpsVW9tOWFYeEVmZG9JU0VtLzExQVlXQTBBRUVObVg2anJQdnorMloiLCJtYWMiOiJhYTQ0NTI5OTE0MDFlYmQ4NTM5MTg2NTRkMzM4OWY1ZjBkYzIzYWViOGQzMmU1ZmU3MzMyNmMzM2QwMmM0NTY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:15:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImlEMlZ6UW1vN3NOZFVOQ0ZjTW1maVE9PSIsInZhbHVlIjoiS21QdzFEVVF0L0FhZVlSVlQ3ekxoWjh1T0xqRlJrNUVNV0NzQmQvOFkxMG1HUDg5YjM1VFI2eEJLR2RmSVBzK3UycXN1bHdoT09yVGxQNzRUTVVzSDJucWJhbDdyb25oSUZldTlOS0pKQXVqVVJHUjZqMXNBUnJubkhQWGtDWEpMVU5sUU16NGR4ZDh2eWJyV1YwbDJLTVpJektBWThzcEpxbGZ4UFNTeUdGbFBJSEVmb0hzRWVYcVNhWmc3WmJ4bXdQUGtTR0E3MzRpOGFXRTRtaXRFaFFPc00wVGNZeW9mSitqYUpRQ3NQc1ArL0tFNkRsNVZyOS9LRUY1T3gzZzkzbjU3SngyNGtJNTBkTlZRZmNtWWJZY2dwN05UOUdBemJFcDZRd1pyLzFOT0RMWnhTM3FzOGZ1K1pnRm5LVWIrSk8xOWRKeGE3S1hWR3laV1I2QkJGY2lnRUFWQVk5SnZ4V1NJL3liUnhWMSt0NXJWTzZwMGcwTmZseXFRc1lya3VIY0xFajBMZFlNZ2dVUWZlSUxoanRvS01vWWRIQUZkOTdXekJqY0VHd3hhenBMbkxPalZBTTF1VnFzL3I1dlE2SU9xSzBOUjdKOWhtYVVrY0VKaUlxa3ArNVVkTFRSbk1BV21IdTZYVjBWTnRoRFprMjRha2tqWjRTdE8zZFEiLCJtYWMiOiIyZmZlNjZkNTMxY2RiOWJmYTJlZDIzMTM0MTY2NGQyYjgxYzkyM2Q4MmMzYzRjNWE3ZGRjYWNjMjUxYmI0ZGY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:15:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}