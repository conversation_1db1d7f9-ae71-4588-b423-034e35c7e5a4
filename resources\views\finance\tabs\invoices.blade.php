<!-- Invoices Tab Content -->
<style>
.product-row {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.invoice-table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.invoice-table td {
    vertical-align: middle;
}

.modal-xl {
    max-width: 1200px;
}

#toast-container .toast {
    margin-bottom: 0.5rem;
}

.table-responsive {
    border-radius: 0.375rem;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

.invoice-table {
    min-width: 1200px;
}

.invoice-table th,
.invoice-table td {
    white-space: nowrap;
    min-width: 120px;
}

.invoice-table th:first-child,
.invoice-table td:first-child {
    min-width: 80px;
}

.invoice-table th:last-child,
.invoice-table td:last-child {
    min-width: 150px;
}

.badge {
    font-size: 0.75rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.selected-contact-info {
    border-left: 3px solid #28a745;
    background-color: #f8f9fa !important;
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.text-muted {
    font-size: 0.75rem;
}
</style>
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Invoice Management') }}</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-info active" data-invoice-view="invoices">
                    <i class="ti ti-file-invoice me-1"></i>{{ __('Invoices') }}
                </button>
                <button type="button" class="btn btn-outline-info" data-invoice-view="quotes">
                    <i class="ti ti-file-description me-1"></i>{{ __('Quotes') }}
                </button>
                <button type="button" class="btn btn-outline-info" data-invoice-view="setup">
                    <i class="ti ti-settings me-1"></i>{{ __('Setup') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Invoices View -->
<div id="invoices-view" class="invoice-view active">
    <div class="row mb-4">
        <!-- Invoice Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->count() }}</h4>
                    <small>{{ __('Total Invoices') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->where('status', 4)->count() }}</h4>
                    <small>{{ __('Paid Invoices') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->where('status', 1)->count() }}</h4>
                    <small>{{ __('Pending Invoices') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock-exclamation" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->where('due_date', '<', now())->whereNotIn('status', [0, 4])->count() }}</h4>
                    <small>{{ __('Overdue Invoices') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Recent Invoices') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create invoice')
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createInvoiceModal">
                                <i class="ti ti-plus me-1"></i>{{ __('Create Invoice') }}
                            </button>
                        @endcan
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover invoice-table" id="invoicesTable">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Status / Invoice Number') }}</th>
                                    <th>{{ __('Created At / Active Date') }}</th>
                                    <th>{{ __('Customer Name / Email / Phone') }}</th>
                                    <th>{{ __('Product') }}</th>
                                    <th>{{ __('Total Amount / Paid Amount') }}</th>
                                    <th>{{ __('Due Amount / Discount Amount') }}</th>
                                    <th>{{ __('Payment Method') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody id="invoicesTableBody">
                                @php
                                    $invoices = \App\Models\Invoice::with(['customer', 'items.product', 'payments'])
                                        ->where('created_by', \Auth::user()->creatorId())
                                        ->latest()
                                        ->limit(5)
                                        ->get();
                                @endphp
                                @forelse($invoices as $invoice)
                                <tr data-invoice-id="{{ $invoice->id }}">
                                    <td>
                                        <span class="fw-bold">#{{ $invoice->id }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="d-flex align-items-center gap-2">
                                                @if($invoice->status == 0)
                                                    <span class="badge bg-secondary">{{ __('Draft') }}</span>
                                                @elseif($invoice->status == 1)
                                                    <span class="badge bg-warning">{{ __('Sent') }}</span>
                                                @elseif($invoice->status == 2)
                                                    <span class="badge bg-info">{{ __('Unpaid') }}</span>
                                                @elseif($invoice->status == 3)
                                                    <span class="badge bg-warning">{{ __('Partially Paid') }}</span>
                                                @elseif($invoice->status == 4)
                                                    <span class="badge bg-success">{{ __('Paid') }}</span>
                                                @endif
                                            </div>
                                            <a href="{{ route('invoice.show', $invoice->id) }}" class="text-decoration-none small">
                                                {{ \App\Models\Invoice::invoiceNumberFormat($invoice->invoice_id) }}
                                            </a>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium">{{ \Auth::user()->dateFormat($invoice->issue_date) }}</span>
                                            <small class="text-muted">{{ __('Due') }}: {{ \Auth::user()->dateFormat($invoice->due_date) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium">{{ !empty($invoice->customer) ? $invoice->customer->name : 'N/A' }}</span>
                                            <small class="text-muted">{{ !empty($invoice->customer) ? $invoice->customer->email : '' }}</small>
                                            <small class="text-muted">{{ !empty($invoice->customer) ? $invoice->customer->contact : '' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            @if($invoice->items->count() > 0)
                                                @foreach($invoice->items->take(2) as $item)
                                                    <small>{{ $item->product->name ?? 'N/A' }}</small>
                                                @endforeach
                                                @if($invoice->items->count() > 2)
                                                    <small class="text-muted">+{{ $invoice->items->count() - 2 }} {{ __('more') }}</small>
                                                @endif
                                            @else
                                                <small class="text-muted">{{ __('No products') }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium">{{ \Auth::user()->priceFormat($invoice->getTotal()) }}</span>
                                            <small class="text-success">{{ __('Paid') }}: {{ \Auth::user()->priceFormat($invoice->payments->sum('amount')) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium text-danger">{{ \Auth::user()->priceFormat($invoice->getDue()) }}</span>
                                            <small class="text-muted">{{ __('Discount') }}: {{ \Auth::user()->priceFormat($invoice->getTotalDiscount()) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        @if($invoice->payments->count() > 0)
                                            <span class="badge bg-light text-dark">{{ $invoice->payments->last()->payment_method ?? 'N/A' }}</span>
                                        @else
                                            <span class="text-muted">{{ __('No Payment') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('invoice.show', $invoice->id) }}" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            @can('edit invoice')
                                                <a href="{{ route('invoice.edit', $invoice->id) }}" class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                            @endcan
                                            @can('delete invoice')
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-invoice"
                                                        data-invoice-id="{{ $invoice->id }}"
                                                        title="{{ __('Delete') }}">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr id="noInvoicesRow">
                                    <td colspan="9" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-file-invoice" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Invoices Found') }}</h5>
                                            <p>{{ __('Create your first invoice to get started') }}</p>
                                            @can('create invoice')
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInvoiceModal">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Create Invoice') }}
                                                </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quotes View -->
<div id="quotes-view" class="invoice-view" style="display: none;">
    <div class="row mb-4">
        <!-- Quote Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->count() }}</h4>
                    <small>{{ __('Total Quotes') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->where('status', 4)->count() }}</h4>
                    <small>{{ __('Accepted Quotes') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->where('status', 1)->count() }}</h4>
                    <small>{{ __('Pending Quotes') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->where('status', 3)->count() }}</h4>
                    <small>{{ __('Rejected Quotes') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Recent Quotes') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create proposal')
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createQuoteModal">
                                <i class="ti ti-plus me-1"></i>{{ __('Create Quote') }}
                            </button>
                        @endcan
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover invoice-table" id="quotesTable">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Quote Number') }}</th>
                                    <th>{{ __('Created At') }}</th>
                                    <th>{{ __('Customer Name') }}</th>
                                    <th>{{ __('Customer Email') }}</th>
                                    <th>{{ __('Total Amount') }}</th>
                                    <th>{{ __('GST Number') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody id="quotesTableBody">
                                @php
                                    $proposals = \App\Models\Proposal::with(['customer'])
                                        ->where('created_by', \Auth::user()->creatorId())
                                        ->latest()
                                        ->limit(5)
                                        ->get();
                                @endphp
                                @forelse($proposals as $proposal)
                                <tr data-proposal-id="{{ $proposal->id }}">
                                    <td>
                                        <span class="fw-bold">#{{ $proposal->id }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('proposal.show', $proposal->id) }}" class="text-decoration-none">
                                            {{ \App\Models\Proposal::proposalNumberFormat($proposal->proposal_id) }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ \Auth::user()->dateFormat($proposal->issue_date) }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ !empty($proposal->customer) ? $proposal->customer->name : 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ !empty($proposal->customer) ? $proposal->customer->email : 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">{{ \Auth::user()->priceFormat($proposal->getTotal()) }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ !empty($proposal->customer) ? ($proposal->customer->tax_number ?? 'N/A') : 'N/A' }}</span>
                                    </td>
                                    <td>
                                        @if($proposal->status == 0)
                                            <span class="badge bg-secondary">{{ __('Draft') }}</span>
                                        @elseif($proposal->status == 1)
                                            <span class="badge bg-warning">{{ __('Open') }}</span>
                                        @elseif($proposal->status == 2)
                                            <span class="badge bg-info">{{ __('Sent') }}</span>
                                        @elseif($proposal->status == 3)
                                            <span class="badge bg-danger">{{ __('Rejected') }}</span>
                                        @elseif($proposal->status == 4)
                                            <span class="badge bg-success">{{ __('Accepted') }}</span>
                                        @endif
                                        @if($proposal->is_convert == 1)
                                            <br><small class="text-success">{{ __('Converted') }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('proposal.show', $proposal->id) }}" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            @can('edit proposal')
                                                <button type="button" class="btn btn-sm btn-outline-secondary edit-quote"
                                                        data-proposal-id="{{ $proposal->id }}"
                                                        title="{{ __('Edit') }}">
                                                    <i class="ti ti-edit"></i>
                                                </button>
                                            @endcan
                                            @can('create invoice')
                                                @if($proposal->is_convert != 1)
                                                    <button type="button" class="btn btn-sm btn-outline-success convert-to-invoice"
                                                            data-proposal-id="{{ $proposal->id }}"
                                                            title="{{ __('Convert to Invoice') }}">
                                                        <i class="ti ti-file-invoice"></i>
                                                    </button>
                                                @endif
                                            @endcan
                                            @can('delete proposal')
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-quote"
                                                        data-proposal-id="{{ $proposal->id }}"
                                                        title="{{ __('Delete') }}">
                                                    <i class="ti ti-trash"></i>
                                                </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr id="noQuotesRow">
                                    <td colspan="9" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-file-description" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Quotes Found') }}</h5>
                                            <p>{{ __('Create your first quote to get started') }}</p>
                                            @can('create proposal')
                                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createQuoteModal">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Create Quote') }}
                                                </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Setup View -->
<div id="setup-view" class="invoice-view" style="display: none;">
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Invoice Settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('taxes.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-receipt-tax me-2"></i>{{ __('Tax Settings') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('product-category.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-category me-2"></i>{{ __('Product Categories') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('product-unit.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-ruler me-2"></i>{{ __('Product Units') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('settings') }}#payment-settings" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-credit-card me-2"></i>{{ __('Payment Methods') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Template Settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('print.setting') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-printer me-2"></i>{{ __('Print Settings') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-template me-2"></i>{{ __('Invoice Templates') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-mail me-2"></i>{{ __('Email Templates') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('custom-field.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-forms me-2"></i>{{ __('Custom Fields') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Invoice Modal -->
<div class="modal fade" id="createInvoiceModal" tabindex="-1" aria-labelledby="createInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createInvoiceModalLabel">{{ __('Create Invoice') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createInvoiceForm">
                @csrf
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{ __('Customer Details') }}</h6>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="customer_name" class="form-label">{{ __('Customer Name') }} <span class="text-danger">*</span></label>
                                    <select class="form-select" id="customer_name" name="customer_id" required>
                                        <option value="">{{ __('Select Customer') }}</option>
                                    </select>
                                    <small class="text-muted">{{ __('Select from Customers, Leads, or Clients') }}</small>
                                </div>
                                <div class="col-md-4">
                                    <label for="customer_email" class="form-label">{{ __('Customer Email') }}</label>
                                    <select class="form-select" id="customer_email" name="customer_email">
                                        <option value="">{{ __('Select Email') }}</option>
                                    </select>
                                    <small class="text-muted">{{ __('Auto-populated when customer is selected') }}</small>
                                </div>
                                <div class="col-md-4">
                                    <label for="customer_contact" class="form-label">{{ __('Customer Contact') }}</label>
                                    <select class="form-select" id="customer_contact" name="customer_contact">
                                        <option value="">{{ __('Select Contact') }}</option>
                                    </select>
                                    <small class="text-muted">{{ __('Auto-populated when customer is selected') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{ __('Product Details') }}</h6>
                                <button type="button" class="btn btn-success btn-sm" id="addProductBtn">
                                    {{ __('Add') }}
                                </button>
                            </div>
                            <div id="productRows">
                                <div class="row product-row mb-3">
                                    <div class="col-md-4">
                                        <label for="product_0" class="form-label">{{ __('Product') }}</label>
                                        <select class="form-select product-select" id="product_0" name="items[0][product_id]" required>
                                            <option value="">{{ __('Select Product') }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="product_price_0" class="form-label">{{ __('Product Price') }}</label>
                                        <input type="number" class="form-control product-price" id="product_price_0"
                                               name="items[0][price]" placeholder="{{ __('Product Price') }}" step="0.01" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="product_qty_0" class="form-label">{{ __('Product Qty') }}</label>
                                        <input type="number" class="form-control product-qty" id="product_qty_0"
                                               name="items[0][quantity]" value="1" min="1" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="product_total_0" class="form-label">{{ __('Total') }}</label>
                                        <input type="text" class="form-control product-total" id="product_total_0" readonly>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="button" class="btn btn-danger btn-sm remove-product" style="display: none;">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">{{ __('Payment Details') }}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="invoice_date" class="form-label">{{ __('Invoice Date') }}</label>
                                    <input type="date" class="form-control" id="invoice_date" name="issue_date"
                                           value="{{ date('Y-m-d') }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="due_date" class="form-label">{{ __('Due Date') }}</label>
                                    <input type="date" class="form-control" id="due_date" name="due_date" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label for="invoice_description" class="form-label">{{ __('Invoice Description') }}</label>
                                    <textarea class="form-control" id="invoice_description" name="description"
                                              rows="3" placeholder="{{ __('Invoice Description') }}"></textarea>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label class="form-label">{{ __('Payment Method') }}</label>
                                    <div class="d-flex gap-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method"
                                                   id="payment_offline" value="0" checked>
                                            <label class="form-check-label" for="payment_offline">
                                                {{ __('Offline') }}
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method"
                                                   id="payment_online" value="1">
                                            <label class="form-check-label" for="payment_online">
                                                {{ __('Online') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cash_collection" name="cash_collection">
                                        <label class="form-check-label" for="cash_collection">
                                            {{ __("I hereby declare that I'm collecting the cash on the behalf of the organisation.") }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>{{ __('Invoice Summary') }}</h6>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <div class="d-flex justify-content-between">
                                                <span>{{ __('Subtotal') }}:</span>
                                                <span id="invoice_subtotal">{{ \Auth::user()->priceFormat(0) }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>{{ __('Tax') }}:</span>
                                                <span id="invoice_tax">{{ \Auth::user()->priceFormat(0) }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between fw-bold">
                                                <span>{{ __('Total') }}:</span>
                                                <span id="invoice_total">{{ \Auth::user()->priceFormat(0) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="saveInvoiceBtn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        {{ __('Create Invoice') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Quote Modal -->
<div class="modal fade" id="createQuoteModal" tabindex="-1" aria-labelledby="createQuoteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createQuoteModalLabel">{{ __('Create Quote Invoice') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createQuoteForm">
                @csrf
                <div class="modal-body">
                    <!-- Customer Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{ __('Customer Details') }}</h6>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="quote_customer_name" class="form-label">{{ __('Customer Name') }} <span class="text-danger">*</span></label>
                                    <select class="form-select" id="quote_customer_name" name="customer_id" required>
                                        <option value="">{{ __('Select Customer') }}</option>
                                    </select>
                                    <small class="text-muted">{{ __('Select from Customers, Leads, or Clients') }}</small>
                                </div>
                                <div class="col-md-4">
                                    <label for="quote_customer_email" class="form-label">{{ __('Customer Email') }}</label>
                                    <select class="form-select" id="quote_customer_email" name="customer_email">
                                        <option value="">{{ __('Select Email') }}</option>
                                    </select>
                                    <small class="text-muted">{{ __('Auto-populated when customer is selected') }}</small>
                                </div>
                                <div class="col-md-4">
                                    <label for="quote_customer_contact" class="form-label">{{ __('Customer Contact') }}</label>
                                    <select class="form-select" id="quote_customer_contact" name="customer_contact">
                                        <option value="">{{ __('Select Contact') }}</option>
                                    </select>
                                    <small class="text-muted">{{ __('Auto-populated when customer is selected') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Details Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">{{ __('Product Details') }}</h6>
                                <button type="button" class="btn btn-success btn-sm" id="addQuoteProductBtn">
                                    {{ __('Add') }}
                                </button>
                            </div>
                            <div id="quoteProductRows">
                                <div class="row quote-product-row mb-3">
                                    <div class="col-md-4">
                                        <label for="quote_product_0" class="form-label">{{ __('Product') }}</label>
                                        <select class="form-select quote-product-select" id="quote_product_0" name="items[0][product_id]" required>
                                            <option value="">{{ __('Select Product') }}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="quote_product_price_0" class="form-label">{{ __('Product Price') }}</label>
                                        <input type="number" class="form-control quote-product-price" id="quote_product_price_0"
                                               name="items[0][price]" placeholder="{{ __('Product Price') }}" step="0.01" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="quote_product_qty_0" class="form-label">{{ __('Product Qty') }}</label>
                                        <input type="number" class="form-control quote-product-qty" id="quote_product_qty_0"
                                               name="items[0][quantity]" value="1" min="1" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="quote_product_total_0" class="form-label">{{ __('Total') }}</label>
                                        <input type="text" class="form-control quote-product-total" id="quote_product_total_0" readonly>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="button" class="btn btn-danger btn-sm remove-quote-product" style="display: none;">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quote Description Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">{{ __('Quote Description') }}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="quote_issue_date" class="form-label">{{ __('Issue Date') }}</label>
                                    <input type="date" class="form-control" id="quote_issue_date" name="issue_date"
                                           value="{{ date('Y-m-d') }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="quote_valid_until" class="form-label">{{ __('Valid Until') }}</label>
                                    <input type="date" class="form-control" id="quote_valid_until" name="valid_until">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label for="quote_description" class="form-label">{{ __('Quote Description') }}</label>
                                    <textarea class="form-control" id="quote_description" name="description"
                                              rows="3" placeholder="{{ __('Quote Description') }}"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>{{ __('Quote Summary') }}</h6>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <div class="d-flex justify-content-between">
                                                <span>{{ __('Subtotal') }}:</span>
                                                <span id="quote_subtotal">{{ \Auth::user()->priceFormat(0) }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span>{{ __('Tax') }}:</span>
                                                <span id="quote_tax">{{ \Auth::user()->priceFormat(0) }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between fw-bold">
                                                <span>{{ __('Total') }}:</span>
                                                <span id="quote_total">{{ \Auth::user()->priceFormat(0) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="saveQuoteBtn">
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        {{ __('Submit') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Invoice view switching
    const invoiceViewButtons = document.querySelectorAll('[data-invoice-view]');
    const invoiceViews = document.querySelectorAll('.invoice-view');
    
    invoiceViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-invoice-view');
            
            // Remove active class from all buttons
            invoiceViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all views
            invoiceViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });

    // Invoice Management JavaScript
    let productRowIndex = 1;
    let customers = [];
    let products = [];

    // Load customers and products on modal open
    $('#createInvoiceModal').on('show.bs.modal', function() {
        // Show loading state
        $('#customer_name, #customer_email, #customer_contact').html('<option value="">{{ __("Loading...") }}</option>');
        $('.product-select').html('<option value="">{{ __("Loading...") }}</option>');

        loadCustomers();
        loadProducts();
        setDefaultDueDate();
    });

    // Load customers for dropdown
    function loadCustomers() {
        $.ajax({
            url: '{{ route("invoice.customers.dropdown") }}',
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    customers = response.data;
                    populateCustomerDropdowns();
                    if (customers.length === 0) {
                        showToast('{{ __("No customers found. Please add customers first.") }}', 'error');
                    }
                }
            },
            error: function() {
                showToast('{{ __("Error loading customers") }}', 'error');
            }
        });
    }

    // Load products for dropdown
    function loadProducts() {
        $.ajax({
            url: '{{ route("invoice.products.dropdown") }}',
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    products = response.data;
                    populateProductDropdowns();
                    if (products.length === 0) {
                        showToast('{{ __("No products found. Please add products first.") }}', 'error');
                    }
                }
            },
            error: function() {
                showToast('{{ __("Error loading products") }}', 'error');
            }
        });
    }

    // Populate customer dropdowns
    function populateCustomerDropdowns() {
        const nameSelect = $('#customer_name');
        const emailSelect = $('#customer_email');
        const contactSelect = $('#customer_contact');

        // Clear existing options
        nameSelect.html('<option value="">{{ __("Select Customer") }}</option>');
        emailSelect.html('<option value="">{{ __("Select Email") }}</option>');
        contactSelect.html('<option value="">{{ __("Select Contact") }}</option>');

        // Populate with customer data
        customers.forEach(contact => {
            // Name dropdown - show all contacts
            nameSelect.append(`<option value="${contact.id}" data-type="${contact.type}">${contact.display_name}</option>`);

            // Email dropdown - only show contacts with email
            if (contact.email) {
                emailSelect.append(`<option value="${contact.id}" data-type="${contact.type}" data-email="${contact.email}">${contact.email} (${contact.name})</option>`);
            }

            // Contact dropdown - only show contacts with phone
            if (contact.contact) {
                contactSelect.append(`<option value="${contact.id}" data-type="${contact.type}" data-contact="${contact.contact}">${contact.contact} (${contact.name})</option>`);
            }
        });
    }

    // Populate product dropdowns
    function populateProductDropdowns() {
        $('.product-select').each(function() {
            const select = $(this);
            select.html('<option value="">{{ __("Select Product") }}</option>');

            products.forEach(product => {
                select.append(`<option value="${product.id}" data-price="${product.sale_price}">${product.name}</option>`);
            });
        });
    }

    // Customer dropdown synchronization with auto-population
    $('#customer_name, #customer_email, #customer_contact').on('change', function() {
        const selectedContactId = $(this).val();
        const fieldType = $(this).attr('id');

        if (selectedContactId) {
            // Get contact details and auto-populate other fields
            $.ajax({
                url: '{{ route("invoice.contact.details") }}',
                type: 'GET',
                data: { contact_id: selectedContactId },
                success: function(response) {
                    if (response.status) {
                        const contact = response.data;

                        // Find the contact in our customers array for additional info
                        const fullContact = customers.find(c => c.id === selectedContactId);

                        if (fullContact) {
                            // Update all dropdowns to show the same contact
                            $('#customer_name').val(selectedContactId);

                            // Auto-populate email dropdown if contact has email
                            if (fullContact.email) {
                                $('#customer_email').val(selectedContactId);
                            } else {
                                $('#customer_email').val('');
                            }

                            // Auto-populate contact dropdown if contact has phone
                            if (fullContact.contact) {
                                $('#customer_contact').val(selectedContactId);
                            } else {
                                $('#customer_contact').val('');
                            }

                            // Show selected contact info
                            showSelectedContactInfo(fullContact);
                        }
                    }
                },
                error: function() {
                    showToast('{{ __("Error loading contact details") }}', 'error');
                }
            });
        } else {
            // Clear all fields if no selection
            $('#customer_name, #customer_email, #customer_contact').val('');
            hideSelectedContactInfo();
        }
    });

    // Show selected contact information
    function showSelectedContactInfo(contact) {
        // Remove existing info if any
        $('.selected-contact-info').remove();

        const infoHtml = `
            <div class="selected-contact-info mt-2 p-2 bg-light rounded">
                <small class="text-muted">
                    <strong>{{ __('Selected Contact') }}:</strong> ${contact.name} (${contact.type})<br>
                    ${contact.email ? '<strong>{{ __("Email") }}:</strong> ' + contact.email + '<br>' : ''}
                    ${contact.contact ? '<strong>{{ __("Phone") }}:</strong> ' + contact.contact : ''}
                </small>
            </div>
        `;

        $('#customer_contact').closest('.col-md-4').append(infoHtml);
    }

    // Hide selected contact information
    function hideSelectedContactInfo() {
        $('.selected-contact-info').remove();
    }

    // Product selection handler
    $(document).on('change', '.product-select', function() {
        const selectedOption = $(this).find('option:selected');
        const price = selectedOption.data('price') || 0;
        const row = $(this).closest('.product-row');

        row.find('.product-price').val(price);
        calculateRowTotal(row);
    });

    // Quantity and price change handlers
    $(document).on('input', '.product-qty, .product-price', function() {
        const row = $(this).closest('.product-row');
        calculateRowTotal(row);
    });

    // Calculate row total
    function calculateRowTotal(row) {
        const qty = parseFloat(row.find('.product-qty').val()) || 0;
        const price = parseFloat(row.find('.product-price').val()) || 0;
        const total = qty * price;

        row.find('.product-total').val(total.toFixed(2));
        calculateInvoiceTotal();
    }

    // Calculate invoice total
    function calculateInvoiceTotal() {
        let subtotal = 0;

        $('.product-total').each(function() {
            subtotal += parseFloat($(this).val()) || 0;
        });

        const tax = 0; // You can implement tax calculation here
        const total = subtotal + tax;

        $('#invoice_subtotal').text(formatCurrency(subtotal));
        $('#invoice_tax').text(formatCurrency(tax));
        $('#invoice_total').text(formatCurrency(total));
    }

    // Add product row
    $('#addProductBtn').on('click', function() {
        const newRow = `
            <div class="row product-row mb-3">
                <div class="col-md-4">
                    <label for="product_${productRowIndex}" class="form-label">{{ __('Product') }}</label>
                    <select class="form-select product-select" id="product_${productRowIndex}" name="items[${productRowIndex}][product_id]" required>
                        <option value="">{{ __('Select Product') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="product_price_${productRowIndex}" class="form-label">{{ __('Product Price') }}</label>
                    <input type="number" class="form-control product-price" id="product_price_${productRowIndex}"
                           name="items[${productRowIndex}][price]" placeholder="{{ __('Product Price') }}" step="0.01" required>
                </div>
                <div class="col-md-2">
                    <label for="product_qty_${productRowIndex}" class="form-label">{{ __('Product Qty') }}</label>
                    <input type="number" class="form-control product-qty" id="product_qty_${productRowIndex}"
                           name="items[${productRowIndex}][quantity]" value="1" min="1" required>
                </div>
                <div class="col-md-2">
                    <label for="product_total_${productRowIndex}" class="form-label">{{ __('Total') }}</label>
                    <input type="text" class="form-control product-total" id="product_total_${productRowIndex}" readonly>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-danger btn-sm remove-product">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        `;

        $('#productRows').append(newRow);
        populateProductDropdowns();
        updateRemoveButtons();
        productRowIndex++;
    });

    // Remove product row
    $(document).on('click', '.remove-product', function() {
        $(this).closest('.product-row').remove();
        updateRemoveButtons();
        calculateInvoiceTotal();
    });

    // Update remove button visibility
    function updateRemoveButtons() {
        const rows = $('.product-row');
        if (rows.length > 1) {
            $('.remove-product').show();
        } else {
            $('.remove-product').hide();
        }
    }

    // Set default due date (30 days from issue date)
    function setDefaultDueDate() {
        const issueDate = new Date($('#invoice_date').val());
        const dueDate = new Date(issueDate);
        dueDate.setDate(dueDate.getDate() + 30);

        $('#due_date').val(dueDate.toISOString().split('T')[0]);
    }

    // Update due date when issue date changes
    $('#invoice_date').on('change', function() {
        setDefaultDueDate();
    });

    // Format currency
    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    // Show toast notification
    function showToast(message, type = 'success') {
        // You can implement your preferred toast notification here
        if (type === 'success') {
            alert('Success: ' + message);
        } else {
            alert('Error: ' + message);
        }
    }

    // Form submission handler
    $('#createInvoiceForm').on('submit', function(e) {
        e.preventDefault();

        const submitBtn = $('#saveInvoiceBtn');
        const spinner = submitBtn.find('.spinner-border');

        // Show loading state
        submitBtn.prop('disabled', true);
        spinner.removeClass('d-none');

        // Prepare form data
        const formData = new FormData(this);

        // Add category_id (you might want to make this dynamic)
        formData.append('category_id', 1);

        $.ajax({
            url: '{{ route("invoice.store.ajax") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.status) {
                    showToast(response.message, 'success');
                    $('#createInvoiceModal').modal('hide');
                    addInvoiceToTable(response.invoice);
                    resetForm();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function(xhr) {
                let message = 'An error occurred. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showToast(message, 'error');
            },
            complete: function() {
                // Hide loading state
                submitBtn.prop('disabled', false);
                spinner.addClass('d-none');
            }
        });
    });

    // Delete invoice functionality
    $(document).on('click', '.delete-invoice', function() {
        const invoiceId = $(this).data('invoice-id');
        const row = $(this).closest('tr');

        if (confirm('{{ __("Are you sure you want to delete this invoice?") }}')) {
            $.ajax({
                url: '{{ route("invoice.destroy.ajax", ":id") }}'.replace(':id', invoiceId),
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        showToast(response.message, 'success');
                        row.fadeOut(300, function() {
                            $(this).remove();
                            checkEmptyTable();
                        });
                    } else {
                        showToast(response.message, 'error');
                    }
                },
                error: function() {
                    showToast('{{ __("An error occurred. Please try again.") }}', 'error');
                }
            });
        }
    });

    // Add invoice to table
    function addInvoiceToTable(invoice) {
        // Remove "no invoices" row if it exists
        $('#noInvoicesRow').remove();

        const statusBadge = getStatusBadge(invoice.status);
        const paymentMethodBadge = invoice.payment_method !== 'N/A'
            ? `<span class="badge bg-light text-dark">${invoice.payment_method}</span>`
            : `<span class="text-muted">{{ __('No Payment') }}</span>`;

        const newRow = `
            <tr data-invoice-id="${invoice.id}">
                <td><span class="fw-bold">#${invoice.id}</span></td>
                <td>
                    <div class="d-flex flex-column">
                        <div class="d-flex align-items-center gap-2">${statusBadge}</div>
                        <a href="#" class="text-decoration-none small">${invoice.invoice_number}</a>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-medium">${invoice.issue_date}</span>
                        <small class="text-muted">{{ __('Due') }}: ${invoice.due_date}</small>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-medium">${invoice.customer_name}</span>
                        <small class="text-muted">${invoice.customer_email}</small>
                        <small class="text-muted">${invoice.customer_phone}</small>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <small>${invoice.products}</small>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-medium">${invoice.total_amount}</span>
                        <small class="text-success">{{ __('Paid') }}: ${invoice.paid_amount}</small>
                    </div>
                </td>
                <td>
                    <div class="d-flex flex-column">
                        <span class="fw-medium text-danger">${invoice.due_amount}</span>
                        <small class="text-muted">{{ __('Discount') }}: {{ \Auth::user()->priceFormat(0) }}</small>
                    </div>
                </td>
                <td>${paymentMethodBadge}</td>
                <td>
                    <div class="d-flex gap-1">
                        <a href="#" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                            <i class="ti ti-eye"></i>
                        </a>
                        @can('edit invoice')
                            <a href="#" class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                <i class="ti ti-edit"></i>
                            </a>
                        @endcan
                        @can('delete invoice')
                            <button type="button" class="btn btn-sm btn-outline-danger delete-invoice"
                                    data-invoice-id="${invoice.id}" title="{{ __('Delete') }}">
                                <i class="ti ti-trash"></i>
                            </button>
                        @endcan
                    </div>
                </td>
            </tr>
        `;

        $('#invoicesTableBody').prepend(newRow);
    }

    // Get status badge HTML
    function getStatusBadge(status) {
        const badges = {
            0: '<span class="badge bg-secondary">{{ __("Draft") }}</span>',
            1: '<span class="badge bg-warning">{{ __("Sent") }}</span>',
            2: '<span class="badge bg-info">{{ __("Unpaid") }}</span>',
            3: '<span class="badge bg-warning">{{ __("Partially Paid") }}</span>',
            4: '<span class="badge bg-success">{{ __("Paid") }}</span>'
        };
        return badges[status] || badges[0];
    }

    // Check if table is empty and show appropriate message
    function checkEmptyTable() {
        if ($('#invoicesTableBody tr').length === 0) {
            const emptyRow = `
                <tr id="noInvoicesRow">
                    <td colspan="9" class="text-center py-5">
                        <div class="text-muted">
                            <i class="ti ti-file-invoice" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">{{ __('No Invoices Found') }}</h5>
                            <p>{{ __('Create your first invoice to get started') }}</p>
                            @can('create invoice')
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInvoiceModal">
                                    <i class="ti ti-plus me-1"></i>{{ __('Create Invoice') }}
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            `;
            $('#invoicesTableBody').html(emptyRow);
        }
    }

    // Reset form
    function resetForm() {
        $('#createInvoiceForm')[0].reset();
        hideSelectedContactInfo();
        $('#productRows').html(`
            <div class="row product-row mb-3">
                <div class="col-md-4">
                    <label for="product_0" class="form-label">{{ __('Product') }}</label>
                    <select class="form-select product-select" id="product_0" name="items[0][product_id]" required>
                        <option value="">{{ __('Select Product') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="product_price_0" class="form-label">{{ __('Product Price') }}</label>
                    <input type="number" class="form-control product-price" id="product_price_0"
                           name="items[0][price]" placeholder="{{ __('Product Price') }}" step="0.01" required>
                </div>
                <div class="col-md-2">
                    <label for="product_qty_0" class="form-label">{{ __('Product Qty') }}</label>
                    <input type="number" class="form-control product-qty" id="product_qty_0"
                           name="items[0][quantity]" value="1" min="1" required>
                </div>
                <div class="col-md-2">
                    <label for="product_total_0" class="form-label">{{ __('Total') }}</label>
                    <input type="text" class="form-control product-total" id="product_total_0" readonly>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-danger btn-sm remove-product" style="display: none;">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        `);
        productRowIndex = 1;
        populateProductDropdowns();
        calculateInvoiceTotal();
        setDefaultDueDate();
    }

    // Additional form validation
    function validateForm() {
        let isValid = true;
        let errors = [];

        // Check if customer is selected
        const selectedCustomer = $('#customer_name').val();
        if (!selectedCustomer) {
            errors.push('{{ __("Please select a customer") }}');
            isValid = false;
        }

        // Check if at least one product is selected
        let hasValidProduct = false;
        $('.product-row').each(function() {
            const productId = $(this).find('.product-select').val();
            const price = $(this).find('.product-price').val();
            const qty = $(this).find('.product-qty').val();

            if (productId && price && qty) {
                hasValidProduct = true;
            }
        });

        if (!hasValidProduct) {
            errors.push('{{ __("Please add at least one product with valid details") }}');
            isValid = false;
        }

        // Check dates
        const issueDate = new Date($('#invoice_date').val());
        const dueDate = new Date($('#due_date').val());

        if (dueDate < issueDate) {
            errors.push('{{ __("Due date cannot be earlier than issue date") }}');
            isValid = false;
        }

        if (errors.length > 0) {
            showToast(errors.join('\n'), 'error');
        }

        return isValid;
    }

    // Enhanced form submission with validation
    $('#createInvoiceForm').off('submit').on('submit', function(e) {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const submitBtn = $('#saveInvoiceBtn');
        const spinner = submitBtn.find('.spinner-border');

        // Show loading state
        submitBtn.prop('disabled', true);
        spinner.removeClass('d-none');

        // Prepare form data
        const formData = new FormData(this);

        // Add category_id (you might want to make this dynamic)
        formData.append('category_id', 1);

        $.ajax({
            url: '{{ route("invoice.store.ajax") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.status) {
                    showToast(response.message, 'success');
                    $('#createInvoiceModal').modal('hide');
                    addInvoiceToTable(response.invoice);
                    resetForm();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function(xhr) {
                let message = '{{ __("An error occurred. Please try again.") }}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join('\n');
                }
                showToast(message, 'error');
            },
            complete: function() {
                // Hide loading state
                submitBtn.prop('disabled', false);
                spinner.addClass('d-none');
            }
        });
    });

    // Enhanced toast notification function
    function showToast(message, type = 'success') {
        // Create a more sophisticated toast notification
        const toastContainer = $('#toast-container');
        if (toastContainer.length === 0) {
            $('body').append('<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>');
        }

        const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
        const toastId = 'toast-' + Date.now();

        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        $('#toast-container').append(toastHtml);

        const toastElement = new bootstrap.Toast(document.getElementById(toastId), {
            autohide: true,
            delay: 5000
        });

        toastElement.show();

        // Remove toast element after it's hidden
        document.getElementById(toastId).addEventListener('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    // ==================== QUOTES MANAGEMENT ====================

    let quoteProductRowIndex = 1;
    let quoteCustomers = [];
    let quoteProducts = [];

    // Load customers and products on quote modal open
    $('#createQuoteModal').on('show.bs.modal', function() {
        // Show loading state
        $('#quote_customer_name, #quote_customer_email, #quote_customer_contact').html('<option value="">{{ __("Loading...") }}</option>');
        $('.quote-product-select').html('<option value="">{{ __("Loading...") }}</option>');

        loadQuoteCustomers();
        loadQuoteProducts();
        setDefaultQuoteValidDate();
    });

    // Load customers for quote dropdown
    function loadQuoteCustomers() {
        $.ajax({
            url: '{{ route("invoice.customers.dropdown") }}',
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    quoteCustomers = response.data;
                    populateQuoteCustomerDropdowns();
                    if (quoteCustomers.length === 0) {
                        showToast('{{ __("No customers found. Please add customers first.") }}', 'error');
                    }
                }
            },
            error: function() {
                showToast('{{ __("Error loading customers") }}', 'error');
            }
        });
    }

    // Load products for quote dropdown
    function loadQuoteProducts() {
        $.ajax({
            url: '{{ route("invoice.products.dropdown") }}',
            type: 'GET',
            success: function(response) {
                if (response.status) {
                    quoteProducts = response.data;
                    populateQuoteProductDropdowns();
                    if (quoteProducts.length === 0) {
                        showToast('{{ __("No products found. Please add products first.") }}', 'error');
                    }
                }
            },
            error: function() {
                showToast('{{ __("Error loading products") }}', 'error');
            }
        });
    }

    // Populate quote customer dropdowns
    function populateQuoteCustomerDropdowns() {
        const nameSelect = $('#quote_customer_name');
        const emailSelect = $('#quote_customer_email');
        const contactSelect = $('#quote_customer_contact');

        // Clear existing options
        nameSelect.html('<option value="">{{ __("Select Customer") }}</option>');
        emailSelect.html('<option value="">{{ __("Select Email") }}</option>');
        contactSelect.html('<option value="">{{ __("Select Contact") }}</option>');

        // Populate with customer data
        quoteCustomers.forEach(contact => {
            // Name dropdown - show all contacts
            nameSelect.append(`<option value="${contact.id}" data-type="${contact.type}">${contact.display_name}</option>`);

            // Email dropdown - only show contacts with email
            if (contact.email) {
                emailSelect.append(`<option value="${contact.id}" data-type="${contact.type}" data-email="${contact.email}">${contact.email} (${contact.name})</option>`);
            }

            // Contact dropdown - only show contacts with phone
            if (contact.contact) {
                contactSelect.append(`<option value="${contact.id}" data-type="${contact.type}" data-contact="${contact.contact}">${contact.contact} (${contact.name})</option>`);
            }
        });
    }

    // Populate quote product dropdowns
    function populateQuoteProductDropdowns() {
        $('.quote-product-select').each(function() {
            const select = $(this);
            select.html('<option value="">{{ __("Select Product") }}</option>');

            quoteProducts.forEach(product => {
                select.append(`<option value="${product.id}" data-price="${product.sale_price}">${product.name}</option>`);
            });
        });
    }

    // Quote customer dropdown synchronization with auto-population
    $('#quote_customer_name, #quote_customer_email, #quote_customer_contact').on('change', function() {
        const selectedContactId = $(this).val();

        if (selectedContactId) {
            // Get contact details and auto-populate other fields
            $.ajax({
                url: '{{ route("invoice.contact.details") }}',
                type: 'GET',
                data: { contact_id: selectedContactId },
                success: function(response) {
                    if (response.status) {
                        const contact = response.data;

                        // Find the contact in our customers array for additional info
                        const fullContact = quoteCustomers.find(c => c.id === selectedContactId);

                        if (fullContact) {
                            // Update all dropdowns to show the same contact
                            $('#quote_customer_name').val(selectedContactId);

                            // Auto-populate email dropdown if contact has email
                            if (fullContact.email) {
                                $('#quote_customer_email').val(selectedContactId);
                            } else {
                                $('#quote_customer_email').val('');
                            }

                            // Auto-populate contact dropdown if contact has phone
                            if (fullContact.contact) {
                                $('#quote_customer_contact').val(selectedContactId);
                            } else {
                                $('#quote_customer_contact').val('');
                            }

                            // Show selected contact info
                            showQuoteSelectedContactInfo(fullContact);
                        }
                    }
                },
                error: function() {
                    showToast('{{ __("Error loading contact details") }}', 'error');
                }
            });
        } else {
            // Clear all fields if no selection
            $('#quote_customer_name, #quote_customer_email, #quote_customer_contact').val('');
            hideQuoteSelectedContactInfo();
        }
    });

    // Show selected contact information for quotes
    function showQuoteSelectedContactInfo(contact) {
        // Remove existing info if any
        $('.quote-selected-contact-info').remove();

        const infoHtml = `
            <div class="quote-selected-contact-info mt-2 p-2 bg-light rounded">
                <small class="text-muted">
                    <strong>{{ __('Selected Contact') }}:</strong> ${contact.name} (${contact.type})<br>
                    ${contact.email ? '<strong>{{ __("Email") }}:</strong> ' + contact.email + '<br>' : ''}
                    ${contact.contact ? '<strong>{{ __("Phone") }}:</strong> ' + contact.contact : ''}
                </small>
            </div>
        `;

        $('#quote_customer_contact').closest('.col-md-4').append(infoHtml);
    }

    // Hide selected contact information for quotes
    function hideQuoteSelectedContactInfo() {
        $('.quote-selected-contact-info').remove();
    }

    // Quote product selection handler
    $(document).on('change', '.quote-product-select', function() {
        const selectedOption = $(this).find('option:selected');
        const price = selectedOption.data('price') || 0;
        const row = $(this).closest('.quote-product-row');

        row.find('.quote-product-price').val(price);
        calculateQuoteRowTotal(row);
    });

    // Quote quantity and price change handlers
    $(document).on('input', '.quote-product-qty, .quote-product-price', function() {
        const row = $(this).closest('.quote-product-row');
        calculateQuoteRowTotal(row);
    });

    // Calculate quote row total
    function calculateQuoteRowTotal(row) {
        const qty = parseFloat(row.find('.quote-product-qty').val()) || 0;
        const price = parseFloat(row.find('.quote-product-price').val()) || 0;
        const total = qty * price;

        row.find('.quote-product-total').val(total.toFixed(2));
        calculateQuoteTotal();
    }

    // Calculate quote total
    function calculateQuoteTotal() {
        let subtotal = 0;

        $('.quote-product-total').each(function() {
            subtotal += parseFloat($(this).val()) || 0;
        });

        const tax = 0; // You can implement tax calculation here
        const total = subtotal + tax;

        $('#quote_subtotal').text(formatCurrency(subtotal));
        $('#quote_tax').text(formatCurrency(tax));
        $('#quote_total').text(formatCurrency(total));
    }

    // Add quote product row
    $('#addQuoteProductBtn').on('click', function() {
        const newRow = `
            <div class="row quote-product-row mb-3">
                <div class="col-md-4">
                    <label for="quote_product_${quoteProductRowIndex}" class="form-label">{{ __('Product') }}</label>
                    <select class="form-select quote-product-select" id="quote_product_${quoteProductRowIndex}" name="items[${quoteProductRowIndex}][product_id]" required>
                        <option value="">{{ __('Select Product') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="quote_product_price_${quoteProductRowIndex}" class="form-label">{{ __('Product Price') }}</label>
                    <input type="number" class="form-control quote-product-price" id="quote_product_price_${quoteProductRowIndex}"
                           name="items[${quoteProductRowIndex}][price]" placeholder="{{ __('Product Price') }}" step="0.01" required>
                </div>
                <div class="col-md-2">
                    <label for="quote_product_qty_${quoteProductRowIndex}" class="form-label">{{ __('Product Qty') }}</label>
                    <input type="number" class="form-control quote-product-qty" id="quote_product_qty_${quoteProductRowIndex}"
                           name="items[${quoteProductRowIndex}][quantity]" value="1" min="1" required>
                </div>
                <div class="col-md-2">
                    <label for="quote_product_total_${quoteProductRowIndex}" class="form-label">{{ __('Total') }}</label>
                    <input type="text" class="form-control quote-product-total" id="quote_product_total_${quoteProductRowIndex}" readonly>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-danger btn-sm remove-quote-product">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        `;

        $('#quoteProductRows').append(newRow);
        populateQuoteProductDropdowns();
        updateQuoteRemoveButtons();
        quoteProductRowIndex++;
    });

    // Remove quote product row
    $(document).on('click', '.remove-quote-product', function() {
        $(this).closest('.quote-product-row').remove();
        updateQuoteRemoveButtons();
        calculateQuoteTotal();
    });

    // Update quote remove button visibility
    function updateQuoteRemoveButtons() {
        const rows = $('.quote-product-row');
        if (rows.length > 1) {
            $('.remove-quote-product').show();
        } else {
            $('.remove-quote-product').hide();
        }
    }

    // Set default quote valid date (30 days from issue date)
    function setDefaultQuoteValidDate() {
        const issueDate = new Date($('#quote_issue_date').val());
        const validDate = new Date(issueDate);
        validDate.setDate(validDate.getDate() + 30);

        $('#quote_valid_until').val(validDate.toISOString().split('T')[0]);
    }

    // Update valid date when issue date changes
    $('#quote_issue_date').on('change', function() {
        setDefaultQuoteValidDate();
    });

    // Quote form validation
    function validateQuoteForm() {
        let isValid = true;
        let errors = [];

        // Check if customer is selected
        const selectedCustomer = $('#quote_customer_name').val();
        if (!selectedCustomer) {
            errors.push('{{ __("Please select a customer") }}');
            isValid = false;
        }

        // Check if at least one product is selected
        let hasValidProduct = false;
        $('.quote-product-row').each(function() {
            const productId = $(this).find('.quote-product-select').val();
            const price = $(this).find('.quote-product-price').val();
            const qty = $(this).find('.quote-product-qty').val();

            if (productId && price && qty) {
                hasValidProduct = true;
            }
        });

        if (!hasValidProduct) {
            errors.push('{{ __("Please add at least one product with valid details") }}');
            isValid = false;
        }

        if (errors.length > 0) {
            showToast(errors.join('\n'), 'error');
        }

        return isValid;
    }

    // Quote form submission handler
    $('#createQuoteForm').on('submit', function(e) {
        e.preventDefault();

        if (!validateQuoteForm()) {
            return;
        }

        const submitBtn = $('#saveQuoteBtn');
        const spinner = submitBtn.find('.spinner-border');

        // Show loading state
        submitBtn.prop('disabled', true);
        spinner.removeClass('d-none');

        // Prepare form data
        const formData = new FormData(this);

        // Add category_id (you might want to make this dynamic)
        formData.append('category_id', 1);

        $.ajax({
            url: '{{ route("proposal.store.ajax") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.status) {
                    showToast(response.message, 'success');
                    $('#createQuoteModal').modal('hide');
                    addQuoteToTable(response.proposal);
                    resetQuoteForm();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function(xhr) {
                let message = '{{ __("An error occurred. Please try again.") }}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    message = errors.join('\n');
                }
                showToast(message, 'error');
            },
            complete: function() {
                // Hide loading state
                submitBtn.prop('disabled', false);
                spinner.addClass('d-none');
            }
        });
    });

    // Delete quote functionality
    $(document).on('click', '.delete-quote', function() {
        const proposalId = $(this).data('proposal-id');
        const row = $(this).closest('tr');

        if (confirm('{{ __("Are you sure you want to delete this quote?") }}')) {
            $.ajax({
                url: '{{ route("proposal.destroy.ajax", ":id") }}'.replace(':id', proposalId),
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        showToast(response.message, 'success');
                        row.fadeOut(300, function() {
                            $(this).remove();
                            checkEmptyQuoteTable();
                        });
                    } else {
                        showToast(response.message, 'error');
                    }
                },
                error: function() {
                    showToast('{{ __("An error occurred. Please try again.") }}', 'error');
                }
            });
        }
    });

    // Convert to invoice functionality
    $(document).on('click', '.convert-to-invoice', function() {
        const proposalId = $(this).data('proposal-id');
        const row = $(this).closest('tr');

        if (confirm('{{ __("Are you sure you want to convert this quote to invoice?") }}')) {
            $.ajax({
                url: '{{ route("proposal.convert.invoice.ajax", ":id") }}'.replace(':id', proposalId),
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.status) {
                        showToast(response.message, 'success');

                        // Update the row to show converted status
                        const statusCell = row.find('td:nth-child(8)');
                        statusCell.html(statusCell.html() + '<br><small class="text-success">{{ __("Converted") }}</small>');

                        // Remove convert button and add view invoice button
                        const convertBtn = row.find('.convert-to-invoice');
                        convertBtn.replaceWith(`
                            <a href="{{ route('invoice.show', ':invoice_id') }}" class="btn btn-sm btn-outline-info" title="{{ __('View Invoice') }}">
                                <i class="ti ti-file-invoice"></i>
                            </a>
                        `.replace(':invoice_id', response.invoice_id));

                    } else {
                        showToast(response.message, 'error');
                    }
                },
                error: function() {
                    showToast('{{ __("An error occurred. Please try again.") }}', 'error');
                }
            });
        }
    });

    // Edit quote functionality (placeholder - you can implement full edit modal)
    $(document).on('click', '.edit-quote', function() {
        const proposalId = $(this).data('proposal-id');
        // For now, redirect to edit page
        window.location.href = '{{ route("proposal.edit", ":id") }}'.replace(':id', proposalId);
    });

    // Add quote to table
    function addQuoteToTable(proposal) {
        // Remove "no quotes" row if it exists
        $('#noQuotesRow').remove();

        const statusBadge = getQuoteStatusBadge(proposal.status);

        const newRow = `
            <tr data-proposal-id="${proposal.id}">
                <td><span class="fw-bold">#${proposal.id}</span></td>
                <td>
                    <a href="#" class="text-decoration-none">${proposal.proposal_number}</a>
                </td>
                <td><span class="fw-medium">${proposal.issue_date}</span></td>
                <td><span class="fw-medium">${proposal.customer_name}</span></td>
                <td><span class="text-muted">${proposal.customer_email}</span></td>
                <td><span class="fw-medium">${proposal.total_amount}</span></td>
                <td><span class="text-muted">${proposal.customer_gst}</span></td>
                <td>${statusBadge}</td>
                <td>
                    <div class="d-flex gap-1">
                        <a href="#" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                            <i class="ti ti-eye"></i>
                        </a>
                        @can('edit proposal')
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-quote"
                                    data-proposal-id="${proposal.id}" title="{{ __('Edit') }}">
                                <i class="ti ti-edit"></i>
                            </button>
                        @endcan
                        @can('create invoice')
                            <button type="button" class="btn btn-sm btn-outline-success convert-to-invoice"
                                    data-proposal-id="${proposal.id}" title="{{ __('Convert to Invoice') }}">
                                <i class="ti ti-file-invoice"></i>
                            </button>
                        @endcan
                        @can('delete proposal')
                            <button type="button" class="btn btn-sm btn-outline-danger delete-quote"
                                    data-proposal-id="${proposal.id}" title="{{ __('Delete') }}">
                                <i class="ti ti-trash"></i>
                            </button>
                        @endcan
                    </div>
                </td>
            </tr>
        `;

        $('#quotesTableBody').prepend(newRow);
    }

    // Get quote status badge HTML
    function getQuoteStatusBadge(status) {
        const badges = {
            0: '<span class="badge bg-secondary">{{ __("Draft") }}</span>',
            1: '<span class="badge bg-warning">{{ __("Open") }}</span>',
            2: '<span class="badge bg-info">{{ __("Sent") }}</span>',
            3: '<span class="badge bg-danger">{{ __("Rejected") }}</span>',
            4: '<span class="badge bg-success">{{ __("Accepted") }}</span>'
        };
        return badges[status] || badges[0];
    }

    // Check if quote table is empty and show appropriate message
    function checkEmptyQuoteTable() {
        if ($('#quotesTableBody tr').length === 0) {
            const emptyRow = `
                <tr id="noQuotesRow">
                    <td colspan="9" class="text-center py-5">
                        <div class="text-muted">
                            <i class="ti ti-file-description" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">{{ __('No Quotes Found') }}</h5>
                            <p>{{ __('Create your first quote to get started') }}</p>
                            @can('create proposal')
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createQuoteModal">
                                    <i class="ti ti-plus me-1"></i>{{ __('Create Quote') }}
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            `;
            $('#quotesTableBody').html(emptyRow);
        }
    }

    // Reset quote form
    function resetQuoteForm() {
        $('#createQuoteForm')[0].reset();
        hideQuoteSelectedContactInfo();
        $('#quoteProductRows').html(`
            <div class="row quote-product-row mb-3">
                <div class="col-md-4">
                    <label for="quote_product_0" class="form-label">{{ __('Product') }}</label>
                    <select class="form-select quote-product-select" id="quote_product_0" name="items[0][product_id]" required>
                        <option value="">{{ __('Select Product') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="quote_product_price_0" class="form-label">{{ __('Product Price') }}</label>
                    <input type="number" class="form-control quote-product-price" id="quote_product_price_0"
                           name="items[0][price]" placeholder="{{ __('Product Price') }}" step="0.01" required>
                </div>
                <div class="col-md-2">
                    <label for="quote_product_qty_0" class="form-label">{{ __('Product Qty') }}</label>
                    <input type="number" class="form-control quote-product-qty" id="quote_product_qty_0"
                           name="items[0][quantity]" value="1" min="1" required>
                </div>
                <div class="col-md-2">
                    <label for="quote_product_total_0" class="form-label">{{ __('Total') }}</label>
                    <input type="text" class="form-control quote-product-total" id="quote_product_total_0" readonly>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-danger btn-sm remove-quote-product" style="display: none;">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        `);
        quoteProductRowIndex = 1;
        populateQuoteProductDropdowns();
        calculateQuoteTotal();
        setDefaultQuoteValidDate();
    }
});
</script>

<style>
/* Quote Management Styles */
.quote-selected-contact-info {
    border-left: 3px solid #28a745;
}

.quote-product-row {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
}

.quote-product-row:last-child {
    border-bottom: none;
}

#quotesTable .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

#quotesTable .badge {
    font-size: 0.7rem;
}

.quote-product-total {
    background-color: #f8f9fa;
    font-weight: 500;
}

.modal-xl .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Responsive adjustments for quote table */
@media (max-width: 768px) {
    #quotesTable {
        font-size: 0.85rem;
    }

    #quotesTable .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }

    #quotesTable th,
    #quotesTable td {
        padding: 0.5rem 0.25rem;
    }
}

/* Loading state for dropdowns */
.form-select:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
}

/* Quote summary card styling */
.card.bg-light {
    border: 1px solid #dee2e6;
}

/* Action buttons hover effects */
.btn-outline-primary:hover,
.btn-outline-secondary:hover,
.btn-outline-success:hover,
.btn-outline-danger:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}
</style>
