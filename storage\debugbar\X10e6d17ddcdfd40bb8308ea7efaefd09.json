{"__meta": {"id": "X10e6d17ddcdfd40bb8308ea7efaefd09", "datetime": "2025-07-31 12:23:48", "utime": 1753964628.049269, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964626.136076, "end": 1753964628.049312, "duration": 1.913236141204834, "duration_str": "1.91s", "measures": [{"label": "Booting", "start": 1753964626.136076, "relative_start": 0, "end": **********.836211, "relative_end": **********.836211, "duration": 1.7001349925994873, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.836247, "relative_start": 1.7001709938049316, "end": 1753964628.049316, "relative_end": 3.814697265625e-06, "duration": 0.21306896209716797, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47339872, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00919, "accumulated_duration_str": "9.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.951312, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 64.635}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9861012, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 64.635, "width_percent": 18.281}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.999897, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 82.916, "width_percent": 17.084}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1142204809 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1142204809\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-496982242 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-496982242\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-971798240 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-971798240\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1651104952 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImdzMnRnbzR3U3FFOUFSSkR1amtPeEE9PSIsInZhbHVlIjoiMmw3anltc3RpWStKZ3BHK1AzUUp0enBIaXJPS1d1elV0cjQ4RXNlV3d5dEpYbTMrNys3TzY4ZUNJWU5NbWdmR3piYzF0eXFCbFVDYmJKc1VNVG9NYnlPZlgvOWlwLzlUNU1wYUhJa2pGMW1KZ3BZby9jZkNEN0dXZTArTTY1MGVoQmpqeTMwTDZQb2tSc0VKNytpbUFtWHU2Sk12ZTZmRVB5ZGFtbS9RbzFUSzNvT2pYSU9HR0c3MVlUUFF1MHEzbitaWGJuY1dlZjZNV2NsclYwM0V0SWhMbCsxOUJ3WUtwa2pFNWpZQ1ZLV0VadnBQeTFuYXFuMWxaWGJnV1VUcmh6TWdISHZGKzYrcVhITTMxR0JUN1IxR1BPYlgvRGpkTjYzak14eTk4RzUvcGJzUU8rbXFqYlljU3V4cDQwYzRMSU45MW55clBHVFVCTkRDaFZMMTRYOW5MVjZQbkhyNWsrUnhzR2psNStmT0kxaUJ1MDFZeUkreWs5a3ZibXFzeHFxUjVycjFHMEZvVW4vOG9vU0JvMS8ybkgrK3BlSnljK0JpcFVhSU4xQTJiMFloMWJtUDFIOCt1TkU1dC9uR0xucFpJY3VqNnR5SDZiTVk2V2pTWG9BbEdCZW5NTXI2cEw5YjROTlNXcUVwdmFHRlJLNkwrd1phNDlvWmUrWGEiLCJtYWMiOiIyYzI5ZTQzY2RjMTU2NGRiZjNmNTdjNzdmMjAzYzYwYTg3MWE2N2IwZTBiZmMyYzBhYTkxZDM5NjBhYWI2YzI5IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkZiL1ZYblgyZ1lKMFViWGdvRER3WEE9PSIsInZhbHVlIjoiZDFCdlY0dWRZaFZzM1c3NG1pZEpud1RtMnZqWFlOeTVYc1BocmVtWjUzQnB0eWxBUklieWxyQmkvcVlReXpLbHZUeWMzS1IwUDdvb1hWRm8vNWZFNmYvZzhmUldMeHQ2T3VhemtDUnh0bXpHOHRzUUNVUDM3WEhWL002MjRVY3A3cW9QdE9jK05UQkF2L0MwRnhrbTY1RDR5TjhYdFp6cVR3cHRDV1d1WFlBWHhQRi80eXB5VUQ5c2h2NlFRcW5PWEU1WU5vS1YwQzVzNys1YS84RjVMMUJlSFVGRC9JVUw3bG5sRGc0ZkVJaHFQMXdRWS8ycmI5SkYvSGZwU3g5SFQwbGtseFArYTZNUWdhQ01oQlFJK2dPSGl3SFhwOXJLSHZEQjhhQXF2ZEFqYUVIditzVHBZaW5yNVlXRk92R2ZBdDFmL2Zpa1NBdWc4TWhVdEQrUGVSVEtaeGdLc1VUQXc3d1gxRGdmVUlrem5tNC9RVm92ZzM2ZTFxRDluUDA4QkRaNXdHUTdPUEgzU3crZzVHZDNvSTJpamYyT1I4MVFlUzg2RHFIdXBqRi9wclAwUHFhRnR2UkRWVWZuandIcFJ4dHJpWFNIcUlNU28xRVpBZTh0MWNYRlYwWndLQWQ2c01taUNEZFpyUXJlM0xDR3pCZS9iL3B2eVJVMnBWSk8iLCJtYWMiOiJjYTg1NTZhYzQxOWRmZGI3NmQxMTEwN2YyMzEyMTUwNjVhNjRlZDZjNzU1YzBiMTUyYzg5Yjc2Yjg4YzNhMTRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651104952\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-930413827 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-930413827\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:23:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9iMWhuUlp5eU0vb0F4WkdWNmhBTnc9PSIsInZhbHVlIjoiZGVXdkhBeGsrclpDSnAyUXozSXlFSmR3WndUcFBMaks3UkxvcnBucmgydnBuY0NuaUNPL0pkR2tDU0llYkdrbUlHQVM5OE9MT2VrMFdNaXZXc0hjMDM4NnJMbENBd1dEVHk5ZzZRcDJoWThOR0NJZ0RqTDAyQUNGT0pIUTJZSkRhRUNkOCtyWUhLTWhzSVRFei8xRHVWZU5OM3h4c2xreURVeC9wWVZoN2hYcWltREptTlg3NWVTR1FSQTRNeDEzMHEwVG9RUU9mZG4xSnV3bkVEcG1FUjM2dTQ1LzZ3cmxlT00vcXJUcjVUWmFKcTFPamg1ZE9oS1dnRmNCKzllck5weWtnQW5DaHZ5bmhqVlJRSXRsalJTY1pKZFpaNERVWGJJL0NtUlljV0hhRVJqRFFTSko2ZTZ5T3czZkZ5Mnd3cFQyTk9hVURIME1ZdWdZNTlMMUNNQjJhS3paU0Y2TFRreDEzVlE3M1haQjc0ZE1GbWk2d2M1dFl1N0NKV3Z2ajkyR0E4WkVVVFJBZlBKbE1rSzRUQ0JzWlMySDZZdGtyT3p5Q1V6VVNnVGphcThXZVFRQm95UHlQaU1US29BTE9GQnFJYnBrdDZXVWJLamFsYzRvMWp1bGpIK3dpSUhPVG9rUTRoRmhwMkVuNVVMSEl5Qk82UjlVZk1GZnNPdUoiLCJtYWMiOiI4NmVmYzMxNzc1NDk3ZmNjNWFjYjVmMzI5ZWQ1YzE4YTMyMjViN2Y3M2I1ZTY5OGE3NTYzNjdhNmQyYzgwYWYyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:23:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6InE1c2g2U2VWWTgzL0wxUjQvQ3gxTnc9PSIsInZhbHVlIjoiQ2M3SVdUN1I5cTJiVjJCUUx4V1FuZWJlTEJZMG1TSGNHaFZFRzFTY3RKUlpZWEd5c2ZtL0NWZ2NsMDdNL2t5MmJTOEU2bW1ja25YUXJQWHVoTWxJVHhuQTdteC9GbFdWdXpMcHBodGxMV3FjUUxrTHdvR0JEZkxMUTU4SHpmbnBLNjk5d1Z6emhjOS9ha2tEM2JhUzRQRjBzZVdsOWtXRzJTUWpoVy9OSGl2UnlvcTFFMEE0RHhzbnZVWkt6eGxMdlFGMjcxcFIxSWhrTkREQ011YnJOUFQvSCtaRC9xcFE4cjdiZDRkVUZYNGtOVWJDbFJvaEhzWHdQS2RmTE5xdWhrVmc2aXErMlB0WU9CMTllTDBvWnRsQTdXUmZSOTZzTjhMZnFXWGh5TlZSVWQyNFRGcWVPdy9lcHB2c056cEVMVmpaK0RnbktqR3pQNWdNaFlpRHlJaVRTbThoSVI1bkpPcGJ3NHdCY2wzK1VCaXd6bmVmbnRPMWw3VjZyc0VnT3QwZnJza2hQd200ZlZ0YVVFMXRHUTYrb2Z0WXUxbDdiemRzODVSMlQ3a1h6cTVJRGtETmJRUkg0cVBldzl3SU5rNXBEU21iSFl2azRDd0RwcjNlYVBoK2lLMzVWNE94TGZWMEtsK3V4MjdkM1dNc0x4MzNiSEpUZmRLL043NEkiLCJtYWMiOiIxMDBjNmMwMmMxZTAxODhiMzljYjkwYTM0ZDdhY2YzYjAwMjdjODFkODhhZDA4OGEwMjc0MDZlZDcxY2YzMmQyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:23:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9iMWhuUlp5eU0vb0F4WkdWNmhBTnc9PSIsInZhbHVlIjoiZGVXdkhBeGsrclpDSnAyUXozSXlFSmR3WndUcFBMaks3UkxvcnBucmgydnBuY0NuaUNPL0pkR2tDU0llYkdrbUlHQVM5OE9MT2VrMFdNaXZXc0hjMDM4NnJMbENBd1dEVHk5ZzZRcDJoWThOR0NJZ0RqTDAyQUNGT0pIUTJZSkRhRUNkOCtyWUhLTWhzSVRFei8xRHVWZU5OM3h4c2xreURVeC9wWVZoN2hYcWltREptTlg3NWVTR1FSQTRNeDEzMHEwVG9RUU9mZG4xSnV3bkVEcG1FUjM2dTQ1LzZ3cmxlT00vcXJUcjVUWmFKcTFPamg1ZE9oS1dnRmNCKzllck5weWtnQW5DaHZ5bmhqVlJRSXRsalJTY1pKZFpaNERVWGJJL0NtUlljV0hhRVJqRFFTSko2ZTZ5T3czZkZ5Mnd3cFQyTk9hVURIME1ZdWdZNTlMMUNNQjJhS3paU0Y2TFRreDEzVlE3M1haQjc0ZE1GbWk2d2M1dFl1N0NKV3Z2ajkyR0E4WkVVVFJBZlBKbE1rSzRUQ0JzWlMySDZZdGtyT3p5Q1V6VVNnVGphcThXZVFRQm95UHlQaU1US29BTE9GQnFJYnBrdDZXVWJLamFsYzRvMWp1bGpIK3dpSUhPVG9rUTRoRmhwMkVuNVVMSEl5Qk82UjlVZk1GZnNPdUoiLCJtYWMiOiI4NmVmYzMxNzc1NDk3ZmNjNWFjYjVmMzI5ZWQ1YzE4YTMyMjViN2Y3M2I1ZTY5OGE3NTYzNjdhNmQyYzgwYWYyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:23:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6InE1c2g2U2VWWTgzL0wxUjQvQ3gxTnc9PSIsInZhbHVlIjoiQ2M3SVdUN1I5cTJiVjJCUUx4V1FuZWJlTEJZMG1TSGNHaFZFRzFTY3RKUlpZWEd5c2ZtL0NWZ2NsMDdNL2t5MmJTOEU2bW1ja25YUXJQWHVoTWxJVHhuQTdteC9GbFdWdXpMcHBodGxMV3FjUUxrTHdvR0JEZkxMUTU4SHpmbnBLNjk5d1Z6emhjOS9ha2tEM2JhUzRQRjBzZVdsOWtXRzJTUWpoVy9OSGl2UnlvcTFFMEE0RHhzbnZVWkt6eGxMdlFGMjcxcFIxSWhrTkREQ011YnJOUFQvSCtaRC9xcFE4cjdiZDRkVUZYNGtOVWJDbFJvaEhzWHdQS2RmTE5xdWhrVmc2aXErMlB0WU9CMTllTDBvWnRsQTdXUmZSOTZzTjhMZnFXWGh5TlZSVWQyNFRGcWVPdy9lcHB2c056cEVMVmpaK0RnbktqR3pQNWdNaFlpRHlJaVRTbThoSVI1bkpPcGJ3NHdCY2wzK1VCaXd6bmVmbnRPMWw3VjZyc0VnT3QwZnJza2hQd200ZlZ0YVVFMXRHUTYrb2Z0WXUxbDdiemRzODVSMlQ3a1h6cTVJRGtETmJRUkg0cVBldzl3SU5rNXBEU21iSFl2azRDd0RwcjNlYVBoK2lLMzVWNE94TGZWMEtsK3V4MjdkM1dNc0x4MzNiSEpUZmRLL043NEkiLCJtYWMiOiIxMDBjNmMwMmMxZTAxODhiMzljYjkwYTM0ZDdhY2YzYjAwMjdjODFkODhhZDA4OGEwMjc0MDZlZDcxY2YzMmQyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:23:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-95148770 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95148770\", {\"maxDepth\":0})</script>\n"}}