{"__meta": {"id": "Xe5d19d6bb2ecc757a8fd2273dde1853b", "datetime": "2025-07-31 11:36:11", "utime": **********.988762, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.240299, "end": **********.988783, "duration": 0.7484838962554932, "duration_str": "748ms", "measures": [{"label": "Booting", "start": **********.240299, "relative_start": 0, "end": **********.898424, "relative_end": **********.898424, "duration": 0.6581249237060547, "duration_str": "658ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.898454, "relative_start": 0.6581549644470215, "end": **********.988785, "relative_end": 2.1457672119140625e-06, "duration": 0.0903310775756836, "duration_str": "90.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47344880, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006140000000000001, "accumulated_duration_str": "6.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.950592, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.036}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.966218, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.036, "width_percent": 12.052}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.971384, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 89.088, "width_percent": 10.912}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-392475810 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-392475810\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-552586234 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-552586234\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1989102939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1989102939\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1835793898 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im1hOTF6WFU1YkJPTUJWRGJqa28ybkE9PSIsInZhbHVlIjoiUnJ0dk40U25rbE5Vb1M4Njh2NklNT2FGYkhmQlIrU0NaMDgzSTdqTWVFZkhFT0ZNV3h0Qml3bEY3Y0xGa0h6ZHY3K09pYm9Qd2t1Rk5WeU5IeUorOFNCRmJSa25SRENwUng4S29IdDhpMG9ZWUtvRUlEcU1scmVqY25xZmY3T0tvQlpVSmtlUy9MdURFUTBVY3NiMmxFOHJVVHRmK0NmSzQrWmNuWWhDUHd5cXRORmNUNmxLZlJHSktPTHNNQTJQZ3ZteEVHVGd3SXJ5VWR3VUlnbDVIbjU4WUhjemxnb1Z2bm9PdDhsRHdtRHFJUFFFRlhzS0hYblVMK3k2UCtYRDd5aDVZS1k3ekJVWmdDYnJQbVFEZXFLK3JqQ2ZqM0E2ekMrellvWU5tQlJUSkE1blIzZk9XMnFjRm91Y1B2L2lZN3JQSFF2U0w3dlJRMDc5NUZ0d2FCMlNUbWJSYWJMMDF2MVdqZDBCWElpamcvSFBNVTNmenozdmtJOFl1SHpUdUJmRFJYZmpyOHpsL2JobHg0M1ZuOHJ0YTg3Ty9jK0xKdWxmeGtrR2x2bXY1a2JoY01GMGdmNi91aHJ4S0RmdXpMUFBXYXRFcjY0VTJ0anQ3SlZyQUFoUWNpeDNPOEwzczZLSC8zVUtzbFN3NHpHNmRKdnFvMlU4cFZRa0tLZ2IiLCJtYWMiOiIxZDlmM2JmZmUxNTZkOWUxMjJjMDYzYWVkODQyZGJiZDQwYjJmZjJmYjBiOTgxMjZkY2YyYzE3YTM0ZmQyNThkIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Ii8zNzc4RElJaWVEVWdSR0tRSWFZWmc9PSIsInZhbHVlIjoiYW54aG42ejFpaytsdEhHanJrMnNpb1k5OEVmckhSTG9meGFXdmR2MWF6MHJNVUZiQ0FYSVB1VisvYjBXRUtuZ0pNZmQ4RzNnWXZRUkNhMW9PV3JxOE9PNTkyelNCa1NOYjlmdE1DVXFVM0ljWDhZenp0elZyNGhFSVJuaVpPbWNmT2V1VTNwR2VVOTJ2YjBCK1FWSE9TOFRpUFUvVzN5YTdudUQ4SGhDdnRSc2xSWmVWaWgxaGNlVGtiZmdGUjR2clh6aHYyM3VkSERtdG9wMFJpVHZPM2hzM3JrdEVUclVEVUFHUksvejFGWW9UZ211ZXVaZHdHaksrWVlaclZRTmd2NEVldXF5WUd2cGxDL0tERzF3MS9pRUZzaW05OVVFcjEvVkxhWkljZ1VqazhBdVZkLzRrc05qN21TYVBOcS9IaWxjNVJZWm9WeGl3RFRKNTU1T3dTQlJsYnh3TWVBTEp0QWRsMGJIakJqS21SbDl0YjhNbjNnU2NWT0c0QWY1bXRxMVZGVmtNRjcrZU10ZndERWF2b2h5dElOcVRmcjduVUNVVTUwMkhqWnpuUW03R0p3cXlmQnhPSUpneWdnMlNuOWxxY2lGVTNzR09QRk1JaFkySjBSTWhBdEV1SjZpaThmMy95N1lHVm5pdzQ0SGR6Rm1WaGxyMERZaDVoeG8iLCJtYWMiOiIzMmMxYTQ1NjUzYWM3NzJiODA0OTljYTQyMmRjYTc5Mjc2N2EwYjk4NGQ1ZjE4ZDE1ZjdjZGE1N2Q1MzUyZTdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835793898\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-779561217 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779561217\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-111478252 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:36:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNTbXlqeVdVV2xjTVlTbWhuVzRxOEE9PSIsInZhbHVlIjoiRWtZTTdxOWcrOGduN0FROUxqYlEyTDZLa3RwMlVHVHVuZHdlTDhQbktEMUpXK0UwOUJ5cytMRHRZVXNQV21rbStVNDAvTi9xankzd3Rtd0xDR25kU1RUQ3I5dENyclNvOGRxMXVGSUE1Kzh3WHNONnhhMnd0NnZwNkRVWUtFeTNaNkpkZDdXb2luVVdiYXBPVkpZV1pHR0ovYkgvWk9XbGs1azRYUXRRaHpLT2pUVHdIMTRhdG1xYzBMNzhSYnVBbWh2S01nOTRrNTUwbXdKOEF5Q1ZwQk41Zk1YeitlSms3OEdKbUh4QUIzWEpqWkNjWXN6b2s0RWZwNmVuQjNmY2FFZGVteUwyUWcwdW9xMkZzb2laY24rWkpWY24xTEI0L2FZZzkrWUJjdkU2Umd3K2FveGIxZktuRUg0QUJuSmdHU2hmaXN3bm1IdDhzM3dRUjlnYjRLR08xMUEwZEtCRGQ1eGFFeWlEYlZITWFyVUtXUlMzSUo2MHZGRTIyTVpmbHZVc1FYUEoxc0k4c2pXdTVKclVZci9sTm40dDQ5dGIraUQwcEFDQjZTQTF3OEIzVW1pYWgwM00zSkg2MHFnNllxbGlYL0kwMWNDQXdoV0VIb2h0QkdhRWM5blJ6T1FraDNBN0hzRTRIdENqbkJyaWZ6cGFOUFJEc3hiQWdLSlUiLCJtYWMiOiIzYjJkNWVkMmQyOGQxYWM4YmM4YzI2OTBhZmY5OGQzMTA1YmQ4ZDAzZDJkZTA0NjIwYmU2NGNjNGZmZmNmOWVhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:36:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IktmcytEMkczNjIvc3ZkVG9Dbk1uWFE9PSIsInZhbHVlIjoiblVEOGVaWlZWY1dvNUx3MGtFVjZxbVZUK2FHVFBIaHFGeFVwNWgzWUQ1cGRtb3dzNGNSK2k3K2F1ZW1ZRDV6NVFDcHJiN1lkekFCb2ZuVkxKVWVEUHBVdUFBUDZHRS9wdmlyN2JMbmlmbHRQRFNEOFh4VTNqRytNZkdhdS9GUFkwU1ZUYkEzZzFRUlVmVkJ6eDFyTG1OMUw0VStEeXEyelBuR1BTUzA4U0MxaFpVVlAwS2ZwSDFwd012cTNvMDc1U3EvTTNwRzNxVmZxZVlMS2JjRnV0YVJUT0QwdjFoL3RLSURzdHIxSHdSZDN2QmQ0TlJBamhsb0V6M0M5ajRuZWY3UlpwOVBQNVBaZG9VcFpzN3QybzRWNU04QkpnYnZlSDBlSE1uR1ZybHE4RVNCVTFyQ1BCYzRtT0pSNi9mdjEyV3k0dXdqUlJtRGxhUHNhV2VGNDlVVG0rWVNWWHVTeDhkUTBhT21Zd2FUQ214K1ZFcU8renpFaVh6ZnJtWDFKNnJJMkhTMWorLzVaYVY0KzhVZ0FNM0ZZZ21qdlp0MFpNNHdxc20wbjAySlpJVDIyeml5M29MaFN0bklOaGZoVUZ4bjJFNWhoclhvZXVreGpIZDF2Vm1hRlRvMGtEV2dZNlVvNlc2czFJbEhUMGRTcFYvRlZpS2ZTL3gyRU5wYVQiLCJtYWMiOiJjOWEyMTEzMjM4MzYzMzUxNTM2YTg4MjRiZTk3ODcwMjhmZjZmYWFiNWYzOGM5ZmVkZWQ3N2Q0Y2FiNTUzZmZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:36:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNTbXlqeVdVV2xjTVlTbWhuVzRxOEE9PSIsInZhbHVlIjoiRWtZTTdxOWcrOGduN0FROUxqYlEyTDZLa3RwMlVHVHVuZHdlTDhQbktEMUpXK0UwOUJ5cytMRHRZVXNQV21rbStVNDAvTi9xankzd3Rtd0xDR25kU1RUQ3I5dENyclNvOGRxMXVGSUE1Kzh3WHNONnhhMnd0NnZwNkRVWUtFeTNaNkpkZDdXb2luVVdiYXBPVkpZV1pHR0ovYkgvWk9XbGs1azRYUXRRaHpLT2pUVHdIMTRhdG1xYzBMNzhSYnVBbWh2S01nOTRrNTUwbXdKOEF5Q1ZwQk41Zk1YeitlSms3OEdKbUh4QUIzWEpqWkNjWXN6b2s0RWZwNmVuQjNmY2FFZGVteUwyUWcwdW9xMkZzb2laY24rWkpWY24xTEI0L2FZZzkrWUJjdkU2Umd3K2FveGIxZktuRUg0QUJuSmdHU2hmaXN3bm1IdDhzM3dRUjlnYjRLR08xMUEwZEtCRGQ1eGFFeWlEYlZITWFyVUtXUlMzSUo2MHZGRTIyTVpmbHZVc1FYUEoxc0k4c2pXdTVKclVZci9sTm40dDQ5dGIraUQwcEFDQjZTQTF3OEIzVW1pYWgwM00zSkg2MHFnNllxbGlYL0kwMWNDQXdoV0VIb2h0QkdhRWM5blJ6T1FraDNBN0hzRTRIdENqbkJyaWZ6cGFOUFJEc3hiQWdLSlUiLCJtYWMiOiIzYjJkNWVkMmQyOGQxYWM4YmM4YzI2OTBhZmY5OGQzMTA1YmQ4ZDAzZDJkZTA0NjIwYmU2NGNjNGZmZmNmOWVhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:36:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IktmcytEMkczNjIvc3ZkVG9Dbk1uWFE9PSIsInZhbHVlIjoiblVEOGVaWlZWY1dvNUx3MGtFVjZxbVZUK2FHVFBIaHFGeFVwNWgzWUQ1cGRtb3dzNGNSK2k3K2F1ZW1ZRDV6NVFDcHJiN1lkekFCb2ZuVkxKVWVEUHBVdUFBUDZHRS9wdmlyN2JMbmlmbHRQRFNEOFh4VTNqRytNZkdhdS9GUFkwU1ZUYkEzZzFRUlVmVkJ6eDFyTG1OMUw0VStEeXEyelBuR1BTUzA4U0MxaFpVVlAwS2ZwSDFwd012cTNvMDc1U3EvTTNwRzNxVmZxZVlMS2JjRnV0YVJUT0QwdjFoL3RLSURzdHIxSHdSZDN2QmQ0TlJBamhsb0V6M0M5ajRuZWY3UlpwOVBQNVBaZG9VcFpzN3QybzRWNU04QkpnYnZlSDBlSE1uR1ZybHE4RVNCVTFyQ1BCYzRtT0pSNi9mdjEyV3k0dXdqUlJtRGxhUHNhV2VGNDlVVG0rWVNWWHVTeDhkUTBhT21Zd2FUQ214K1ZFcU8renpFaVh6ZnJtWDFKNnJJMkhTMWorLzVaYVY0KzhVZ0FNM0ZZZ21qdlp0MFpNNHdxc20wbjAySlpJVDIyeml5M29MaFN0bklOaGZoVUZ4bjJFNWhoclhvZXVreGpIZDF2Vm1hRlRvMGtEV2dZNlVvNlc2czFJbEhUMGRTcFYvRlZpS2ZTL3gyRU5wYVQiLCJtYWMiOiJjOWEyMTEzMjM4MzYzMzUxNTM2YTg4MjRiZTk3ODcwMjhmZjZmYWFiNWYzOGM5ZmVkZWQ3N2Q0Y2FiNTUzZmZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:36:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111478252\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-899467857 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899467857\", {\"maxDepth\":0})</script>\n"}}