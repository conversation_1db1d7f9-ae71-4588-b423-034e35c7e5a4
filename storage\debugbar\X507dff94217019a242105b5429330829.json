{"__meta": {"id": "X507dff94217019a242105b5429330829", "datetime": "2025-07-31 12:13:11", "utime": **********.380661, "method": "GET", "uri": "/invoice/contact-details?contact_id=lead_12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963988.96347, "end": **********.380712, "duration": 2.4172420501708984, "duration_str": "2.42s", "measures": [{"label": "Booting", "start": 1753963988.96347, "relative_start": 0, "end": **********.126268, "relative_end": **********.126268, "duration": 2.1627979278564453, "duration_str": "2.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.126326, "relative_start": 2.162856101989746, "end": **********.380718, "relative_end": 5.9604644775390625e-06, "duration": 0.2543919086456299, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46309152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1593\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1593-1651</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03601, "accumulated_duration_str": "36.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2656531, "duration": 0.03345, "duration_str": "33.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 92.891}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.329616, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 92.891, "width_percent": 3.943}, {"sql": "select `id`, `name`, `email`, `phone` as `contact` from `leads` where `id` = '12' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1624}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.340011, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1624", "source": "app/Http/Controllers/InvoiceController.php:1624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1624", "ajax": false, "filename": "InvoiceController.php", "line": "1624"}, "connection": "radhe_same", "start_percent": 96.834, "width_percent": 3.166}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense-categories/list-ajax\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-1935701216 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1935701216\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-28201322 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lead_12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28201322\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-871068323 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-871068323\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImlKQWJkUVk3SWIrd0dzVThJOENxVnc9PSIsInZhbHVlIjoiN2wzUVJXdHZzei9TdVUrOGVLUk1JaHRoZVdHK3FENkgzTVBTa0tBcnRFQTNXT1dUOTBkbkk3c3Erb0tDUzFrTS9ZeUFaYUgyaURTVm8zTFVwUy9MbWpOZDNJdkI3RU1xS0k1QkplQy9JTjB3M1N4QkJwVGpPb0tncnVpUWVvTUxXK3poS1hhMis4UmlDb1VYYmVuSkhzV2FzNE1iTEhGbVRmTFlmMmx5MDl0VWxyZkJDblNzWDlScndUMW1RTGRCWUdyMlNMMnlOQllMYTZ1b2Q4bnFFZVRRSVZPc0hzMUt6ZFI4U3BLNFhXTlJxNmUxUGl5NldkTit3V1ZtYUI0aHgxVWhjckg0S1VCMytEcElTSVg0bHNOK0tVK3R1dUlQYS9MbzA0eEJQUVQ4ZHBVcUcvaFlNUERGSTltdUY3TGV6cS9qNmUwYnBUWDFhd0dpbUtVcmZPOTI0dXZqRWZCdTRyTC9EMFdYWTRQQmZGekdXZzNEU05CTks5R1VrbmtwbVBXWWN0SjE1emUyUmN3a01ZQjR4TWI0ZXpYS0txYkZUZm9wM1ZKYVpPcUhDejY4MlZ6NG8weENMN21xczg1ZVVKaXBKZmI4dCtBRWNFVGdnMGVhRnZqNGhDQUNYdUZCUHBXcXlQOS91STFQK0tqcVNaY1NRalp3UHNaZjFPRkwiLCJtYWMiOiI5ZGFiMDllNjRiYjhkN2IyMGQxNzgyZjk3ZjVjYzAxNTZjMmNmZWMyNGUwNzZjMmIyMjFlMTU4NTZlYTIzYzc1IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InFkYWgwaThxRXZBbStwbFBkOGlzc0E9PSIsInZhbHVlIjoiWHNoQVIrbmpJSEpOT0NwNlYwWFhOblZtT0ZMdTN3UDIxTzBWeVVtQ2ZzblU3Mndqd3RPRjVLeHJMMHE0bDBjb1BCUm1xSE0xOGVyQnFqMDhBMk9kaGhsT3IrVDZiSGg3b1VSU21tbERWRVBUUU4vODAyNmtPemIwenhoUmo3WG44TUpnQmxPeWxJZUxtM1lFTHJSTkZvMUVhL01tRVlTYm41VmNjZEhLajVzSGp1NituTndXQjdWb3FTUiswMld2RkVRZS96d0NvYjRuUGxhbm1JWTBuSTl3d01nODJ4S2U2SlpjeUdxOGxYR01QR0RNdzZ6SmxWdG03QVdhNi8rK1pOcStnelZIbVRIU0tmYXZWV01kYkhWT2w4Yi91c3o5K0NpTnZFVEZhRGZWZUMyai9EZCtCOVBhU0t5T0dmR1FIOEdlc0tZWmdqalEzSU5ZSG9KcWxNMnoxdTk5bGNhcEpYdWtmeDVjOUNPaHpVTE9xMmlOWjZyQjkveDRpbVBvNGwxRi9ZK09rSnpBT2ZqcGVNaFhJcWdYTE1qYUJUVW5ZS09ncHNPRHJib2JHS2hyVnYzZVlNYkJYY25EdFNESzdXcVJsdmVtNmRQS1ZWSXpYMG9QUDJHNXJmVVFKWmpnSDZlT2g5WXRPUlpIRFpsOE1yRHA4dFNmQlZBaXUzSlUiLCJtYWMiOiIyZDQ1YTM4YThkOTAxZGYxODhmNjQ0ZTdmNTk3YTQ3YWQ1YTg4OTNhMzFkMjQzYjkxNGM1MTlkOGVkNDA3OTZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1196191969 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196191969\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-319577350 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:13:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ing0NlY4d0tEaG9rL0JhM3A4R2l1cVE9PSIsInZhbHVlIjoiazlsd1JxUllLckVGc0d0NGpJdlJhSVlOMUNsbE5lcTNSNkJnTkVYb2xxOWJqQkVRK3FxUis0cGdMWlpITFFEN3gvelplWXV4S0ZjNXZ4ZkhIZ1BodW1SVk5ZTFV2cHVodlFlNVQ2VDJzZmdTNEp4NWZrVkxyNG1RODlrM0ZYR1UwNEFHdGxJRHNmYlVQSU1pR3pvMTB6T0Jwc1BWbnRkR1hkWndSL0RvUHF3UEltVlM0d1pxL2xMYlo4QlVIREdTS1BGMzdqMWc4VHJaZHZsWGFKb3d4UkUxYkhic01qUDJWTXNLVzNHYlZ3YlhldXRkTmo2blp0dFpmdlIzMGtXakhVcEVmdUJwV3hFYzNNTVR1ai9rOEtTN0wxNjcyQzFCLzdndEJoY2NRd3lXdzFUN09DSEtXbXB1ZlZGcDJHMHkyQkZGNGFoUkdOWTNIQWsxSzd3RG51eFJXL1ZXUTVuV3dQakxzOGlSRk9HTyt6akpqN2t1endhNGFYQ3RESG1wRXBsU0tIRHo2em5RZVQ2REVsbm1nV3lEWlM2eDRoRWdHMGV1ektja2V5ZG5mL0lNUEpYOW94NDZWbE55YVlVZHN5d1AzWUh1aG1XUUNFbjArdC9lQkZHR2JkQzJISE96WnBPNmd0L3U2WWx4WDRFbm81M3pFYkpsa0VwcmYrUkEiLCJtYWMiOiIwOWY4OGQyNDBmNTc5YjI0YzExMzA1NzMxMjYzOGJhZmYyOGUzZjg2MzhmODIxNzkwM2NlZWIwYzI5N2ZjMmNiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:13:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImlaeDNpMC9tK2VTU3JTMGlkdDFNT2c9PSIsInZhbHVlIjoiTno0MXNFR29JYjFpSXdoVXAzaUhBRVhIKzdqY1dMazI5OTZ6ZDB5Vzh0NUc2VDVkRnNxOUJzSGVJbkFmazc3TUhhS3RkY2tYUU95MFlzMFo0ZnRwbmRCdCtBdnd0cXYvOW9halJJdTM1bGtlT1pxTTNpclhZVzJQQmk1SUNzWVI4aXMzSGJVWUFxOHZmSTM3NEJVWUIyclZ2R2tiSG1Jbys1UTRDZ2R4NW9DOHgrMTVrQUlVaU9rZmVMMkdaUmhCQmRDTG4zd3crbk9lUTltVTk4QUNqUStRajlwWmdHQnNCcXFyY3JFQjhtNnZxVEx1UkQ1R0pQM3JzTkkzNHR0eXpRQlhqRWJnaXZsVFdacTFUd3BMa2Q2OFEzVkR3ZnM3QXR4NWZHNkFZV2hEZ1V4M3Y3UnAyeE00SmZyUU9tQ0VWeEV0Z0dyV2VrUDhyOU0xMDhCS2lQR3BOc0RrTlBZRHNMVU5hbVpiNGdyTE5sZGRBazdEWjMvcHNaWkE0b1dmMVNocWo2KzBEVEN6ZllXRjNvLy92eUJOU1ZnOXVTcnJoMFVEZDBsVnF6aDQ5YXAvR20yZCtVV0ZKT01QRDVMcGkwMGlWRmZIOEpzbDlBSkIzYlorbkIyNkZjdE9iM3hRS01JMTFQd1NYaElwYjFYMjdtRHpBdVBvVGdFc0Q3Uk4iLCJtYWMiOiJmOWU4MTc4NTcxOTEwNWFmMzNjNzAyNjQwYTI1NzA2Yzc1OWY3NWQzNGUxOTA2ZDI1YjgyNTBmNTc5NDgzNzZmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:13:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ing0NlY4d0tEaG9rL0JhM3A4R2l1cVE9PSIsInZhbHVlIjoiazlsd1JxUllLckVGc0d0NGpJdlJhSVlOMUNsbE5lcTNSNkJnTkVYb2xxOWJqQkVRK3FxUis0cGdMWlpITFFEN3gvelplWXV4S0ZjNXZ4ZkhIZ1BodW1SVk5ZTFV2cHVodlFlNVQ2VDJzZmdTNEp4NWZrVkxyNG1RODlrM0ZYR1UwNEFHdGxJRHNmYlVQSU1pR3pvMTB6T0Jwc1BWbnRkR1hkWndSL0RvUHF3UEltVlM0d1pxL2xMYlo4QlVIREdTS1BGMzdqMWc4VHJaZHZsWGFKb3d4UkUxYkhic01qUDJWTXNLVzNHYlZ3YlhldXRkTmo2blp0dFpmdlIzMGtXakhVcEVmdUJwV3hFYzNNTVR1ai9rOEtTN0wxNjcyQzFCLzdndEJoY2NRd3lXdzFUN09DSEtXbXB1ZlZGcDJHMHkyQkZGNGFoUkdOWTNIQWsxSzd3RG51eFJXL1ZXUTVuV3dQakxzOGlSRk9HTyt6akpqN2t1endhNGFYQ3RESG1wRXBsU0tIRHo2em5RZVQ2REVsbm1nV3lEWlM2eDRoRWdHMGV1ektja2V5ZG5mL0lNUEpYOW94NDZWbE55YVlVZHN5d1AzWUh1aG1XUUNFbjArdC9lQkZHR2JkQzJISE96WnBPNmd0L3U2WWx4WDRFbm81M3pFYkpsa0VwcmYrUkEiLCJtYWMiOiIwOWY4OGQyNDBmNTc5YjI0YzExMzA1NzMxMjYzOGJhZmYyOGUzZjg2MzhmODIxNzkwM2NlZWIwYzI5N2ZjMmNiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:13:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImlaeDNpMC9tK2VTU3JTMGlkdDFNT2c9PSIsInZhbHVlIjoiTno0MXNFR29JYjFpSXdoVXAzaUhBRVhIKzdqY1dMazI5OTZ6ZDB5Vzh0NUc2VDVkRnNxOUJzSGVJbkFmazc3TUhhS3RkY2tYUU95MFlzMFo0ZnRwbmRCdCtBdnd0cXYvOW9halJJdTM1bGtlT1pxTTNpclhZVzJQQmk1SUNzWVI4aXMzSGJVWUFxOHZmSTM3NEJVWUIyclZ2R2tiSG1Jbys1UTRDZ2R4NW9DOHgrMTVrQUlVaU9rZmVMMkdaUmhCQmRDTG4zd3crbk9lUTltVTk4QUNqUStRajlwWmdHQnNCcXFyY3JFQjhtNnZxVEx1UkQ1R0pQM3JzTkkzNHR0eXpRQlhqRWJnaXZsVFdacTFUd3BMa2Q2OFEzVkR3ZnM3QXR4NWZHNkFZV2hEZ1V4M3Y3UnAyeE00SmZyUU9tQ0VWeEV0Z0dyV2VrUDhyOU0xMDhCS2lQR3BOc0RrTlBZRHNMVU5hbVpiNGdyTE5sZGRBazdEWjMvcHNaWkE0b1dmMVNocWo2KzBEVEN6ZllXRjNvLy92eUJOU1ZnOXVTcnJoMFVEZDBsVnF6aDQ5YXAvR20yZCtVV0ZKT01QRDVMcGkwMGlWRmZIOEpzbDlBSkIzYlorbkIyNkZjdE9iM3hRS01JMTFQd1NYaElwYjFYMjdtRHpBdVBvVGdFc0Q3Uk4iLCJtYWMiOiJmOWU4MTc4NTcxOTEwNWFmMzNjNzAyNjQwYTI1NzA2Yzc1OWY3NWQzNGUxOTA2ZDI1YjgyNTBmNTc5NDgzNzZmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:13:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319577350\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-64570642 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/expense-categories/list-ajax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64570642\", {\"maxDepth\":0})</script>\n"}}