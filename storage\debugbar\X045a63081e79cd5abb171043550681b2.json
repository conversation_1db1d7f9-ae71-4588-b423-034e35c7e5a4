{"__meta": {"id": "X045a63081e79cd5abb171043550681b2", "datetime": "2025-07-31 12:12:39", "utime": **********.334558, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963957.402844, "end": **********.334632, "duration": 1.9317879676818848, "duration_str": "1.93s", "measures": [{"label": "Booting", "start": 1753963957.402844, "relative_start": 0, "end": **********.06282, "relative_end": **********.06282, "duration": 1.6599760055541992, "duration_str": "1.66s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.062844, "relative_start": 1.6600000858306885, "end": **********.334639, "relative_end": 7.152557373046875e-06, "duration": 0.27179503440856934, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936976, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1604\" onclick=\"\">app/Http/Controllers/FinanceController.php:1604-1663</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028320000000000005, "accumulated_duration_str": "28.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1923418, "duration": 0.023960000000000002, "duration_str": "23.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 84.605}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2619662, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 84.605, "width_percent": 7.133}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.280721, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1630", "source": "app/Http/Controllers/FinanceController.php:1630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1630", "ajax": false, "filename": "FinanceController.php", "line": "1630"}, "connection": "radhe_same", "start_percent": 91.737, "width_percent": 8.263}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense-categories/list-ajax\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-14691110 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-14691110\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1862040094 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1862040094\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-238245132 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-238245132\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1960876870 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjdzQ3ZCM1VYYndCd0Nyd1J3WlRySXc9PSIsInZhbHVlIjoiTHRRRkhWRjVId2VPNXErU2hEMVlwbzl6cWk3VXMwNEFSdWExM2laM1JFN1hmTks3MDg4M28rbXRSQUNKTGpDOTR3ODBTYXFqOHIybHQ3cE9hdGJsWktjY0dQMkVrRUZnNkxMMmJzeTBEQXd3WFpQVjQ4MFdQUFpqSlE4MzY0czJMTDRsR2wxbHNxcmJjY0tKSnEyUEJIRk9LMmF2Wjllb0ZTNUZjT2E1cHVaOC8rcklGeElyMWd1VFRmUzFVS2FvSmVkTkY2bFdhNkFPOXZWZytJWktCc1Y3UVE1M2xBVFpHNWhjeUtSRmJoRmE2cWsxeEh5bnFwSW0zN1BCYTg0KzNNTWdaby9GNWJPV3ZUSEl2ZDZZVUlaTnh6bVdtVkw4bGt4NU8zYkhKRW9lQ2JybjlUb3ZCaXZzOUxFd1BXV1dZUS9rN3NYQnJCRDVRV0pkb0JOK2hZNStpL1ZEQVFWdU5aZ002c3A1am9tSDBlaGRueW5DbmI0TTlFM2NFRXg2YnExTmdmTVI3eUNMWWhYU3VYSmhmdWxiSEZSVkVqZ0h4WHprTnR2amZXTnVOK1g1OWRJdGtGU3FZblJ1QVJZMnphbXA2enppVU90Y1FWdHRDVVlxM01aeDZyRFlCaExiVjl0ZzJGQ2lSQnBEdVpoSzZCZHZ0QTg3UnhRSUF4dVciLCJtYWMiOiJjYjg1NTUyODRmOTM3MjA0ZjNmMzVkMDIzNThhNWE3NzAzMjY4OTgzNzlhMjNkNDljZDE3YmUxMjNjZmVkZDQyIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkM4eTV2T2p4bEpKTWhQT01zVmxxOVE9PSIsInZhbHVlIjoiRVdrVk8rT1FDNWRRUWNUOUp0eWh6MnNrNjdJVEZueVdjM0NFcm9UL1VydS9vckgzbW1FVkpFTGgrNVk0c0FpSkpUazcwWk1IRmtYNWxYc2dKdkNqOFI4KzF5QUFMQWpCWEFZc2UxcnFEdGNqeE1hcmx1M0UwbE55YlkvTDRiMUxUV0E2ZUltQ1BSU0FJcVUrcnRJcU41dWlGcVhwMlRML21wNmZOSmhMWVBuYzY0c084Um4yRDFRRHd6T0laZGpHV1hOVnNRbGVJNG1ObW80V2xSNEw2WkdUSFNlUFhSQ2FiTlQ2WDRqMU1wd1lJcXg1Q0pmbE4zVUZucWxQTnNuVVBmd0x6L0oxUUh5ZnRIazVaTkphYWtxNkpuM2FkSUo1UXd5MVJYcDNiSmZvenpYdEw0d2Zja1F1TjR0aUpXRjBveURIRVRBMFJaeVpvTlMva1lIUGlPSW9hek84cWZUNVJVbnovNDJYN3NKbW5qZVJxZmlJSFd4byt0a1V6VnU1WHhIbGJhTDdMdlBGUjNaUzNMYzUvbU4zeVpqdGcybFJMaFdPamxEdEZiT1pyaFpGNVJnN1ZtaytPTjlsMnNnMDl2ak0wRVl0THNzNEZzNW5wZ1NQSmhBS3lGU2JrOWlRS2twaFo5aFdIZGFaVmpFRU9MMEFDdGpTY1NST0FHeUYiLCJtYWMiOiJjODRhNjU4ZmFlNzkwN2VhYjA3NTYxYTAyZmM1ZWE0ZGQ4Yjg3NGUxNjlhMmEzOTU2NTVjMDE0ZGZhOTcxNjU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960876870\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1174669758 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174669758\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-47130180 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:12:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InF1bGp6Y0YwcGdxNncrUUtmL24vdWc9PSIsInZhbHVlIjoiL1ZVVHcvcklqL1VaQ2NkekVONWRBaFYvZFV4MUJHNDJMM1pIMWVBQmIzN3NjcnZpNGU0MmJmQm4vYzVCcHlnc0RnZDNrWitPbytucVQ0TlBmYXZzS1RNdkFnZU5DMDFDK29iZjlPNnhYQXE0VWpmd1REYTRObGV0MkF2UWFwaG9qMnpoSWozc1E2eDA2VFdWNjNpc1ZGMDlnbjNGVUE5S3JmdTkwMUJaM3NlTG1QZFArRlRhVnlZMnJtREU5OS9zMXdtM2pQNWlKK0RhdHZMalhnY0ZhRkplQTVnUnJzVjN0cStjckFoR1VnTzZrODFLOHFXV2tUM0VhZlFqTTJKUis0VE9IRFdxUEx5OVdLVlFCRER5NmRMaXVocVU4dkZDSk51WGxnUFhuejR0UVhSZzNUUXo2L3FaK2g4bE9ORXg1VXFuS3FuWldleVpEODZ1WHBLNnMxZWdkMCsxbk1VY0tUQlk3Tm9LS3BKOFpWWVN3Rm9LQ3Jicys2eHprVWlOOHBramlyaG5QNDNNRll0TTZSMXlqY3NwbkEycWNzM25MMlNoY3M4dUwzNHJpU0tDbVBjY2J1L01kKy9lOXFxQmRjYzNNV0R0cVVNR0ZZY2lPWm5QMlRoNmJXME1WYUdiM0VUbkVLSWVvKzJESXl3czBxZmlvS082NWFXV0dTVmsiLCJtYWMiOiJkMjljYjUwODA1MjU0YzFmODMwZWIwMjQ0NDA1NDA5OTIyZjJlNjY5MDE4ZTZjOTQyODk5ZTcwZGQwYTc0ODhhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:12:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IitKRWUwVE5QNTFhaU10U2E5NlZ5NEE9PSIsInZhbHVlIjoidlA0dTB2MFVOTXUyVlFhK2RPalpqR0sxQVdlWDhxMnRSaDFPK3EveDhGRENNNXpCZGlpVnhnWENBeVNRYStwNTgrKzFxNVRYY0ROczBxQmw2OEk3SkwyY3NjbXBWTjZmOG1adlFJT0kvSlJzV2FVSkNrVnZuQkMzbGp3OGl1NVBMNU1NTHJNc3YxTWxqckxxNkJ2ZmdJVkdKOFBaWHpRc2pNQlpHdk1nR0UyQmx4bjhKUWFoczNtVURGaUZHcTBIMlVjNnYzeXptWkF6WmFNaUg0NnhaNVJNZGZYMXAvb25uWmxmSWk3V3FWY3ZrdEhhdzBuc0xSYm55eWwveXhCKzRhSk1Ddmg2dWdWd3RmTVJlbXdYUDNTYnhYYTlHdnNjUVUyUUFaTzJFcHRScEw3WFM1Q1Evc2pDbk10MXB4RWVseXpwekxtYmNSV0tRZ0hyT1JrVFhTTUdacm9JelBOdEg1cDg5bnJzYlZzeTN4YW44ckY3clc0aEl1cWk2L0tKYUcrMkh5ZXNscHVFbm82WTVEMEdheG5aZ3BKbDRBVmx6RW0vSmwxbGNqZCtsQ1RyRFlpNGx5cE9oZWNHM2pFbUZaQmtYYzByYk9yWTZab056ZFhSTi9YdkNKdy9GQ2UzNVJsWkRGUloxUk5jR05IM3lrcXZISjZmc0ltNzBpN2siLCJtYWMiOiI3MzUzNjEzNDM5MDU4ZTk5NDFjYjMzZTI1ZjE1NzcxNzY0ZjZiNDk2M2FhZGZkOWZjYzhlYTk3NDU3NmFkZTg4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:12:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InF1bGp6Y0YwcGdxNncrUUtmL24vdWc9PSIsInZhbHVlIjoiL1ZVVHcvcklqL1VaQ2NkekVONWRBaFYvZFV4MUJHNDJMM1pIMWVBQmIzN3NjcnZpNGU0MmJmQm4vYzVCcHlnc0RnZDNrWitPbytucVQ0TlBmYXZzS1RNdkFnZU5DMDFDK29iZjlPNnhYQXE0VWpmd1REYTRObGV0MkF2UWFwaG9qMnpoSWozc1E2eDA2VFdWNjNpc1ZGMDlnbjNGVUE5S3JmdTkwMUJaM3NlTG1QZFArRlRhVnlZMnJtREU5OS9zMXdtM2pQNWlKK0RhdHZMalhnY0ZhRkplQTVnUnJzVjN0cStjckFoR1VnTzZrODFLOHFXV2tUM0VhZlFqTTJKUis0VE9IRFdxUEx5OVdLVlFCRER5NmRMaXVocVU4dkZDSk51WGxnUFhuejR0UVhSZzNUUXo2L3FaK2g4bE9ORXg1VXFuS3FuWldleVpEODZ1WHBLNnMxZWdkMCsxbk1VY0tUQlk3Tm9LS3BKOFpWWVN3Rm9LQ3Jicys2eHprVWlOOHBramlyaG5QNDNNRll0TTZSMXlqY3NwbkEycWNzM25MMlNoY3M4dUwzNHJpU0tDbVBjY2J1L01kKy9lOXFxQmRjYzNNV0R0cVVNR0ZZY2lPWm5QMlRoNmJXME1WYUdiM0VUbkVLSWVvKzJESXl3czBxZmlvS082NWFXV0dTVmsiLCJtYWMiOiJkMjljYjUwODA1MjU0YzFmODMwZWIwMjQ0NDA1NDA5OTIyZjJlNjY5MDE4ZTZjOTQyODk5ZTcwZGQwYTc0ODhhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:12:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IitKRWUwVE5QNTFhaU10U2E5NlZ5NEE9PSIsInZhbHVlIjoidlA0dTB2MFVOTXUyVlFhK2RPalpqR0sxQVdlWDhxMnRSaDFPK3EveDhGRENNNXpCZGlpVnhnWENBeVNRYStwNTgrKzFxNVRYY0ROczBxQmw2OEk3SkwyY3NjbXBWTjZmOG1adlFJT0kvSlJzV2FVSkNrVnZuQkMzbGp3OGl1NVBMNU1NTHJNc3YxTWxqckxxNkJ2ZmdJVkdKOFBaWHpRc2pNQlpHdk1nR0UyQmx4bjhKUWFoczNtVURGaUZHcTBIMlVjNnYzeXptWkF6WmFNaUg0NnhaNVJNZGZYMXAvb25uWmxmSWk3V3FWY3ZrdEhhdzBuc0xSYm55eWwveXhCKzRhSk1Ddmg2dWdWd3RmTVJlbXdYUDNTYnhYYTlHdnNjUVUyUUFaTzJFcHRScEw3WFM1Q1Evc2pDbk10MXB4RWVseXpwekxtYmNSV0tRZ0hyT1JrVFhTTUdacm9JelBOdEg1cDg5bnJzYlZzeTN4YW44ckY3clc0aEl1cWk2L0tKYUcrMkh5ZXNscHVFbm82WTVEMEdheG5aZ3BKbDRBVmx6RW0vSmwxbGNqZCtsQ1RyRFlpNGx5cE9oZWNHM2pFbUZaQmtYYzByYk9yWTZab056ZFhSTi9YdkNKdy9GQ2UzNVJsWkRGUloxUk5jR05IM3lrcXZISjZmc0ltNzBpN2siLCJtYWMiOiI3MzUzNjEzNDM5MDU4ZTk5NDFjYjMzZTI1ZjE1NzcxNzY0ZjZiNDk2M2FhZGZkOWZjYzhlYTk3NDU3NmFkZTg4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:12:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47130180\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722864768 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/expense-categories/list-ajax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722864768\", {\"maxDepth\":0})</script>\n"}}