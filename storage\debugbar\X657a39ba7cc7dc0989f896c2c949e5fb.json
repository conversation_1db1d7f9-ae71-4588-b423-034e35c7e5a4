{"__meta": {"id": "X657a39ba7cc7dc0989f896c2c949e5fb", "datetime": "2025-07-31 12:29:43", "utime": **********.718044, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964981.210735, "end": **********.718111, "duration": 2.507375955581665, "duration_str": "2.51s", "measures": [{"label": "Booting", "start": 1753964981.210735, "relative_start": 0, "end": **********.302971, "relative_end": **********.302971, "duration": 2.092235803604126, "duration_str": "2.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.303014, "relative_start": 2.****************, "end": **********.718118, "relative_end": 6.9141387939453125e-06, "duration": 0.****************, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3060\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1892 to 1898\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1892\" onclick=\"\">routes/web.php:1892-1898</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01455, "accumulated_duration_str": "14.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.583139, "duration": 0.01455, "duration_str": "14.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1384138606 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1384138606\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-638036767 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-638036767\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImZiY0RPZVQvVThYS1pMZ1lZWDRySUE9PSIsInZhbHVlIjoiMk1VdVhkWHk2SFh4QVp1WU1odWNOT3pGZXdqbXpETU5GOVpCd281L0FFQVhnZ285VllENnZMNGJGak9JZGMraTlsMlpBMzJJUTdLeXEvSTY5ZnBEYkRXMno2VXZhWEJpSE5mcDk3UktmalFZd1N3RnJEZ25PUnU4MHdmN0dsN2xCMzA1VkhoY0ROcngzNFpFOGx3eGtFNDUybjJEVDV0WDNOWjZROGNHbjI4NEJxWGVwbG04TEsvQ1V1K0xGVFNRNTN4OVBWU1hpWmVockRHY0d2OXJpYWNzUm9seThsVWMzdjdGWGRJUUVBRVV4Tkx2clR4d1FLeStwNDdZTFZEekw4c05HQVlKTldCc0ZxWDV3a1dRaHpITWRVeHlIU0EyaDRYbjA2UXhlbHpLR2kxSmo3WWdKSGZzRksrMXBPejF6V0UrdjBoOHhlWkFtNC95RENHN0lFMTZteWluUHBuRVErTWNhcnp6WkVlRkQ3cjdXUDR4SkRWcEl3NS9yRGtKUVFJUGYyVFUxWUxhU1FOM0VybmttL0EyR1RMVjFBbXVEQjFvN1VxR2ZZNy9iWGpqT1VFMWpxUWhQUEhMSTcyZFM2V3R5OEFjTm5kdzJKbEUwVkthcFN5NVBMZFZSelJXYUxMdVRRVElqUEVaWWsvbWNFWjd2SHd3cWxoZnpOZWEiLCJtYWMiOiJhNGY0ZTU5YmNhZmU1NGI1Njg1YTdjZmY5MDMxNGI1MjM5OWEzNDk3ZmMzM2EzYmU5NDFjNWY0MzJjMjJkNWY3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InBmR281cFJTeG10dXI5TXV6Z1VwWXc9PSIsInZhbHVlIjoiZFJOQWRtUVZHS2o3N25HMnhGSWxQdGFEeTZOODNuMmV0NTZxdUMzcjNteDlYa3RLdG1jYmsxR0NucnMrSHVrQ0s2MjR5WEZEcTVlbks3WlloWmdpQ1VHaUg4a24wQmM4OWs3ZUZxUGNqczluT3pxUG43QW9Ga1piR1IyM0hiREF6S29rUkpndFZwMWlTSnY4QXlvLzk3ZVZiTnp6eGNRZmJWZzd2ODkvMTl4OC9IWlR1OGc2dythYmVKR1ZwODhHUzNEbjdaajM5UE56WFN3WGZQdzU1QzJYWnNQWCtOM1A2a3VHVG5jSUpwNEhkMVlGRFRhSTRXNjd0WEFQQXRKZi9mdmhKemN3eWIwTzBMN1FuTkZuM0JOV0Q4MFFESytYbnF2QTVWVjVLZ05VYStRWGF0eStmSUxUcldNQW9tcjZPaDNHMGlKeTBBOWZ5Z241MTNpRFNUcllBZ2xyVkhMTHRTQndpdnNtT2ZPb1RrRmNKZTdZL0JNdmE2WGU4c2xLWHpEa2xFL0x6Rm01VXUvZnJ4RlJzZm0zM2xzb3NVRjgySmpySXJOakhpeW5WQVd1OVMrSkZiaEJRdGNYSWd3aDFrSFl6TnNBU2s3MzF4TnNkUzcwQWhXR0ZXV0xaSUtRbDVXNEd0d0VhcmoxNHZubnFRd21Sd0lSVkdCdWhBMnMiLCJtYWMiOiJkYzZjZmE5ZWU3MjQxMzZkYWMyZmQ3MDdhYjBiOTQzYmU5N2ExZTkwYzRhZjg4YTA4NzBjNTEzYzNkNGNmOWQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2027118637 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027118637\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2021364282 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:29:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNudG1PdWpENEU3bTBHK05PaVR0T1E9PSIsInZhbHVlIjoiNWEzVnRSak5zVHgxbFNuODk0WCtrRXlvNjB4SjRmbHZwSU53dFNSWE1aNlJzNS81cW9qWEZVRnRRZ0puRFJpU2tZa1ZucWwvUnQrMkp2SGhlTXIyWnpZNmNwS2dmRlRxeFh4ZXZCMWlwd1VnMjhDeWlsYVB3aHRaVFA2eFNkSTgxUUdpbWM1cGthYlNDaStBRDR5dFg2aHZwbWFDRkxGaFZlbDhvK1ZockFsd0RKbU9iZVozY1FUbFMxYXVJSmxGOXNYZWkrTnJxNVFFdUx6N1NaM2pReC95eXpGSGVONEhjbmc5a05PMnhYSVp1QTZlRzVVTmRHMURJdkZsWllWalFjRlVVWWNLc29USnJHcTEycVRwSmIzcGZBM0lXY2JCbjk0dSs3T2lYeHhkdVY3T1Qyd3BHRGJLL1ZpU2s3S3BSZFd4TWM0Q0JidENKYmUyTDFHbURERFRFMHdMelJIQ3dzdWtLNFA2VTRzMTFwZ29Fa28vWDQ3M0xuaVpjVTlKVmpWUXNiZndGQkc1QWx3TkpIOWtjT0pxeUZtR0JlTEp6dmR6My9RbXR4YVVCUFZPbXg4N3IyWjJkL3BBNzRIZDBEU1J1aUZYcXdETVdXbDVRUHpYM2FuSWxKSCt5dUFUTE1XU3dTVnVYNHJjTHFJbmNtSVhsZk9pWlhGQ2JKbzIiLCJtYWMiOiJmNDY0YjViNTZjY2M5NGNiMzQwYzRiMDBiNTEwNDM4MmE2ZGQ1MzE3ZDE5ZWRjYWE4NjU1OTMzYmFlYjFmYmFlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:29:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkhMZ0h4eVdHYWR1UzBlUCtaUDVDV1E9PSIsInZhbHVlIjoib09kc0xDS1BiSmRQUEY1ZkJKaitwMlJqMzYyQk9ZeFBlbTBCQ1NqUU01cFBTQ2lIZHBBYUZ3MHJFTFBGUEczcDJXWWc5aUhXUTN4Y0Rhc0VCaHZtZmZCV3ZYZG4xdWl1YXFqUkYrc0NOV0owZzY1Y29JQjlKMUVHRkhCeEk1WUVwYUR1OUNWRGYxczdJQ1JhUjkxMmpVdXZkSktSc3VrUmFpcnhPbjV2MnFwbGFVdUtNc2VXNVh2TEhqbElsSThKVGhkL0xxWUlQY1Byei8raE4xcDkxaHYxakpDdTdDajRKYVBJR21rSE1raDNMdk9vZk9kVFZrcWNia2hUREI4aW5UTEt4NjRPb2M2Q2xFcDVmL01ZcmJONXFDU1F3OHRZbWNha3lzdDlKWFVaTksxNXg5NWRuWlJZRFhQNlhoRzg1RzlZZmZIMXgvQkFVU2pIQ2t6WExhOHIvQzY0NmxiNmJrSXQvRnRxUzNoaUpMVTg0S3EremJoYklzNDYxblc4R0ZsSjhzY0RSdTZ5N0dCK29jc2dsV2dhaDdwdFNwcDFvNmlEdDEwMWR2czNxRW5EcFNCWGJVRHVUOG1aQ1pTQ1BiOUZ3MjBEdmkwVEE1YWt6dzkyOElrcWFDRkk3RWxUeTdKb29nMlZtb3Q3SlhwdFNqNWJ0ZnlodXUzbXZuSnUiLCJtYWMiOiJkNjg5Y2IyMWQ3OGJiMDQ4YzI2OGU2MmIyNTE0NmY4NzM3ODZhMzdjNzA2NTg3ZWNlMmM2ODc1MmE0YjU3MDdlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:29:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNudG1PdWpENEU3bTBHK05PaVR0T1E9PSIsInZhbHVlIjoiNWEzVnRSak5zVHgxbFNuODk0WCtrRXlvNjB4SjRmbHZwSU53dFNSWE1aNlJzNS81cW9qWEZVRnRRZ0puRFJpU2tZa1ZucWwvUnQrMkp2SGhlTXIyWnpZNmNwS2dmRlRxeFh4ZXZCMWlwd1VnMjhDeWlsYVB3aHRaVFA2eFNkSTgxUUdpbWM1cGthYlNDaStBRDR5dFg2aHZwbWFDRkxGaFZlbDhvK1ZockFsd0RKbU9iZVozY1FUbFMxYXVJSmxGOXNYZWkrTnJxNVFFdUx6N1NaM2pReC95eXpGSGVONEhjbmc5a05PMnhYSVp1QTZlRzVVTmRHMURJdkZsWllWalFjRlVVWWNLc29USnJHcTEycVRwSmIzcGZBM0lXY2JCbjk0dSs3T2lYeHhkdVY3T1Qyd3BHRGJLL1ZpU2s3S3BSZFd4TWM0Q0JidENKYmUyTDFHbURERFRFMHdMelJIQ3dzdWtLNFA2VTRzMTFwZ29Fa28vWDQ3M0xuaVpjVTlKVmpWUXNiZndGQkc1QWx3TkpIOWtjT0pxeUZtR0JlTEp6dmR6My9RbXR4YVVCUFZPbXg4N3IyWjJkL3BBNzRIZDBEU1J1aUZYcXdETVdXbDVRUHpYM2FuSWxKSCt5dUFUTE1XU3dTVnVYNHJjTHFJbmNtSVhsZk9pWlhGQ2JKbzIiLCJtYWMiOiJmNDY0YjViNTZjY2M5NGNiMzQwYzRiMDBiNTEwNDM4MmE2ZGQ1MzE3ZDE5ZWRjYWE4NjU1OTMzYmFlYjFmYmFlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:29:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkhMZ0h4eVdHYWR1UzBlUCtaUDVDV1E9PSIsInZhbHVlIjoib09kc0xDS1BiSmRQUEY1ZkJKaitwMlJqMzYyQk9ZeFBlbTBCQ1NqUU01cFBTQ2lIZHBBYUZ3MHJFTFBGUEczcDJXWWc5aUhXUTN4Y0Rhc0VCaHZtZmZCV3ZYZG4xdWl1YXFqUkYrc0NOV0owZzY1Y29JQjlKMUVHRkhCeEk1WUVwYUR1OUNWRGYxczdJQ1JhUjkxMmpVdXZkSktSc3VrUmFpcnhPbjV2MnFwbGFVdUtNc2VXNVh2TEhqbElsSThKVGhkL0xxWUlQY1Byei8raE4xcDkxaHYxakpDdTdDajRKYVBJR21rSE1raDNMdk9vZk9kVFZrcWNia2hUREI4aW5UTEt4NjRPb2M2Q2xFcDVmL01ZcmJONXFDU1F3OHRZbWNha3lzdDlKWFVaTksxNXg5NWRuWlJZRFhQNlhoRzg1RzlZZmZIMXgvQkFVU2pIQ2t6WExhOHIvQzY0NmxiNmJrSXQvRnRxUzNoaUpMVTg0S3EremJoYklzNDYxblc4R0ZsSjhzY0RSdTZ5N0dCK29jc2dsV2dhaDdwdFNwcDFvNmlEdDEwMWR2czNxRW5EcFNCWGJVRHVUOG1aQ1pTQ1BiOUZ3MjBEdmkwVEE1YWt6dzkyOElrcWFDRkk3RWxUeTdKb29nMlZtb3Q3SlhwdFNqNWJ0ZnlodXUzbXZuSnUiLCJtYWMiOiJkNjg5Y2IyMWQ3OGJiMDQ4YzI2OGU2MmIyNTE0NmY4NzM3ODZhMzdjNzA2NTg3ZWNlMmM2ODc1MmE0YjU3MDdlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:29:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021364282\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-57976651 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57976651\", {\"maxDepth\":0})</script>\n"}}