<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductEmiOption extends Model
{
    protected $fillable = [
        'product_id',
        'emi_month',
    ];

    protected $casts = [
        'product_id' => 'integer',
        'emi_month' => 'integer',
    ];

    /**
     * Get the product that owns this EMI option
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get formatted EMI duration
     */
    public function getFormattedDurationAttribute()
    {
        return $this->emi_month . ' Month' . ($this->emi_month > 1 ? 's' : '');
    }
}
