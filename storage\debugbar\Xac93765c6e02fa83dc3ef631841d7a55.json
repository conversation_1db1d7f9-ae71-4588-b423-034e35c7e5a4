{"__meta": {"id": "Xac93765c6e02fa83dc3ef631841d7a55", "datetime": "2025-07-31 12:16:58", "utime": **********.428328, "method": "GET", "uri": "/invoice/contact-details?contact_id=lead_12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964215.964179, "end": **********.428385, "duration": 2.4642059803009033, "duration_str": "2.46s", "measures": [{"label": "Booting", "start": 1753964215.964179, "relative_start": 0, "end": **********.070353, "relative_end": **********.070353, "duration": 2.1061739921569824, "duration_str": "2.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.070389, "relative_start": 2.1062099933624268, "end": **********.428391, "relative_end": 5.9604644775390625e-06, "duration": 0.3580019474029541, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46309136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1593\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1593-1651</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0293, "accumulated_duration_str": "29.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.241936, "duration": 0.025079999999999998, "duration_str": "25.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.597}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.351069, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.597, "width_percent": 9.215}, {"sql": "select `id`, `name`, `email`, `phone` as `contact` from `leads` where `id` = '12' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1624}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3731291, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1624", "source": "app/Http/Controllers/InvoiceController.php:1624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1624", "ajax": false, "filename": "InvoiceController.php", "line": "1624"}, "connection": "radhe_same", "start_percent": 94.812, "width_percent": 5.188}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-1252046964 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1252046964\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1834717528 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lead_12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1834717528\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-624998125 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-624998125\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1744940864 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IitDRlJxam0xR1ptUi9GS0srYVdPZ1E9PSIsInZhbHVlIjoiWHZZM3FJOTZCZ3RPNmJWNzQxcE9BNUhtRklCRklXSm1VcEQ4UTgxTkZWYWxJOW9mUGVuNkNweDZOQ01EYjNhQXRQakhlbmJlYXhoZmJzSk5uNzU4ZmlGQ0pWQlZoejA0TG9yL2tkTXRyL2RnMXpHR0xTVkd3NkpPZkNpNG1sblp6ZTQ5elBrOS92WUlYK3RDZTBWZkY5aHNqTFFqSStNcXArQlN2NkI3Tmtmb3Vsbm5Sejl6OVdGTm0yelAxUE93K2RBVStvOGpVY1JldUJXVXp3QVJVeHM2RGpaUXY1RUJMaWFNWkRYYmgwK0dHeEFLUDhXSWlsOWRoZEVkTEVPL08zOWFrTW4zdUhpYkYxK0lLS2VzaW9JODN6YTR2MVZRaDArTXExZVcwTm5vOHZvR0JRZmdYZ0VYNkJraHJ2bkJpeExPWnlia081dC9lNTBxME10clNjblpEcDdPU3FQb3VCYTNyaG5KY09FTkE3NUNKcWpTcUxQR0dydzZ2aHpMVmNmbUVwRnlKclg4R0xiVi9QTDd6aktTYXZhYlAzSDJsQWc2S2lSYThIaEdqYzJtK2FiWmlMazNjcWlsWmdRbWxSZE5ZQlE3eWZOcmszZk9lWXQ4SVZTMzkvc09tWEpmS3N4bjVXTVNDenlpMk4zZ0hQTHRVV3NGaWVOUllMak4iLCJtYWMiOiI2MWZiMGNhYjIzZTdhZjdmOTliZjJkNjUwYTFmZTc3MzQwZWY2MDBjMGRlNGM1Y2JhNGI3Yjc3MmE3YjM5NmQzIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkN0ZjZOWlo3b0I3WTN2TWg1cGV4WUE9PSIsInZhbHVlIjoiUzZUVWxucjJ3Q1RxRDkvUis5UWw1RGphcW90eDhGSFp2Q0hFSWVlNkpnL0tZcU43NFFlam5HdlhyVHU1TmFyMjA0YWtTWElTWW8rcGVUZjhBMjVHQmFCaDRIOTlZcElsU0VMREJCVit1Ym9HbUwzUTVhY3JRcnNMbTduWGlGcGIxckhkbTdQSDN0ckoyaU80RDB3YXF1ZmdnNm5iQTAybGhYVHAyYWhiNmljNVFDL3Jka3JaVFQvRGRVQ1lkREp5MitzZGxtUmp2b0pEYUoxeVE1RGNrOEVMRzNuam9ESWNLc3JNTXhIVy8vaExzWkpXZXVuWjdWY2tNcHhvNVJ1TnJoejl0bGpPUERFbmh6dEx3NEdGZjhiL1p0N2JhSUlxMU5NcEVPT2NDdHcwQVk2QmJ4eE1oYldmRWc5ZndtSktuREUyTnBUOWRDVTBsOUtUbjVMNlFYVWlrUWxFTmIxSU5nck1pNWhhOVIvbjJMTTVjQzhScUh2cHdoYk83aHZ2MVVIQkFERjNSMWJ6bVR2N2c2WTBBS2E3a1drNmp6YW8rblU1bk9LaFpJRURZdm5McTdCcTdZOU5EWHhtaTFzSlNUZHlMMjlHcFhHNXdIbDg2ZVc4UWZ0STd4TjQ1RElhejNObWNQdkh5cFpJQXpKWjhQdUw0WDBRYWNwSGxVWFciLCJtYWMiOiI1ZjUyYWNhN2UwYTI2ZDYxZDA5YjM1NzFiOWJkNzJkZmE2ZGI5ZDhkZjlkZGYwMDdiODU5YWIwNDFhZjJjNDg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1744940864\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-618824155 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618824155\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1280177304 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:16:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNEVG9XaERKSlZvSUZiWXFaaW5IbVE9PSIsInZhbHVlIjoiK3NJdndaQUtBQ1RaT3BxUnd5akZHMUhWQ3lDSm5WbElJd08yaVVhR2lNQ0pLTmc0cWt2a2E0Nmp0MWxYMTBGM0huRWFSc2VsOWVvTzJseHRldVBvTTJhVWI0ZmVWQ0lrSG1tZ1JINWxPRlV0REk3bGZ3UGhSWGZNMnhDMkp2cktQN2ErQ1d2TjNJZ0lEdmZKSlBGYmRIdVpoSExTbjE2c0NUcmRsRndsTmhnRnh4NlJnb2xZVGlPbmJqM3hZSm9RUFoxQ3RHQm85NDUrbGFJMGU4SHEyZTQ3cDVDYi9DU0xib2E1czF6eTlPVXIrTnZSRUtQWkxYR2VBS1U1TXVxNS9IVUk1U3J3WjNmbWJZMGh5aDFsZUlTcmp2cU9LdnlNSmpnajVOR1R1UFh1LzNTRXkwZDNIekVKcnpoU2Y5OXJXWGtBaUlpN2pHMnRoK0xkRmF4TG9xTzBUdXM5MnpuS2V5a0hhV2FCQnNkRTJrbEdTN3FwL21INFZNMTk5c2dWMW96OXVRNXFMTm9XTFNBcTg1QTd6TzJqRWoyMGFjMnh1RTRqTGg4d0E5NmhBclVaQk1lVGZZVmRyYUV4RUVtWXZlb3ZWYkF4ODZzSUZPTDlqZlFLVS82ajdUYmFTWjdYUE9CTE5XL0RYbEdrREl5NVlvVFQ4UWxSeGJhSkJJWXMiLCJtYWMiOiIzMGJjNjJlMTlkNGM0ZDNiY2NiNjdkMTA3Zjg0YmIwMDhjZThiZDMzZWQyNmViMTkxOWUxMmVkNDgyODhlZTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImhYem11T0FDYWFURXhhR3Bnd3VsTFE9PSIsInZhbHVlIjoiY3JvS1dIaHl0SWQ0bktYTXN6Qy9qNFhvM1k5cGRBUm9TRzBDbjFQQTNHRXRSM0RKazRWMUYxd0FKZ0lHNG1ZT3lSQTN4cjZJU1NQVnBpNzBpTnJQOGJCNERlRFRnRkY0Rjl6OWl0a1RJdmNBQzRKREVob1J4RGhmV2kvOFA4a1N3QnZ6emtNNTRVbnorQnlXYm5FbHEvdjRqZXpwM1YrTlBTbjBlRTd3N1JLT2pMOExpbjJ3dVpOdXlYZmJCRFVtNG52THhjQlNJQkd0d1FXemN4WDA0RWRXRGJqbEdlaUNWbXhzaVVPMDBleEg1YTYzazlhSUV6aWJlQUVCV2JmSjNoM1hqVkt5VjlTQkYyUVdyREpBV2lFK1FRdTd1SWpYbjV5elVkUkNZa2tkZml1a0xvYjJYNjFHT2lHN2Q4VHF6QVI0VGJiZGRITURIVVpyNVVQRkQraVp2WmRta0FLTllkWDVZMUs3MFd6aXZleEF4S24xelNaZ2d0L2R1cGc3c3MvY3ZpakM2MjNhelhPRWEzYjVITkdTcisycTdCUHR0TG5oRVdXZFZ2T1ZUdnhoOFNFVWE4NXVBVzR5VG5iOGExekR6TVhXRVlINTBpd20vNzdMY0IyN1dVb1NsUkdKbUYyNVpETkFXdWxWcm92cHZiUC82TjJxbmVhdFJiL1kiLCJtYWMiOiJjNDE0YmZlZjE3NGJlMzcxYjRhYjJlNWFiNTBkZjkyMDk4MGU3MTgzOWZlMjc2OGE5YWNhMTRlNmZkM2ExNmUwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNEVG9XaERKSlZvSUZiWXFaaW5IbVE9PSIsInZhbHVlIjoiK3NJdndaQUtBQ1RaT3BxUnd5akZHMUhWQ3lDSm5WbElJd08yaVVhR2lNQ0pLTmc0cWt2a2E0Nmp0MWxYMTBGM0huRWFSc2VsOWVvTzJseHRldVBvTTJhVWI0ZmVWQ0lrSG1tZ1JINWxPRlV0REk3bGZ3UGhSWGZNMnhDMkp2cktQN2ErQ1d2TjNJZ0lEdmZKSlBGYmRIdVpoSExTbjE2c0NUcmRsRndsTmhnRnh4NlJnb2xZVGlPbmJqM3hZSm9RUFoxQ3RHQm85NDUrbGFJMGU4SHEyZTQ3cDVDYi9DU0xib2E1czF6eTlPVXIrTnZSRUtQWkxYR2VBS1U1TXVxNS9IVUk1U3J3WjNmbWJZMGh5aDFsZUlTcmp2cU9LdnlNSmpnajVOR1R1UFh1LzNTRXkwZDNIekVKcnpoU2Y5OXJXWGtBaUlpN2pHMnRoK0xkRmF4TG9xTzBUdXM5MnpuS2V5a0hhV2FCQnNkRTJrbEdTN3FwL21INFZNMTk5c2dWMW96OXVRNXFMTm9XTFNBcTg1QTd6TzJqRWoyMGFjMnh1RTRqTGg4d0E5NmhBclVaQk1lVGZZVmRyYUV4RUVtWXZlb3ZWYkF4ODZzSUZPTDlqZlFLVS82ajdUYmFTWjdYUE9CTE5XL0RYbEdrREl5NVlvVFQ4UWxSeGJhSkJJWXMiLCJtYWMiOiIzMGJjNjJlMTlkNGM0ZDNiY2NiNjdkMTA3Zjg0YmIwMDhjZThiZDMzZWQyNmViMTkxOWUxMmVkNDgyODhlZTMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImhYem11T0FDYWFURXhhR3Bnd3VsTFE9PSIsInZhbHVlIjoiY3JvS1dIaHl0SWQ0bktYTXN6Qy9qNFhvM1k5cGRBUm9TRzBDbjFQQTNHRXRSM0RKazRWMUYxd0FKZ0lHNG1ZT3lSQTN4cjZJU1NQVnBpNzBpTnJQOGJCNERlRFRnRkY0Rjl6OWl0a1RJdmNBQzRKREVob1J4RGhmV2kvOFA4a1N3QnZ6emtNNTRVbnorQnlXYm5FbHEvdjRqZXpwM1YrTlBTbjBlRTd3N1JLT2pMOExpbjJ3dVpOdXlYZmJCRFVtNG52THhjQlNJQkd0d1FXemN4WDA0RWRXRGJqbEdlaUNWbXhzaVVPMDBleEg1YTYzazlhSUV6aWJlQUVCV2JmSjNoM1hqVkt5VjlTQkYyUVdyREpBV2lFK1FRdTd1SWpYbjV5elVkUkNZa2tkZml1a0xvYjJYNjFHT2lHN2Q4VHF6QVI0VGJiZGRITURIVVpyNVVQRkQraVp2WmRta0FLTllkWDVZMUs3MFd6aXZleEF4S24xelNaZ2d0L2R1cGc3c3MvY3ZpakM2MjNhelhPRWEzYjVITkdTcisycTdCUHR0TG5oRVdXZFZ2T1ZUdnhoOFNFVWE4NXVBVzR5VG5iOGExekR6TVhXRVlINTBpd20vNzdMY0IyN1dVb1NsUkdKbUYyNVpETkFXdWxWcm92cHZiUC82TjJxbmVhdFJiL1kiLCJtYWMiOiJjNDE0YmZlZjE3NGJlMzcxYjRhYjJlNWFiNTBkZjkyMDk4MGU3MTgzOWZlMjc2OGE5YWNhMTRlNmZkM2ExNmUwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280177304\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-941701270 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941701270\", {\"maxDepth\":0})</script>\n"}}