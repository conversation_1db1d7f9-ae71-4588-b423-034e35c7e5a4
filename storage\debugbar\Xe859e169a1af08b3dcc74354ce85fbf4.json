{"__meta": {"id": "Xe859e169a1af08b3dcc74354ce85fbf4", "datetime": "2025-07-31 12:15:35", "utime": **********.063608, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964133.250314, "end": **********.063657, "duration": 1.8133430480957031, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1753964133.250314, "relative_start": 0, "end": **********.797409, "relative_end": **********.797409, "duration": 1.5470950603485107, "duration_str": "1.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.797446, "relative_start": 1.5471320152282715, "end": **********.063662, "relative_end": 5.0067901611328125e-06, "duration": 0.2662160396575928, "duration_str": "266ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936416, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1532\" onclick=\"\">app/Http/Controllers/FinanceController.php:1532-1599</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02572, "accumulated_duration_str": "25.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.945563, "duration": 0.02144, "duration_str": "21.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 83.359}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.007093, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 83.359, "width_percent": 5.443}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1548}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.022536, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1548", "source": "app/Http/Controllers/FinanceController.php:1548", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1548", "ajax": false, "filename": "FinanceController.php", "line": "1548"}, "connection": "radhe_same", "start_percent": 88.802, "width_percent": 5.482}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1572}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0329108, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1572", "source": "app/Http/Controllers/FinanceController.php:1572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1572", "ajax": false, "filename": "FinanceController.php", "line": "1572"}, "connection": "radhe_same", "start_percent": 94.285, "width_percent": 5.715}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-866832515 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-866832515\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-59863769 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59863769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-898533017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-898533017\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1310649635 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imp6KzdObml5WUM0NVc5eVN5amcvYkE9PSIsInZhbHVlIjoiY3ltc0Q5V2JFUG1RNFBxVERkSDFrU0ZJQ0RKT25KMy9lWVJFZTRseWRWRTMwSC9wY0V6UFp5TlU2ZkdyK05QVTI0cnN2QmRRcDZWcFpiKzh0aGtYMWt0QjNMQVZxaGFXV3dJTHNzVDBPa1hXbHhhYzM5RTBpUTRPZlNlZU1DcFpndG5YZzJQQy84RWo0MXNZU1poRGljb2cvWDgvSS9jN1UzQzlkdmFhVlljdjBJL0E1RWlaTGk5b0tmaW1TTUxOaWRqSnhYK1FnZXZqNUJPaDZkVCthVklPYUk2bE4yU1M1MnNLNVpyb0ppMmk0L1BqWXpNMlJHaStPcU5pZHczU0hQRENEQWxFbFB2TTd4VVovSURNaFJXamFoMGNmSFkvZ1RHYUY3azBiWE5iOHJoanZXN3NEemQzelNiWEtPbXc1T2xjYVRVS0s2L2ZKWjFvNm4xdXY2U2VJQ2xkMkxzT1I4cG9wbDE3M1I4M01RU1MvRmJ0U1J0VUNocDVlZE5rL0ZxWkZta3E1cEUrb1JFaEJKRjczc2lreEhCdFhOcno3VFFCM3M0MTgrUkdNaFRqSExmZXg1YStiSkVNSXJ4N2dDQUF1OXgzbWxweCtRRHROaGtrY21KS1p2RWljb2l1WDhtM0R2c0FodWJ6MkF3YUNVc1JTR2xYUlVSOUVJWk8iLCJtYWMiOiIyM2JmMzFlOTQ1MTlkOThjZjYwNWI5YWZmZWI4NzM1NWFhYTcxNDhjNzRjMTZlYWEyMDU4M2Q5ZmU2ZWU5ZjRkIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlZUL2pUOVRWUTNaSkZjL3NZSlhvSnc9PSIsInZhbHVlIjoiMU1HaXVCYWgyVHNRdE5LbUdRQnA1QnlxOE5VVUxpTzY1VUtlbjNObWN1YkxDREU3U0tldXliZ1d3Q1VNNnZ2TFdmZHhMR1Vwc0NIOGpSN2V6NGI5NFFtTHZNMVhmOEY4b1VVaVVERUUzeFZGWm9ZQmVDY2FZaFN0WTRhWmtjYVh4cUMya2pkTEhhc0JFcXNkaG54Y1BmMlBEdGxzOWFhU1VPZWtNTzJRL0N3eUVKUDlBSEtZMjVuMGRwNDRTVnFkYkNMY2wyVVpiREZBNnN1dzVRT0NGZ3EyUVplUGdoRU5RV3EraysxenB5TkFxNHdPN2RKSWxjdyt4aDM4cEFMZE9hVGlnSHZiKy83YjRyclpGZkpqZ1ZPcGExRkRyNjFtSythUzJyaUdDSFlGdFRRQmV3a3JNSGtyZXhMNTNnOVZXNWZ2UFhmU0ZjUFBDVURjMHV3MnhqOHhaKy9nYXZMUTJxMlV2c2dPZ2l6OTdiNGFZVGFyWVZoWXhqVzlMb29wSzFma21JTnp5cHd2dnh4bEZSWmwzTy85QUpRUkFrdm52Y2QvRThyM0lwWUV1Qlp1MnB5WDlqM1lHWE1hS2xBTHpMbEpKdmgxckl6NUxEQm5jUGFEeVJrR1lXL0dhVmJpWHR4WnRHOHBTNU8zTUJpK25OelJoVXN3di9GVG12N1MiLCJtYWMiOiJiOGQ5NDJlZTA0YTM1YWZmODc5MWU2MDEyNzk4ZmJjNDA4YzIxYWRmNjEzYTg5MjZjNmJkMDQxMWRlZThiZTRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310649635\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-573397997 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573397997\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-102322198 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:15:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVoVEkySVVOc0xoVktmVjNHTENBOEE9PSIsInZhbHVlIjoidlBuYkFSZS85MTBXR1dBQ21JTGI2SFc0cXZzcWNkWW5rd3F4T3g1M1NQd2xwbk9iVkRLTWlqb2o2N0o4aWtTZEM3MWhzK2JPNHVISUp1NlZ5QUVGQmJVZDQ5MHBlSnZMeVRpNGNqSWFscndsZi9DbjRlQWQ2N2sxMjdUSU9Cb0RkNi8wZTFaYk5wRUF5ZG9iUzArdXJpeWZDSEZHOFlQY2RqWEpGcm0xaHg3SjNQdVBVaVlJRW9BR29wdnB3QTJSc2h5c2hZbm1zcnhiS1poa2ZhYTQ3MmJ4bWdUUm5VeVNJOWFUZ2tvZGp5dHZuR0JicHEyZm1CY1Ftbld3QzB3d29LZ0RIK2YxMHhNdWZrdnl0OGlVTmpOWXhkTjZPVVhIeElSWERLaE8wN1h4MDc1OFpkVGxXWEYvekxEWkc2eVpITkhhRWZrdWJtTUVsL3BaMlhuaUFiT2Q0VmMvaDZSa1NKT01BZDFFK05EZVhybmQ0RVI3dmQxbHdiV29TZmJFOU9MaFgzbWNNZ24wdUE0YUROdE9qZWtEZitacTRNOU9MVStqcDVLRWFpQkg0MUthS3pLMFZxUEN3Q3V1OVpGWDdFM0w1eG9BNFp4bnkwZDZscDdNZ2tEZ3l3Y2xjeUovUjVxQlVtdkI5bjN5eDZEMkdnVUJ1VEVnVXNBY25NNmgiLCJtYWMiOiIyYTU4NTFiMWVjODNhMjkxNDBiM2ZkMmE1MTE2NDA3ZDMxMDgyZjQ2ZGQ4NTIwYTljN2EyMTY2ZjI0M2QwOWNmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:15:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Inc2b1h4aHJWZlloYlZKNVZkNGRsU2c9PSIsInZhbHVlIjoiV0VtRVJ0OTV3djd5R0YwYmM5SU5oZUMyV3FoYWV4MllCaTlnV1BUQThNdlprdWNvM01XRXFLOUhRbHpLYlRMa1NQM0R5QWhGY1V2UnJxU0VZNStVVWhhaW4wQnhmNDE5b2NncXB1Z1NWbEVBRFhOVlhFY0g2aDRaRDBVU1VyaWthVVhlb3M5bHJ0R0cvSk5YWkJlQ041SG1ucTNRWlUyQkI0dUpoS0FqU1JUTEp3dHBubDdtYkw5QXYwaFlWTUhBMUNJVktTYW92TDExWE92MTdQUk8rdHlkSlZtK0hNMFZ6Nzd0L2N4ZW1yalFkb1dBa1ZybWJNN3VwNHJ3RmptR1FyNUZqVGtZOTZLNWVyd1BBeTdSQ0NrQmFjTVdyeEs2ellHV3pmdXdHWXpCRmFJUzExZ3VkZ1p5L0dDM1dNQ2k3R0JoWW1QbWVhYlVwejc4b0lpaVZTZ0tUZVJNMDhEWXR0ai85Q2FjclcvemlETkZ0Lzh5cWxZTzJSWEtWVXArUWdDWkhzZmtIeVkvU29UZGRaWXZhYmJaa2Z3UnVKcXQrZU1na2lvT3YvNkJ1WStLRk1rZ01uZTd3QkRDcG84RlRERkhRK3YvQXBqZ3NVWjhvK2xxMGNWV2JvSFFDYURzQVpDZlFjUEg1MU9uVjdQUjlQRTExU0xlWWR5U0JjNVkiLCJtYWMiOiI0MmZlNGZkZTA1ZTlhOGVhMGQ0ZmFjZWQxNjM0MDM2MjlhYTE3YThhZmRkZDIyZTYyOTQ3ZjM2MGUyM2RlZjhiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:15:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVoVEkySVVOc0xoVktmVjNHTENBOEE9PSIsInZhbHVlIjoidlBuYkFSZS85MTBXR1dBQ21JTGI2SFc0cXZzcWNkWW5rd3F4T3g1M1NQd2xwbk9iVkRLTWlqb2o2N0o4aWtTZEM3MWhzK2JPNHVISUp1NlZ5QUVGQmJVZDQ5MHBlSnZMeVRpNGNqSWFscndsZi9DbjRlQWQ2N2sxMjdUSU9Cb0RkNi8wZTFaYk5wRUF5ZG9iUzArdXJpeWZDSEZHOFlQY2RqWEpGcm0xaHg3SjNQdVBVaVlJRW9BR29wdnB3QTJSc2h5c2hZbm1zcnhiS1poa2ZhYTQ3MmJ4bWdUUm5VeVNJOWFUZ2tvZGp5dHZuR0JicHEyZm1CY1Ftbld3QzB3d29LZ0RIK2YxMHhNdWZrdnl0OGlVTmpOWXhkTjZPVVhIeElSWERLaE8wN1h4MDc1OFpkVGxXWEYvekxEWkc2eVpITkhhRWZrdWJtTUVsL3BaMlhuaUFiT2Q0VmMvaDZSa1NKT01BZDFFK05EZVhybmQ0RVI3dmQxbHdiV29TZmJFOU9MaFgzbWNNZ24wdUE0YUROdE9qZWtEZitacTRNOU9MVStqcDVLRWFpQkg0MUthS3pLMFZxUEN3Q3V1OVpGWDdFM0w1eG9BNFp4bnkwZDZscDdNZ2tEZ3l3Y2xjeUovUjVxQlVtdkI5bjN5eDZEMkdnVUJ1VEVnVXNBY25NNmgiLCJtYWMiOiIyYTU4NTFiMWVjODNhMjkxNDBiM2ZkMmE1MTE2NDA3ZDMxMDgyZjQ2ZGQ4NTIwYTljN2EyMTY2ZjI0M2QwOWNmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:15:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Inc2b1h4aHJWZlloYlZKNVZkNGRsU2c9PSIsInZhbHVlIjoiV0VtRVJ0OTV3djd5R0YwYmM5SU5oZUMyV3FoYWV4MllCaTlnV1BUQThNdlprdWNvM01XRXFLOUhRbHpLYlRMa1NQM0R5QWhGY1V2UnJxU0VZNStVVWhhaW4wQnhmNDE5b2NncXB1Z1NWbEVBRFhOVlhFY0g2aDRaRDBVU1VyaWthVVhlb3M5bHJ0R0cvSk5YWkJlQ041SG1ucTNRWlUyQkI0dUpoS0FqU1JUTEp3dHBubDdtYkw5QXYwaFlWTUhBMUNJVktTYW92TDExWE92MTdQUk8rdHlkSlZtK0hNMFZ6Nzd0L2N4ZW1yalFkb1dBa1ZybWJNN3VwNHJ3RmptR1FyNUZqVGtZOTZLNWVyd1BBeTdSQ0NrQmFjTVdyeEs2ellHV3pmdXdHWXpCRmFJUzExZ3VkZ1p5L0dDM1dNQ2k3R0JoWW1QbWVhYlVwejc4b0lpaVZTZ0tUZVJNMDhEWXR0ai85Q2FjclcvemlETkZ0Lzh5cWxZTzJSWEtWVXArUWdDWkhzZmtIeVkvU29UZGRaWXZhYmJaa2Z3UnVKcXQrZU1na2lvT3YvNkJ1WStLRk1rZ01uZTd3QkRDcG84RlRERkhRK3YvQXBqZ3NVWjhvK2xxMGNWV2JvSFFDYURzQVpDZlFjUEg1MU9uVjdQUjlQRTExU0xlWWR5U0JjNVkiLCJtYWMiOiI0MmZlNGZkZTA1ZTlhOGVhMGQ0ZmFjZWQxNjM0MDM2MjlhYTE3YThhZmRkZDIyZTYyOTQ3ZjM2MGUyM2RlZjhiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:15:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-102322198\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1839207112 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839207112\", {\"maxDepth\":0})</script>\n"}}