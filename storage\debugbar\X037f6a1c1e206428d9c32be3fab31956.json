{"__meta": {"id": "X037f6a1c1e206428d9c32be3fab31956", "datetime": "2025-07-31 12:14:36", "utime": **********.799284, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964073.607796, "end": **********.799363, "duration": 3.1915669441223145, "duration_str": "3.19s", "measures": [{"label": "Booting", "start": 1753964073.607796, "relative_start": 0, "end": **********.442878, "relative_end": **********.442878, "duration": 2.8350820541381836, "duration_str": "2.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.442931, "relative_start": 2.***************, "end": **********.799372, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3060\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1892 to 1898\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1892\" onclick=\"\">routes/web.php:1892-1898</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.03052, "accumulated_duration_str": "30.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6339831, "duration": 0.03052, "duration_str": "30.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-129615598 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-129615598\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-886029479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-886029479\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-119257373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-119257373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-303347552 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IklaNndKcEU1SnJjcVFpMkVRY3FJREE9PSIsInZhbHVlIjoidVJ6MDRDNFRhc1N0Vi93UHptRFdCbXNoeHlSOW5PWWdtSlFBWlVZTnVqNDlJUTE3bGtBT1R1eU51NHRseGUxczNBV0c4WkI3Rkx1d01GMHNCZFA5WHhOSjZjNzkxTktVVVNRbzFDQmt2M1BMUTlBa1QvZWdrSkpPbVZvY29IV1orTTRHSFRkUnFWRmJ0cE54S2V4OGtQWFRGZUo5ZXFyTFpoTzV0RVVhc0ozNDNWTjBsNFdGekR6bWJtaVprSVpicGk3bUtqcmlxK0NqaDVFR29CWlJDMEsvdjM2VTFiT1RiN1pjNmUvWnpXM1lhQ0ozQXhiM2dkYTZ1ZnlpWHk3SjFIMko3aWdST1d3Y3FrTFFuRGdHZW1veThlRjhVM0l5WVlWajBUS0ZDZkRVbFBEWGllQ2V4TnVGMEZJUWt6U3RPSnVaajk3RVpDY1Bhbmt3STkxZ0thOXJpbXowTHB5emRqNjdYbk9hRGUrTEtJWkRKN2pxTEM0R3k3cEFEZkxoYW9XSkMxNnZlOW50ZDkzQlJmRTFBRGYwU0dwdTJESVU0TFJjTW55a0lueENXdHVsTGZ0TCtZM3J6WHY1dkQwNVFsNzlPZnNYdnNKLzlSc21zWmRNRkxreWJLbGNYNk1xOE1xN1BseEl1NDJMYUZFVkNmaFhkVncvVnVRaWJQeTMiLCJtYWMiOiI1YmM5YTJjNWQ4NzA5MTAzZDU4Nzg2ODY2OTQwNjVjY2VmYmE5YzM1NzEyOTFhMjU0YTE2YTZjOWRmNmM0MmFmIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlhaMnlrMURER0RLSUtBb0dHMWgzOFE9PSIsInZhbHVlIjoibnhXUXl4dTNldVpxN3h0R2Z2NnlDUGRoZUFFazF6U3Q3cnN6dmNaSmhVTjh5aEFWU1JjQWNESGJoelRFMXU4ZTRDb280dDFBdHRVdXo2cDRsVjNkYzgxcTIwTUsxbGtwYzVlRm9idDF6ZDlCMDFWbEVGV1RRVDJMSVRNdmNLTE1pT2plTGFxaEQ4T3ZRNXM5WnZjaGVFTTllMHA5cGlNZUdRVmk3Yjk5M0xmWmkwK2ZuWGZHaTlHemQ3a0hvaDNsVVdaWmFyQlo2TEtYdS9ndENpY3dMYjM0dkd1NDc1ODl1bElKZzg3WElFWG9YMHhJTVNqQkFoM2FTalVUSy9qR1RUMy9HUmhIUHJXd3Z5ZGpVK2s0VFQ3K1ZWazhHSXBHMFB1WkJRaDhxZ1VHM3NEM1ZuVk1ZeW1MdDNxU0tsNmtUdHFEOHkyUEpmNkxCTVBMUnJsaFVOTWxKQTg0cXFYYlRFZ1ZpK0xIZWQ2T1dBcXY4S1JybFFRQzdLT3drTjAwZ1czc0Q4elhhSnBMR3lacWVXd1U0a1RDZjV5VmxkZnQ4WlZqd3FXVHRpaG9SQzN5WlRQeWc2RTZ3NTJrcmdSSXNJZ1dLRWFWYlVPRHBTTmJJVVYyMFlSZTdxd3dTSDA1Vkc0YmRRUjJwS0FmMHFWeU00V1NSczlhNDY4TnFQYU0iLCJtYWMiOiI4ZjlmZjI1YTI2MzAxMTE5YTNlNjEwYjQ3MDk1NWVjZjAzN2Q0NTU1N2ViMWI2OTY5YTgwYWZmM2FjYTFiNDljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303347552\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1352224844 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352224844\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-39678848 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:14:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJUZS95S09JT2d4SnhidVJTemQxZnc9PSIsInZhbHVlIjoib3NERVNpdU0vV0IvVVdKSWtlaWVrUUorcGdZdUhsN2hNbU5BUlNmMUV2bEdMRXBnOVFtKzNvOE9IcmhyUjAxZU1BY0JaSkw0OFRLK29aZk1nRUdab1JyVnVIb2g2RmptY2JjZEFrZjNnQ0dtaCtuRDNDdkFxeWFpRjJoUXdablRTT0M5OVFzUFJaTlVOU1VYam5hNVd4a1NzL3lmNk9hWHB6QXhTWjJJNkFBRjJUNEF4dWtaYnJqeFlyVXVpYVdBeDJUUitYL0ZJNWJ4Smp2bEozNTZIZ0pKWnZQc3UvdExhRlRzVU5ZNjBUN1lOWlBIQ0FkQVNMYmxFVGVJNUo4dVJPZ1liTlcyUER2cTVJUXRNZVJhUTErSU1nMXMxK25NNWxPbmowc2NGWnpHbkUyeENubUYrdUVKK0NGL0lGd3BQaWFDdnVFbHQxbUI2QmRLZ2lxNFVCK2V0UVYzeFNuc2JveVVIK1p5ZDV1MHFwc1NEZ09NVDFlZkhOL3FBSWpUampiekNxV2hlYWhRY09RWUd3VVZBMlJ2b3I2VnYvWUwwQ2dORzVMeGtXOThVWFBFVWNVQWRVQkJXMzRPVTZuY1R2WHUzU09CR0FoTHlCNkNJSjUvN1FmWjlpNXNJWGJ1VmRIdFRHSGFaSWlFdlF3UERXdy81Z1pESzUwZmRTRHEiLCJtYWMiOiIwMGUyYjE4ZmNkYzIyNTdjZWZhNWE4MWIzODQ4ODU2MTM3MzI4NTdmYWZhMzE3YjU3Y2FmZGY3NDY3ODBmN2VjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:14:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImdlZi8zcEMwT3VBNitCQXhaNVUwaGc9PSIsInZhbHVlIjoiV1JwZ0RyWGZhRlJJUXhYcDhtOFVEdlc2THlhekVWd204YkVvRTFQeTJMa0Q1Q2JoQTQwU3Mxck5HR1UvUjVROHFoTlNodW56QkM3RmJURlhzeXFIeWFCWmdRMmVsK0ZyOE1hNEtvemxWUmhoRjl5M2pCWVEwdC9RcUo1ejNjeU9BQlRxdXJTRDdGUGVzNExscFQxVFNjR3ZBWElMN3lpNFlHb3NuUDBhY2p1NFA3dDlXOFhhVFJFcVNjUnF0ZDJ5UGsyYjlHbFUxSVpKdzdUSU1xZTVsMS9hZnUzSnd2cDhuYlNnMmhZeCt5aFNCc0ZTZERCdzg0dWZpV0JtbVFCckVaYVhpTE9JVUJmVitzS29NblJtV3dHd0dlcFdRbHhFUzVXWTZYOW0wVG9EeXBQZWRUelJGeC81RDNWLzBncUloSm1EQU1na0NDN1BGclluaGxGN0FKRTlXSUZzcTV1WTJOOE04ekc0MURkTkdYcXBaOWJ0QUFQMkpNY1ZERVF5dDNrdGxUb0U3WVZZb0o3RDRYUHptQTlJUDc5VzJuMWcvR1NCMThIZUQ0bHBBUS8vVHMwQXVFa2ZUd3BxdFovckdOa1NuaTBUdWdzOCtrdDFGRVBCUG9FUE5seUJiK1Mxa2FPYmZsTld3MGxjL0FjdDc0cXlBUFRyWHdpRERIdWUiLCJtYWMiOiIxMTgzNGRkNTBhNDA0ZGJjMDMyOTQ0OTg2YjEwYjdmZDQ1YTViMWFlNGJkNDljMzE2ZWI5N2NjMTQ4YTY1NDk3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:14:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJUZS95S09JT2d4SnhidVJTemQxZnc9PSIsInZhbHVlIjoib3NERVNpdU0vV0IvVVdKSWtlaWVrUUorcGdZdUhsN2hNbU5BUlNmMUV2bEdMRXBnOVFtKzNvOE9IcmhyUjAxZU1BY0JaSkw0OFRLK29aZk1nRUdab1JyVnVIb2g2RmptY2JjZEFrZjNnQ0dtaCtuRDNDdkFxeWFpRjJoUXdablRTT0M5OVFzUFJaTlVOU1VYam5hNVd4a1NzL3lmNk9hWHB6QXhTWjJJNkFBRjJUNEF4dWtaYnJqeFlyVXVpYVdBeDJUUitYL0ZJNWJ4Smp2bEozNTZIZ0pKWnZQc3UvdExhRlRzVU5ZNjBUN1lOWlBIQ0FkQVNMYmxFVGVJNUo4dVJPZ1liTlcyUER2cTVJUXRNZVJhUTErSU1nMXMxK25NNWxPbmowc2NGWnpHbkUyeENubUYrdUVKK0NGL0lGd3BQaWFDdnVFbHQxbUI2QmRLZ2lxNFVCK2V0UVYzeFNuc2JveVVIK1p5ZDV1MHFwc1NEZ09NVDFlZkhOL3FBSWpUampiekNxV2hlYWhRY09RWUd3VVZBMlJ2b3I2VnYvWUwwQ2dORzVMeGtXOThVWFBFVWNVQWRVQkJXMzRPVTZuY1R2WHUzU09CR0FoTHlCNkNJSjUvN1FmWjlpNXNJWGJ1VmRIdFRHSGFaSWlFdlF3UERXdy81Z1pESzUwZmRTRHEiLCJtYWMiOiIwMGUyYjE4ZmNkYzIyNTdjZWZhNWE4MWIzODQ4ODU2MTM3MzI4NTdmYWZhMzE3YjU3Y2FmZGY3NDY3ODBmN2VjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:14:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImdlZi8zcEMwT3VBNitCQXhaNVUwaGc9PSIsInZhbHVlIjoiV1JwZ0RyWGZhRlJJUXhYcDhtOFVEdlc2THlhekVWd204YkVvRTFQeTJMa0Q1Q2JoQTQwU3Mxck5HR1UvUjVROHFoTlNodW56QkM3RmJURlhzeXFIeWFCWmdRMmVsK0ZyOE1hNEtvemxWUmhoRjl5M2pCWVEwdC9RcUo1ejNjeU9BQlRxdXJTRDdGUGVzNExscFQxVFNjR3ZBWElMN3lpNFlHb3NuUDBhY2p1NFA3dDlXOFhhVFJFcVNjUnF0ZDJ5UGsyYjlHbFUxSVpKdzdUSU1xZTVsMS9hZnUzSnd2cDhuYlNnMmhZeCt5aFNCc0ZTZERCdzg0dWZpV0JtbVFCckVaYVhpTE9JVUJmVitzS29NblJtV3dHd0dlcFdRbHhFUzVXWTZYOW0wVG9EeXBQZWRUelJGeC81RDNWLzBncUloSm1EQU1na0NDN1BGclluaGxGN0FKRTlXSUZzcTV1WTJOOE04ekc0MURkTkdYcXBaOWJ0QUFQMkpNY1ZERVF5dDNrdGxUb0U3WVZZb0o3RDRYUHptQTlJUDc5VzJuMWcvR1NCMThIZUQ0bHBBUS8vVHMwQXVFa2ZUd3BxdFovckdOa1NuaTBUdWdzOCtrdDFGRVBCUG9FUE5seUJiK1Mxa2FPYmZsTld3MGxjL0FjdDc0cXlBUFRyWHdpRERIdWUiLCJtYWMiOiIxMTgzNGRkNTBhNDA0ZGJjMDMyOTQ0OTg2YjEwYjdmZDQ1YTViMWFlNGJkNDljMzE2ZWI5N2NjMTQ4YTY1NDk3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:14:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39678848\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1918190519 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918190519\", {\"maxDepth\":0})</script>\n"}}