{"__meta": {"id": "X43cfbb146b1094a09973f2d8db745bf3", "datetime": "2025-07-31 11:15:46", "utime": **********.193268, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960545.41243, "end": **********.19329, "duration": 0.7808599472045898, "duration_str": "781ms", "measures": [{"label": "Booting", "start": 1753960545.41243, "relative_start": 0, "end": **********.07447, "relative_end": **********.07447, "duration": 0.6620399951934814, "duration_str": "662ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.074485, "relative_start": 0.6620550155639648, "end": **********.193292, "relative_end": 1.9073486328125e-06, "duration": 0.11880683898925781, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47340312, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028999999999999998, "accumulated_duration_str": "29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.129808, "duration": 0.02729, "duration_str": "27.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 94.103}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.168973, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 94.103, "width_percent": 3.241}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.1741118, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 97.345, "width_percent": 2.655}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense-categories/list-ajax\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1308301302 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1308301302\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1852844828 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1852844828\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-330334871 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-330334871\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1376323427 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNvNjllUlNjZk9kMnFNS2NzU2Y2SlE9PSIsInZhbHVlIjoicmd4MnduVWN4R1JBM0JMTThwanBqdkhmZkRjK0NtWFZjb2FSc2JjNTcxQlJRRHhQZWlsL0RzWlRwYkhIVWN2QTNJTXJ1SllZRkRpNHQwUGxwam1UNjFkTWtjaUg5QXE5Z3hKNHJXZHA2eXdzMXBZdW1uMkpyOW1qZ0NBcFpVbWIwN0V5SUpxV043Z0RlOWxUcHRTbmpDUGxZQnd5c3d2bFZ5YmE2YkFSQkUwTVpselU5bWl3UWV5U3crdUpyOFZMZFE4REFSUmYwSVBVTDU5RVh6dEtvN0dzVzZ6VG9GcHNlTzI1R2NNclI3ZjQzclY3eWpGQTI4N1J4Y3RwSWRCSGdyU2hpbk4zdkNDUU9TVkZmeDQ5ZVp1cnlmZ3NMZ21DVDZZQ0RyZGd5YXBrbWpsbTFLUnp3VVNSUVFFaXJZaktQR2p0WXRLOUxZbzJsd0xUZE93c1ZHdHFGaGxqR3F5T3hpdE9ZTER0SjFhcUN4aEpDVnNIeDNvb3B1SjdKbDhUN2xOVWwrMDFOZzNpWHVzcUJySDhjNHU1cjNDMTA1c05NS3o2Tk05OW5pRWkrYnRXN05GTGdwWGNnT1MvYlNDaWk5enZVUGpRSlJyRG82aUU2NnMzMFlySWlUUFBUZkRqTUR1RzdmZlBIekVtcXVYd3hGcXVIQmZSa1pzQzZVQy8iLCJtYWMiOiI4NjdhM2IzOGRmMmQ4NzU4MGNjZmFiMTdkZmQzZjJiZTAyZTYwYWUwMDM1YjQxNmExZmYwOGU1YjNhNGE1YTMyIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InVMY1gwTHRTWG5JMzVyYVFqR2VhN3c9PSIsInZhbHVlIjoiQVRnQjg1am5odVliR2t0WVRPT2pWQmk2eEhoUjVFa0NHUUhCZzJ5cms4dThGeUwrQ0xqVWZ6RHFMc3ZaZndTOXNaK3hwWm9abmVwQlFSTXpzeVJld3FlV3BHTGU1K2M0bXNydUU3NXI1TXZuNEJ6bFpOVTU5bUpkbE00TUIwQkFXVHQ4TlFjeUVYQVA1NVBBVldRRTFrYno4bGhQUXh4RW5ZM1JVQjBTdzh4bDFVcVJ2YlJ5cWlPMzlJa2ZzZnZLSVY1TTlheEJOSklCbVNjaW83MzVHRDBGcXpUcFhGV3AzeFdwMW50WElSR3VrOThONTFzcFRTQlU5N1JMZnhaU2FDa1ROczd1SkxVd0IzckpKYmpUQmZ5NnhiamxMak1Ba29nc1ZNdTZGcHgwZ0dNdE5uVUgxbHZmQ2Rrc0M1b2o2TDhuV1BqRXA4cWRLTTROWmlvOVA4dFN2OXlLSDJmdklEUkd1N1NYekNxTzNnM2V6RE1yRkYxMG9PTE9XbDRTVTNRWDlydWRiZ1FZQ2RYV2NHaDVQMkhpOHpsWDV4ODNEcWlHczZ2YUN6bXdFRC80Z2tsNFI1bDJXQUJjYXNKYUdWR0FNb2dwN2dtY0NQTkZIdnZSVjdORVppeVNsL3dMTHM4anJTWitqSkxsNXpkRUNTc3JpdmdLTXBuK01EWi8iLCJtYWMiOiJjMTg1ZWIzNzhlMGM2ZWQ3YWI3OTg0ZTEwYWMzNWJlMGM3MTBkOWYwNGQ1Zjk3YTNlY2RhOGQ5OGUzNTk2MTY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376323427\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-924533718 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924533718\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1895724483 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:15:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFuM0pGUVo0MmJlYTRpNGZHQzFURHc9PSIsInZhbHVlIjoidjFUR2t4dzFUZUVqZDQxdXArVXk1dVNGSjAyb1pLYmRuZlVTd1QyRHNrRnJsYWhIWmh0MFJiMW1MWklldEloK3lSakdKMmVDQnNNQXhTVHkxU0hUeWd5a2FSTEFsWVNRS29EQ2hWUEhQcFJEcjIzbFAwVFo4Y3lyY0Jzck1zSzE0UUNBZ1R1VWg3VkN6Q1dSK1kwcHBSczIxZVExYmhnUmxqNmdsa0UydDVrcXh3ay9UK3daMmg1OEh0SVF6TDlmbWs4VTBaTTUzZ3M4bzVxNlJvazJ6blV2OUZ2ZUNmYzBlNVV5clNYR0xDQm9DeUtOc2dKeVJZOS8vd3pOY0Q4Q1htdVQrQ1QxcXoxUEtUWlRsbERkd0lFWDNLdE0veGgvdVh5QXBMOWExd0w4NFlhcTBQTWlFcDB5Q05xUnRubFYvZ25iRThzbnZPei83SzlnR1hCM0IyLytVVkE3cDFwUFhKaFVyc2ZrRnVPTzJ4Y1BXTHZDb0FpT2NPQy9SOFovNithYkc0RjV4SEVrUTFXZTQwNkdnT0tIVWgrayszRXFrNEQwN1RITmxpMGVpL0RWaFhDTWNENFh4MUV0ZGZDU1hwb0ZLU0RNUXdYY2o0QnpGTVlucHNYU3VqYTVvOU1LZjVpYURTVHJ3Vk1Ra285Uk45MklBOG5wNWxPR3dkRE0iLCJtYWMiOiI5ZTJkZWUzMDRhNmQyZjY1ODQ2OTgxYjdmZGNjYzVjZTc0OTVmM2U0NGZlYmExNzE5NmFkYTUyM2ZjZTc0MmY5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Imx0bEJ4TktVNStlRVFRYkNUM2VOR0E9PSIsInZhbHVlIjoiUSthaHJpc1FtOGJ4dWlTSHNNdXZOTmtXN2JpM0VsZUhyMisrWHFlaEFGdEZaaERRbnprZWRqNDZZWFNGWUlMVzZWSzROcXZ0QjRpNzF4NkEvM1p5WFRIcGUwN283d3FJSWIxTUw3R0dNREFPWTQzK3RURVlKQ3RiTEppMlMyVE05b0NaeU1zcUhnb29RRy9uOXlSSFlLS0RpQlNpdFFYb21JaVJ2REdZeGN4cHdhMzhJbjhTZndjVUdtZ0tXa0QrSGxHcWdkM0JwUmNpM2ROeVQ1S2NoNXI2M2JhR3E3TThlcTA3U0IrdmpWODZQWmo1NjFPQVptTTNFNXN1QzRqdzMxVEUvRkJHeTZXd2dMbVd4NXJRSHR1QnVWK1JIcFZaUHppYlJBNjNZd2ZDbUxwMzFnSlVMelBEdjU0MGZraGF3L3VIdFhHd2FXRHJpWThVNWs5S1phWW8xdE0rYjduSGZnMXcwVjZrQ0FzdTZlS2dTbVBjNVdlRmluR1dpeXFxYkN5SjZzMlpBRFQ2NVovNk80ajV1NTZ3WG51dDZtL1VrRWpiWWhSQ2dvOCt3L0pTQWs1Wmk5WlgxR25aYm1DdVJCS3Q1U3NUcXh3Y1hnZXpwdDVEdS8rUkoyVy9YNnppT0NHbjVoOFBNcms4UjBPZjBHQ1ZFRzdMaVZuY1RVKzAiLCJtYWMiOiI0ZDNlYTNjZDg4ZDczZWFjMjBkZjE2M2VmOWQ2NTllZjBkM2YxMzU1M2ViMjU4NzcyNThjYTBkNWYzMzM5ZWI0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFuM0pGUVo0MmJlYTRpNGZHQzFURHc9PSIsInZhbHVlIjoidjFUR2t4dzFUZUVqZDQxdXArVXk1dVNGSjAyb1pLYmRuZlVTd1QyRHNrRnJsYWhIWmh0MFJiMW1MWklldEloK3lSakdKMmVDQnNNQXhTVHkxU0hUeWd5a2FSTEFsWVNRS29EQ2hWUEhQcFJEcjIzbFAwVFo4Y3lyY0Jzck1zSzE0UUNBZ1R1VWg3VkN6Q1dSK1kwcHBSczIxZVExYmhnUmxqNmdsa0UydDVrcXh3ay9UK3daMmg1OEh0SVF6TDlmbWs4VTBaTTUzZ3M4bzVxNlJvazJ6blV2OUZ2ZUNmYzBlNVV5clNYR0xDQm9DeUtOc2dKeVJZOS8vd3pOY0Q4Q1htdVQrQ1QxcXoxUEtUWlRsbERkd0lFWDNLdE0veGgvdVh5QXBMOWExd0w4NFlhcTBQTWlFcDB5Q05xUnRubFYvZ25iRThzbnZPei83SzlnR1hCM0IyLytVVkE3cDFwUFhKaFVyc2ZrRnVPTzJ4Y1BXTHZDb0FpT2NPQy9SOFovNithYkc0RjV4SEVrUTFXZTQwNkdnT0tIVWgrayszRXFrNEQwN1RITmxpMGVpL0RWaFhDTWNENFh4MUV0ZGZDU1hwb0ZLU0RNUXdYY2o0QnpGTVlucHNYU3VqYTVvOU1LZjVpYURTVHJ3Vk1Ra285Uk45MklBOG5wNWxPR3dkRE0iLCJtYWMiOiI5ZTJkZWUzMDRhNmQyZjY1ODQ2OTgxYjdmZGNjYzVjZTc0OTVmM2U0NGZlYmExNzE5NmFkYTUyM2ZjZTc0MmY5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Imx0bEJ4TktVNStlRVFRYkNUM2VOR0E9PSIsInZhbHVlIjoiUSthaHJpc1FtOGJ4dWlTSHNNdXZOTmtXN2JpM0VsZUhyMisrWHFlaEFGdEZaaERRbnprZWRqNDZZWFNGWUlMVzZWSzROcXZ0QjRpNzF4NkEvM1p5WFRIcGUwN283d3FJSWIxTUw3R0dNREFPWTQzK3RURVlKQ3RiTEppMlMyVE05b0NaeU1zcUhnb29RRy9uOXlSSFlLS0RpQlNpdFFYb21JaVJ2REdZeGN4cHdhMzhJbjhTZndjVUdtZ0tXa0QrSGxHcWdkM0JwUmNpM2ROeVQ1S2NoNXI2M2JhR3E3TThlcTA3U0IrdmpWODZQWmo1NjFPQVptTTNFNXN1QzRqdzMxVEUvRkJHeTZXd2dMbVd4NXJRSHR1QnVWK1JIcFZaUHppYlJBNjNZd2ZDbUxwMzFnSlVMelBEdjU0MGZraGF3L3VIdFhHd2FXRHJpWThVNWs5S1phWW8xdE0rYjduSGZnMXcwVjZrQ0FzdTZlS2dTbVBjNVdlRmluR1dpeXFxYkN5SjZzMlpBRFQ2NVovNk80ajV1NTZ3WG51dDZtL1VrRWpiWWhSQ2dvOCt3L0pTQWs1Wmk5WlgxR25aYm1DdVJCS3Q1U3NUcXh3Y1hnZXpwdDVEdS8rUkoyVy9YNnppT0NHbjVoOFBNcms4UjBPZjBHQ1ZFRzdMaVZuY1RVKzAiLCJtYWMiOiI0ZDNlYTNjZDg4ZDczZWFjMjBkZjE2M2VmOWQ2NTllZjBkM2YxMzU1M2ViMjU4NzcyNThjYTBkNWYzMzM5ZWI0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895724483\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1741444379 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/expense-categories/list-ajax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741444379\", {\"maxDepth\":0})</script>\n"}}