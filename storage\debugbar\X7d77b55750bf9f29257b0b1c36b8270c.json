{"__meta": {"id": "X7d77b55750bf9f29257b0b1c36b8270c", "datetime": "2025-07-31 12:10:08", "utime": **********.300794, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963804.482699, "end": **********.300842, "duration": 3.818143129348755, "duration_str": "3.82s", "measures": [{"label": "Booting", "start": 1753963804.482699, "relative_start": 0, "end": 1753963807.87271, "relative_end": 1753963807.87271, "duration": 3.3900110721588135, "duration_str": "3.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753963807.872757, "relative_start": 3.3900580406188965, "end": **********.300847, "relative_end": 5.0067901611328125e-06, "duration": 0.42809009552001953, "duration_str": "428ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936432, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1532\" onclick=\"\">app/Http/Controllers/FinanceController.php:1532-1599</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01975, "accumulated_duration_str": "19.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1633801, "duration": 0.01331, "duration_str": "13.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 67.392}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.226454, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 67.392, "width_percent": 5.418}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1548}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2417002, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1548", "source": "app/Http/Controllers/FinanceController.php:1548", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1548", "ajax": false, "filename": "FinanceController.php", "line": "1548"}, "connection": "radhe_same", "start_percent": 72.81, "width_percent": 9.57}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1572}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.256766, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1572", "source": "app/Http/Controllers/FinanceController.php:1572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1572", "ajax": false, "filename": "FinanceController.php", "line": "1572"}, "connection": "radhe_same", "start_percent": 82.38, "width_percent": 17.62}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/expense-categories/list-ajax\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1293420629 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1293420629\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-306032235 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306032235\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-779368682 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-779368682\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1163961430 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVMVlhlYWNGSm1wZlNKVmU4RGY0b2c9PSIsInZhbHVlIjoiTEpydVQ1RWdKRlVlTDFVMGs0NENVYUU0NDkzTko0SVBvYVZvaE45VmZmRGNGZXNoZzBiT2JweWI3dVkwTE4veHNTYmQ3YWRpcVU3RUd0TGYwM0NLSW9MMWZEb0tXbGlVVDFneG1XRTVzMXBFYmtEeFJaT1BBWDlNL05UbEZUNjlqSjNKa0x2VVBSVWJUOGZZeVpKMUExWFhNeHE4SDRWS2xTSnFTMVp3YlNxeGNZUHFjMzJQYWRxWEZUb3lMM2UvTFZCZTF3SjlPTGMyT0JHV1pNWTlxZWpVQzUzTzNhUFVaVi84L3lQVlVvN25Ja0oxL3Riai9OMnR4SFRJSy9JbEg0OGlqcFNTbWM3VVdiZXR2bTZGQXZxenNWS25tKzFhMytEYzlqbkVWRzVaRDN6cXBEMnBXdkZ6YXlzNjdtSkRXTSsyNHdybUcvUDFRVDAzQmVaV0FadXRZT1BoWjFvbVNacEc2K0lrMnRaRVp3b2lETEYyTEVZcUdqdkRzZlFTQ2YrOXhZZXNiWGczTk1QMTBpdG02M0pWTTY5dU5nd0ozYUZTeE9vRWZ4eHllcUFTQUtITHM1aGR6MkN2Qyt5SHZ2Zm04ZTdZM0xpNWhhSktmMDBaQmNJdnJ2OWdMZ2p0TzFEYUdNS3NQTWxyY2FCdmRudjZVdW5OcDEyRlQ4T20iLCJtYWMiOiJmOTQ0MDZhYjQxMDIyYTM0ODE0ZjMzYjE3ZmM4ODE3YTkyZTMwZTUwNjliNmJlNjdhMmJjODYxYTExN2JkZWQ3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImJ4dGlWQ3pBSC9NZVpwK2toRTErY1E9PSIsInZhbHVlIjoibmpFUkFxUHNsYTh1VUwvaXZUR1hLVnlwbW9lRndIbVVTbXlCZkszaGkyKzhXR1o0U0RGNUJFZ0ZmZzBwd0R0SEN1ZlBQL3JuR0lPTGRzRytnbWJyeHh4dGsyQ0dZSDAvaG9HMEgwSGg2KzBzc3lpVzFxMk5vdFdkWUpxdnBmcnZIbFlFRE5VMU5FdGR2VkpqUmRWT1BNcUN3K2lKem9uQUhWaDF2MFpMdXRtRUdNZnNSQlV4VTduZ1NjSVJSbkw0VUh0MmdBL2tIZVpPdkhQRjU0S1NsYVVlSk1PeTFiUlU1UE54T0g2c2plWENad3ROeVc4VzN5U3hBbGVqL1V0NlF6UUJIQytwWEx6SE4zNmRockRaenNNOURaQjdZNWRIVTV6V05jZHV4NXVzRXVPQ0lMZFAycG5Od091ak1POU9NczVLOXR2ZDdzcm04NDg3SGVlTEZuVTUwUGVaa2RWZ0VGbWtreTBPYXFkb0kwaitkQXB2SGlPc0tEWXZFNzhwVWRHZnozYXltdktEREhoN2NRTzBVamg2Y1U4R0t5K3lnTlhQK0ZKSUlaU204RGU1eDdRWlk1Nmg3Z2pFaUFRQ2dGY3p5QW0xUnFmZG1NcTh3b0ZjMldMRUQ4RUljUStiUkxnV3FMMnVQQVp2SnhNdjVmdEgzUW0xZ1NzNzBaRTAiLCJtYWMiOiI1ZTdjYWJkYWNlOGJhM2I3NTc4YjlkYzY1MDk3YTRiZjI4NmI3NmNjMTE1MTlmNGIxODU4MGRlZTg5N2NhZWZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163961430\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1758656243 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758656243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1418330606 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:10:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ino4T1NVd2tlc2w1d3lTaE5zaXF0cEE9PSIsInZhbHVlIjoiUGxFWkVLUjk0ZzdMN2FOd0JJOFRwYngxd2NYMjV0dUVRS25USDZLc0hWelduME52cnFJc1ZmZkdscUc4bURWZ2RweFJLSnZPZjhseTNFc2hPbks3dVgwaWlJMTAzUTJoZWdBTHJEZGJBVWgwK1FRYjQ2SHZ5UjUraUIxdGpHd2tCMVFWb05GeStpUDlVMFdCZi96eGR2djRQSnZQOUk4TWh4anFMaERqczZvclFaa2RRNDl1MHlhdEQyOGJNU0liK0dEQUh1K2N3M2ljQUxHOHllMis1OUppaUIwWTBrSW1mVUlMVmZ0Y2dYZW9qRUx0ay9FUnU4M1lEVkYwdWpYaE9yRHpRajJIMzFpM0tjY3VKbzhOaTY4dXpDaWV2THV0bFp5eTR0V1Z4ZEI4QTRwdEVFelJXUElicTFWSjBlYUN6OVgvQjJuSXQ1cnNNUVlGWUc2enZhU1duRkpCSGQ3N3k3ZFVhdUpZV253SmVJZFVjWkVNZ0FLOFlyUisrT0dZT2MvdzJKTXNlMFg1YnF1SkU2b3l1andLRUpHRlVubFVEdGRpRE1QM3ZOS0JaLzNQeThxT2VJVzZxOFliN090WmE5dTRzN3NhWk5vM01QTDdQQjI4NXU5b0lWY3BxNnprWG9GOHlJcFV0bXJEL1BBUlVXbTBZbXRyMU9BRkgwTlgiLCJtYWMiOiJlNDE0MGE5ZWI3M2NmNjNjYzU3NDRkYTNmODNjMWQyNDlhOTU2NGYyMzBlNDViNTFjNDk1MjBiOWY5NjFkNmQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:10:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlBMNFNpU0FXamRkcU8xcFNmeFp6T1E9PSIsInZhbHVlIjoibUZSdVRlQXB6MFh5RUw3Z0VrZkNWY21GS0p5SUR4VlZOUGQ2bmg0NXJEOElHcFFYcDg5Q0Jya3VWMk1QaVB0NmxGcElINy9YZ3U5UHQ0aCt2NmtjSlN3OGR0bStLNkJGMHVtcWZYb05OZzNkRzhSQ2cxVEVtN0RrL3ZBYUluUHV2dnNYdEpnTUlPUkwzVXE4dGZEMElDT05IT0dJa0RPYWtidGdEQW9BZll4UzFBTkY5UGp1QlNkVk9IajNVRThQeGF5QUtHTmFFZzAydHMxSE9vcmpwUElnSmF3TmtyU2IvQjU5cFpDR0FqQTZhdkE0anBWcEpydmtnVm50ODJFWXlzdVJNWnNhbXloamkyVUN1RW5HWHR3NVUzZ1MzSjBKTnRVTE5LZ3BldmIyYmpxVU1JSll6KzM1R1VEU0N1L0hRdmdhWTlaNjl1cDNGVUVGTERwQTBxNmgzUVAzb2pkT2xFcnlnN29ncXZFbXExcmI3bGl4QnFRNHd5eTNiNVl6ejNrbWhiQ1MzL1BDQXBTVElFTUs0K0Q4ajNoSE5mQ1FZQVhqQ1pYOVV0T3NseGtXREFGZDBLNWh6VVhYSFVSQ3AveUo4Qi9DWU84YUorQ3RUZjJ6Y2NpaDc3Z1ZHMlAvZ2hLWTZiOHlHZGcwclJwMm9DVlA0RDZ5cnM4UXRIbWciLCJtYWMiOiI3NDkyNWZmOGQzNTZhYWU3MDMxYmVlMTk5Nzc4MTM0YmVlNmMxNjI0MGNiMmVkNzc3NzI4NjhlZjEzMTdmMTVlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:10:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ino4T1NVd2tlc2w1d3lTaE5zaXF0cEE9PSIsInZhbHVlIjoiUGxFWkVLUjk0ZzdMN2FOd0JJOFRwYngxd2NYMjV0dUVRS25USDZLc0hWelduME52cnFJc1ZmZkdscUc4bURWZ2RweFJLSnZPZjhseTNFc2hPbks3dVgwaWlJMTAzUTJoZWdBTHJEZGJBVWgwK1FRYjQ2SHZ5UjUraUIxdGpHd2tCMVFWb05GeStpUDlVMFdCZi96eGR2djRQSnZQOUk4TWh4anFMaERqczZvclFaa2RRNDl1MHlhdEQyOGJNU0liK0dEQUh1K2N3M2ljQUxHOHllMis1OUppaUIwWTBrSW1mVUlMVmZ0Y2dYZW9qRUx0ay9FUnU4M1lEVkYwdWpYaE9yRHpRajJIMzFpM0tjY3VKbzhOaTY4dXpDaWV2THV0bFp5eTR0V1Z4ZEI4QTRwdEVFelJXUElicTFWSjBlYUN6OVgvQjJuSXQ1cnNNUVlGWUc2enZhU1duRkpCSGQ3N3k3ZFVhdUpZV253SmVJZFVjWkVNZ0FLOFlyUisrT0dZT2MvdzJKTXNlMFg1YnF1SkU2b3l1andLRUpHRlVubFVEdGRpRE1QM3ZOS0JaLzNQeThxT2VJVzZxOFliN090WmE5dTRzN3NhWk5vM01QTDdQQjI4NXU5b0lWY3BxNnprWG9GOHlJcFV0bXJEL1BBUlVXbTBZbXRyMU9BRkgwTlgiLCJtYWMiOiJlNDE0MGE5ZWI3M2NmNjNjYzU3NDRkYTNmODNjMWQyNDlhOTU2NGYyMzBlNDViNTFjNDk1MjBiOWY5NjFkNmQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:10:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlBMNFNpU0FXamRkcU8xcFNmeFp6T1E9PSIsInZhbHVlIjoibUZSdVRlQXB6MFh5RUw3Z0VrZkNWY21GS0p5SUR4VlZOUGQ2bmg0NXJEOElHcFFYcDg5Q0Jya3VWMk1QaVB0NmxGcElINy9YZ3U5UHQ0aCt2NmtjSlN3OGR0bStLNkJGMHVtcWZYb05OZzNkRzhSQ2cxVEVtN0RrL3ZBYUluUHV2dnNYdEpnTUlPUkwzVXE4dGZEMElDT05IT0dJa0RPYWtidGdEQW9BZll4UzFBTkY5UGp1QlNkVk9IajNVRThQeGF5QUtHTmFFZzAydHMxSE9vcmpwUElnSmF3TmtyU2IvQjU5cFpDR0FqQTZhdkE0anBWcEpydmtnVm50ODJFWXlzdVJNWnNhbXloamkyVUN1RW5HWHR3NVUzZ1MzSjBKTnRVTE5LZ3BldmIyYmpxVU1JSll6KzM1R1VEU0N1L0hRdmdhWTlaNjl1cDNGVUVGTERwQTBxNmgzUVAzb2pkT2xFcnlnN29ncXZFbXExcmI3bGl4QnFRNHd5eTNiNVl6ejNrbWhiQ1MzL1BDQXBTVElFTUs0K0Q4ajNoSE5mQ1FZQVhqQ1pYOVV0T3NseGtXREFGZDBLNWh6VVhYSFVSQ3AveUo4Qi9DWU84YUorQ3RUZjJ6Y2NpaDc3Z1ZHMlAvZ2hLWTZiOHlHZGcwclJwMm9DVlA0RDZ5cnM4UXRIbWciLCJtYWMiOiI3NDkyNWZmOGQzNTZhYWU3MDMxYmVlMTk5Nzc4MTM0YmVlNmMxNjI0MGNiMmVkNzc3NzI4NjhlZjEzMTdmMTVlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:10:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418330606\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-974449794 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://127.0.0.1:8000/expense-categories/list-ajax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974449794\", {\"maxDepth\":0})</script>\n"}}