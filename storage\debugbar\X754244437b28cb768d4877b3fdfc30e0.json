{"__meta": {"id": "X754244437b28cb768d4877b3fdfc30e0", "datetime": "2025-07-31 12:03:32", "utime": **********.374787, "method": "GET", "uri": "/storage/products/1753963399_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963409.561217, "end": **********.374837, "duration": 2.81361985206604, "duration_str": "2.81s", "measures": [{"label": "Booting", "start": 1753963409.561217, "relative_start": 0, "end": 1753963411.879094, "relative_end": 1753963411.879094, "duration": 2.3178768157958984, "duration_str": "2.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753963411.879133, "relative_start": 2.***************, "end": **********.374843, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "496ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3060\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1892 to 1898\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1892\" onclick=\"\">routes/web.php:1892-1898</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02194, "accumulated_duration_str": "21.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.081319, "duration": 0.02194, "duration_str": "21.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/products/1753963399_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-1295444185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1295444185\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1915618417 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1915618417\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IldWZTlXWUlScDRvYzlGcXJHSllDYmc9PSIsInZhbHVlIjoicHpaVzJSN1NVWHA5cDljV0pBK1JvOXhoT1lFdHNDTmYxaDhZdUlVWng3NStKMzIzUTZTS0VzYW00ekNoRkFpUEw4Y3NBRTM0REdVTmsvdjdXQ1lwSDd4eFg5TGZNd0xwN0NZbXZDT2JONEtqSjFCbXBpN054RVh1ZXZIZ3NxcEU3djVhNGU2SnJDbjZBMmdzdEVlLzRablZqMk51K0xjT2p0U3BmanplQ0lacXd6K2ZLclJmUTBlZUdBaVpWcFJ3cEhWbytJVlZzbXZRYXg3VEpOVUJQdTNnYTRtdXBnUm8xbElQZm14a21qNVE1WWRPbzdMMkVTSjA1YkkxZ3hnMmpGTzg0eVY4b0ZDSVJNa1IzRkU5RkR4K0RIRUNVMDloQ2ROV213Q05rYUZFVklSa1NyK0JXVjR0TjhNQ2E5WElyR2hmdlRFNDNneCtVYS95MFMzczNzaldvOUFEQkZUNE5UWmRudXd0TzJXTmlMWno0c1gvMjRkeGswcmRNMXhyeTVaV3FxY3ZXNlVPaGhrR1NpM0FZN1lLQUtYYlJSTml6U0pnM0U3d1NrMmxoNFJTRWc5SGFlNnovMGpqK0VJVUV0MDB3UFpvSTgva2lIK2NQSExiWVJSc0QwSlFtMGZWL0NzaU9QZnZtZ0l1ZmpPVWdtWHpYeExKSWlpZW1qUkwiLCJtYWMiOiJhNWMwYjA2NTIzZjZlYjM5ZmUzN2FhOWE3MjgyYzc3NTRjYTI0ZDBmOWUzMjJmMDI3MTcwZGE2YTA5OTZkOGJjIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkJ2dnRiazJVYUdvTEZjTGQ5QVl6NHc9PSIsInZhbHVlIjoiR3Z0UGlCeE5memkxWWhXajRYWUtpVy8wemU5ODFMTUxidkNHaVh4OXlSaUVUa2pwcEZlZ1ZKUmpaYm1yV3dxWWtDT3NiWU16OUJIOG1obGRodWVoQ1JvWmhJL3ZkNFd2bjl1UHF1M1dDdnAyTmk1MUFyZU12dnBUbDBTYUFPcXl4Y0E0TXcvVGNCd1ByVjRrKytvMkJNRUhBbHU4SURJK3RHS29kMEFGUWVoMWYrT2tldHhqWGkwbHAxYklrMU1yWHFsOS9oN3hyZjlQK3pGSEcxT3M1cXJndCtYd0lRY2JmQm1GQWxVNjl1cHc1ODFQc1NFazV4SlBaa1lxaUhsT0hNdDN6V096VG1NSE0wL3JFS2FaUlpiaCt4djdYZkJVbVA2eDJFZ1NBN0pKQmNQek1Ldnc4THExdTFBbysvZUdqY1kzdUROZ2dCSm9NYVVTYUtPWUxDeTZZbG9OOGttN2FodzRwUG16b2dlaTE1VnJDYmZRUWFDZVpjRE1XQzFvSzlQKzNDVG9HSGpLTjhBaHJMa3M1c2NHSTdURGVhY3hmQ3A1OFJBWTFYbkdhbklleDYzbWpEQ3I5b08xQ3hleG9BTmt1ckVtbEJRSUlqZW1NLytJNDNoOHphT2hkOU9ubVg3MG16aXA4ajBpTlJYbE9iYnJMRFhyUVA3T3BZZUwiLCJtYWMiOiI0M2UxMGM1ZDg5MmVkZmNlMjEyMTBkYWM0ZDIyNmNmMTA4YzNiMDFjMzBhZDVhNTllMjkxMWFjY2NkOTRkNjZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRobGF3MjFpYVcwOGxXK1VQNEZpMUE9PSIsInZhbHVlIjoiZFd4ZENtS0Y4dlB4NVJBanFvODhXY1B1SUt0L1dYRFc4bUdsZHNoSWVCcXpNRXhXWWZTaXppMUUvZTBseUFGQlZDRTZEMnh1VWRUNlFnNFk3ZURnVm9jTVR4d0tEY1hubHZxdXJXdjFBcG0yTWtydU9yTUNyK0E4L01nUCsrVkhaSWxjZnYycXZoWXJzQWJuK0ZtZldTSlRFYklTd0xjdS9XR3hacnJIVFdqVDB0Z0JHaUgvQzBOekt6VlRZN0FvaTZxSVB2cERwSWpHMGpvcXJtZzNJMVZ6NlhsK3BYN3ZpMW1sUHhoR0srbFFxdUdrcVpDdWo4bVFIeTBZMWJmVGhkZWVqbkVnZTNXNnhyNlhYd1A5aWlMcGdwRWI5c1l4ZExPR0pGRkgvWmtWc2RMT2dKWHBRV21jMndSME9hOUdqMTdaV3JQa3JTTlZoUzVPa2NqdElCS3B1Wjh0MkhnVWNjRTFNSmVaanNGeUVzRVowRGkvK2RkN1FrT1hydEdWWW9RY0FUb0hPbVcycy9ndWNjWHh3Z1V1cjVQMU1NT3VLZS9HcjdlV1FMdGRIS1B6a25YL0o5TkIwalZ0V2xuWkJUNy9RMDdGR3o5MTF6Wkkrb3VzV1Jnb2pWSk5GVXp5MzVvUzVRS3J4dXllYWhGeHoxcjk0b0VOTGZ0UVNPamoiLCJtYWMiOiIwMmEyOTg1Yjc3MWQ3N2ZmYjkyM2IwYTBlNDgxMGE2NWQ5MTdmNDVlYThlMWRjMjIzZjJiMmI0ZTFhZjE3ZDc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:03:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImdPN3RhRTdNQzdUZUk2UnVYWU1LZ2c9PSIsInZhbHVlIjoidnRpdlgwYkM5TzRvTExNTC9hNDRaeWlUbGNQa1FoNlhUN09rQjRObE53ZDN5ejYwVjY3NVZlUjVzTU1OVzgwOUFwcUlrS0x3QjQ1MFNzQ3p0Tmthemp4YWZHTlNNVjF1ZVcrU3BnS2pLNG9kZVBtMEVmWG5Nd0lhRlRHNnNGMGRXQmFZeWpIQy9BcWJtQ0Z1cjFVOFNhK09YaEVNdm0yVklsaXM2U2djcGhSZHVpWXlIYzdIK0xwZEhqc1VpM2NSa1NINzRhSnpidmMzMFl3eUdMM0xwQ3BOc0JWRHFoOU1kS0tUd052bTFhQVV0L1ZYL1VCMFI2c0FWL2pFck1rNTNIem9sL25oR0VlemJtSzdXNDg2eitBZy82ajZsVlRPbWhDTnZGb1JhbWt0MDFITUZvbGU2aVBJYkVUemNkeWRwUy9qMHZ3d0QyT1dMWUxLT3RqR2x0Umo3RU5oN2tFQllRNjl3Z1NLWFEzZUxxSjg5WHdITS9YYmtJNXRubFVCeGhkVHN2ZjBnTWZETTZWeFJhQm9ER0NvN0o1N3U3cXBHditVYU9vdyt4VmJoNXZCQXE1YUxVZTNUMGloRGhqSy80ZnJ6Q09HRVYzRXpwaTBFYURzWmtyODlKTW16eDdselJKNEVnV2VuMERsL3RybFUwaG11SnFxZVM1ZVhSd1IiLCJtYWMiOiI0ZGE3ZDkwYWJkNWQ5YzE0ZjY1OTg1NmZkYjJmZGNhNmM5MWExODM2ODQ3OWQ5Njc5MTdlYTY3NGVhZjg4MDJkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:03:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRobGF3MjFpYVcwOGxXK1VQNEZpMUE9PSIsInZhbHVlIjoiZFd4ZENtS0Y4dlB4NVJBanFvODhXY1B1SUt0L1dYRFc4bUdsZHNoSWVCcXpNRXhXWWZTaXppMUUvZTBseUFGQlZDRTZEMnh1VWRUNlFnNFk3ZURnVm9jTVR4d0tEY1hubHZxdXJXdjFBcG0yTWtydU9yTUNyK0E4L01nUCsrVkhaSWxjZnYycXZoWXJzQWJuK0ZtZldTSlRFYklTd0xjdS9XR3hacnJIVFdqVDB0Z0JHaUgvQzBOekt6VlRZN0FvaTZxSVB2cERwSWpHMGpvcXJtZzNJMVZ6NlhsK3BYN3ZpMW1sUHhoR0srbFFxdUdrcVpDdWo4bVFIeTBZMWJmVGhkZWVqbkVnZTNXNnhyNlhYd1A5aWlMcGdwRWI5c1l4ZExPR0pGRkgvWmtWc2RMT2dKWHBRV21jMndSME9hOUdqMTdaV3JQa3JTTlZoUzVPa2NqdElCS3B1Wjh0MkhnVWNjRTFNSmVaanNGeUVzRVowRGkvK2RkN1FrT1hydEdWWW9RY0FUb0hPbVcycy9ndWNjWHh3Z1V1cjVQMU1NT3VLZS9HcjdlV1FMdGRIS1B6a25YL0o5TkIwalZ0V2xuWkJUNy9RMDdGR3o5MTF6Wkkrb3VzV1Jnb2pWSk5GVXp5MzVvUzVRS3J4dXllYWhGeHoxcjk0b0VOTGZ0UVNPamoiLCJtYWMiOiIwMmEyOTg1Yjc3MWQ3N2ZmYjkyM2IwYTBlNDgxMGE2NWQ5MTdmNDVlYThlMWRjMjIzZjJiMmI0ZTFhZjE3ZDc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:03:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImdPN3RhRTdNQzdUZUk2UnVYWU1LZ2c9PSIsInZhbHVlIjoidnRpdlgwYkM5TzRvTExNTC9hNDRaeWlUbGNQa1FoNlhUN09rQjRObE53ZDN5ejYwVjY3NVZlUjVzTU1OVzgwOUFwcUlrS0x3QjQ1MFNzQ3p0Tmthemp4YWZHTlNNVjF1ZVcrU3BnS2pLNG9kZVBtMEVmWG5Nd0lhRlRHNnNGMGRXQmFZeWpIQy9BcWJtQ0Z1cjFVOFNhK09YaEVNdm0yVklsaXM2U2djcGhSZHVpWXlIYzdIK0xwZEhqc1VpM2NSa1NINzRhSnpidmMzMFl3eUdMM0xwQ3BOc0JWRHFoOU1kS0tUd052bTFhQVV0L1ZYL1VCMFI2c0FWL2pFck1rNTNIem9sL25oR0VlemJtSzdXNDg2eitBZy82ajZsVlRPbWhDTnZGb1JhbWt0MDFITUZvbGU2aVBJYkVUemNkeWRwUy9qMHZ3d0QyT1dMWUxLT3RqR2x0Umo3RU5oN2tFQllRNjl3Z1NLWFEzZUxxSjg5WHdITS9YYmtJNXRubFVCeGhkVHN2ZjBnTWZETTZWeFJhQm9ER0NvN0o1N3U3cXBHditVYU9vdyt4VmJoNXZCQXE1YUxVZTNUMGloRGhqSy80ZnJ6Q09HRVYzRXpwaTBFYURzWmtyODlKTW16eDdselJKNEVnV2VuMERsL3RybFUwaG11SnFxZVM1ZVhSd1IiLCJtYWMiOiI0ZGE3ZDkwYWJkNWQ5YzE0ZjY1OTg1NmZkYjJmZGNhNmM5MWExODM2ODQ3OWQ5Njc5MTdlYTY3NGVhZjg4MDJkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:03:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-944524465 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944524465\", {\"maxDepth\":0})</script>\n"}}