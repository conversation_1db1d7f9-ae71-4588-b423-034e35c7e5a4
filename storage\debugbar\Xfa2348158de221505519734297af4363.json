{"__meta": {"id": "Xfa2348158de221505519734297af4363", "datetime": "2025-07-31 12:00:51", "utime": 1753963251.145842, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963248.395577, "end": 1753963251.145892, "duration": 2.750314950942993, "duration_str": "2.75s", "measures": [{"label": "Booting", "start": 1753963248.395577, "relative_start": 0, "end": **********.594182, "relative_end": **********.594182, "duration": 2.1986050605773926, "duration_str": "2.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.594218, "relative_start": 2.198641061782837, "end": 1753963251.145897, "relative_end": 5.0067901611328125e-06, "duration": 0.5516788959503174, "duration_str": "552ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47346440, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.009309999999999999, "accumulated_duration_str": "9.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.802004, "duration": 0.00526, "duration_str": "5.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 56.498}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.837453, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 56.498, "width_percent": 11.493}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.905665, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 67.991, "width_percent": 32.009}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-422026154 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-422026154\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-511517769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-511517769\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-54301511 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-54301511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1630931516 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IngyQlJkQVp5K1lHUlQ5YkcvTWQzelE9PSIsInZhbHVlIjoiTVQ5WWMveUhKamNDaDlSSTVrWWhhbDdEc1J3NnlRRjNCT241ZXNsb0xWYUVicVpoWnFFdVE1ZTd2cWRHSXI2Q2VMMlVMOVd1VWpXcUcrZElXMnRNUHFhcjRzeURPVU5ORzVlWlpjZlE2dno0d3dZS0hhajJ3NWE0bHo5cjRRSC8vbFRuaFcvRHI1VWNoaUJmeHJ0M0JCaEZUOXBMMkVrSUhRRUVSeVhqbFFraWdsTDlZQTc3WlI0NTNKUjczNFRUUXc4eTE3QVdNVkh4bVFlK2pvVUVZeVlkVHdNYStEa2YwdzF4eUVUM2E2eFdhQVkzNnovYWR5S2NVaWlJOGsyYUFkN29MY2xQdlUrYkYyb21zbGx6MWc1UlpSRHNjK3NJSFFwYkpZbkE0aUtRKzNveTJ0M2MwUHFuUmE4am42ais3OWdMcUd4T0tPdlczMHlsL1F0citqSVlxYWhudFBhU252L2dmc0JWaS9qcG9JdmhER3dRWWRpOXJzZnBJYlBKQkZXZ1U5SE9xNzUxYTFLSi9hTXVvRnR5ZlV2bDFxZnFZdGYvZlk0SEV4bVF5THZlSDZjUTRtakxSRExwZVhmUTEyV0lFeGlBTno5UHBKemYvVUhFeDU1N2J6c0FWMTlMV2xNUzlTeUhkM3JqMVNNaU9DN202Y2VWdllETVVxSEYiLCJtYWMiOiIzNGE5ODgzZTMzOTE5NzQ1NTc0M2Q5OTE5NmI1ZGIxMGFhOGEyZDBjNjkxMzdiMDgzMmVlYWVjNDI3MWNhMzA4IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlZ1MVIvdVlMWlBJdG9LQndlSDBhMmc9PSIsInZhbHVlIjoiZkUxbGtLZEI2WWxTQTg3VnMvZXluelhjak56RVc2SU5ZNUQ3Rnd2M2hZb0ZmZWxRRnJUaDdGaWtjQzJqSDVPRGNqVFR0WVZrUlkydjUxVjJQTmdTcU5scEJuTElFVFk1UDN3TldrT3BYZlVmalBMQURFN3p5MWtzMG96N2ZkNTE4dVlOOFJiMFVyR3EzZ3M0QTRCWXhObXV5NXZ6OERna1Nyd0tlSlRxTzk2VVVLSmdvM3N5NjExVDQ3eDJsaW4vNVFOM2txbW9PYVZYUGJQbnR3SWFuUGJ0RkJiWXpvSm1QTHUvMU4xTGwyNERYMFFyU0pHQlMxaWpUWGQzNGk0VFBIYVpmV04weFdwd2dsOGsyUzE1ZnlJcDFmdnJsZkV4U0dRTDZBOG0yUGR1WmJxUmdtY2pDZS9TQytZRElEekRROWk4eS9NY2xReXdVM1J3SVdHTFZOcktKQURxaFhkV1VrT3JjS3c2RXUwNldEcWNSY0Q0UGNiblVNaWpMdWoySC9ramhiRldhOFNMNTVKbXZ5RkpINHJzVUJYOEV3b2dzSTFYZnhwVXFnV0E4aU5kaUs2NFBvR0FDTzY4WXRxblU5cGUwYjluclZqd3l0QmhXd0FIOEg3TjR5dUJncWM0MkY0bk9lWFVGTkR0ekZ0dno4cE1LbmtTQjcyR2RJR0IiLCJtYWMiOiIyOWJkYzQxNmQxMWE1ZDE4NjYwNGMyMTIyYTIzY2NkZDU1YzBmNDM1MzYzNzY1NzMxNjgzYmY4N2EzNTBlMmJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630931516\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-287879733 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287879733\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1025352176 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:00:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBiUUY3WkdUSy9mVEJmd09YS3VhNkE9PSIsInZhbHVlIjoiSXhKKyt2UktOQy85UG5KclNnaDlVSDNMMEZJRWI3U0FhRjBhbGJzUFc1eWRYcHV5RUVMTDMyaHpUeW80NGxGTytvM09WN2hGYVB1aG4zVUtOaG43eVBrWk1DeTB0cVpVdHl1ZGRqKytwaHFtbjhyb2pVeis2T0RSTmkrWDRNVHpLTWJxcUs5dW85aU8xWnNPbDh0bXJsczlXYVJNUUI2RHkwN3YvSjJ4NU1lNWhuQk5NOWdCYlFrb2J5RFZYWkxXSExWMGt1Q09KRytHRDN4K2VFUzVZdWk1Q3Z4bGNSaHVyYnhQRkVpYThmR0x6TUNnWVNaSy9Kei9BQ3RIQTRDWWpSZ3MwUkJPa2dYNmJ5YWtZWUhiTFRuQU5jdHVpVW5oampKdXVKVnovS0hnOXlMNnpJYUd0c3lnaURYdVc1QTdRVkQwRjJSREZJb2ptSXBvU2QyMndtbFFocDVOTnRlbmVnWkI4ZE5waFdVOXRsV2RjcDFxVk0xVERBMFl4aUVDRElieG14bXphQk1ueVdPa2dRM0pRWWtJMHJnQ0lVck9IUk9hY2J4QzYrNW9mNEpXZ3BSVVV6Rm9VNjRFVU9iMUlmUjNQSEs1dE95eXUvTjJtOW16bHhrS0RqNzhndm9NanpCTUE5dU8vS2k3eUFzV2UvSmtIanczdmtncDFvMjciLCJtYWMiOiJiNzQ5Zjk0NDc4MzNiMmEwNGQ1N2RmY2I0ZmU3YzRhOGM3MmE3ZjcyMDQyYTgxYWZiNmE3ZDJmODE3YWFhMWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:00:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlRGS2VPTklPMGFaVHMwN1ZYN3IveFE9PSIsInZhbHVlIjoiREk4T3p0NTBJMGFFVGZudnY0V0Jtblh2VExvbG9rajBRZC81eTROcjhjcFZGUFJKZi9VV093MGNrbG1sek5nQ0JGREJyOW1rQmNFUHdmSWRzbkw4SzRnMm5SQjlMQlN3cGpFcExvSFFUc1VCVXVQUjJsa1QreHdKOVFqa0orR3U4Sm9LMlV6U3BxN1pubHRiQlUwVnRFeVJZeG1WQTlOTE1abDZWeDZsclBEOENqQkV1UTFoRmF1VjVIU05CNUZTa3JTWEd0ZmFtTXU3MTdNVm9CQThPSC9xZGRsWFZtbDJSblluZnlpd1IyZFRrbms1VlJ4bUhrV1BFemtIdnFTNGtlM2dnQmdkNHkxZVV4YndvVEtOMmJEKzRSMDB3VnREVmxJZjhRdEg0UjRLeVBXSzl4bjVMbWRZbWg0ODVUNHNrbDBHa3RyL3JSall3N3BCTVhBN29HSzk4SUVqUnFYNjdXbmdTWUR2cWR1VEhoa1JROFY2VU1sYXF4T2gyUFUxU1pneDMrMnBjdUd3dXF6VlBKSURJck9PaEIrSzI0UUVMb3dUellRNlFZT2FpTnBmSXJiL2JSOEl1YThVRlg4aUJsNnZwSXp4RnZSMVIxM1dPVG1yOUJFZHdWc2FMMzdPTWl2RVBTUXQzZUhsU2w1QUdMK3JVU0hvVDVwNFBXOUgiLCJtYWMiOiIyMDUzOWY1OThhNDJkY2Y0NGEzZjYzNzFhMmQxZDkwMjFiNzBlMTRlYmQ2YjA2YjkwMWIyZTczNTA1NmU0YmRiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:00:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBiUUY3WkdUSy9mVEJmd09YS3VhNkE9PSIsInZhbHVlIjoiSXhKKyt2UktOQy85UG5KclNnaDlVSDNMMEZJRWI3U0FhRjBhbGJzUFc1eWRYcHV5RUVMTDMyaHpUeW80NGxGTytvM09WN2hGYVB1aG4zVUtOaG43eVBrWk1DeTB0cVpVdHl1ZGRqKytwaHFtbjhyb2pVeis2T0RSTmkrWDRNVHpLTWJxcUs5dW85aU8xWnNPbDh0bXJsczlXYVJNUUI2RHkwN3YvSjJ4NU1lNWhuQk5NOWdCYlFrb2J5RFZYWkxXSExWMGt1Q09KRytHRDN4K2VFUzVZdWk1Q3Z4bGNSaHVyYnhQRkVpYThmR0x6TUNnWVNaSy9Kei9BQ3RIQTRDWWpSZ3MwUkJPa2dYNmJ5YWtZWUhiTFRuQU5jdHVpVW5oampKdXVKVnovS0hnOXlMNnpJYUd0c3lnaURYdVc1QTdRVkQwRjJSREZJb2ptSXBvU2QyMndtbFFocDVOTnRlbmVnWkI4ZE5waFdVOXRsV2RjcDFxVk0xVERBMFl4aUVDRElieG14bXphQk1ueVdPa2dRM0pRWWtJMHJnQ0lVck9IUk9hY2J4QzYrNW9mNEpXZ3BSVVV6Rm9VNjRFVU9iMUlmUjNQSEs1dE95eXUvTjJtOW16bHhrS0RqNzhndm9NanpCTUE5dU8vS2k3eUFzV2UvSmtIanczdmtncDFvMjciLCJtYWMiOiJiNzQ5Zjk0NDc4MzNiMmEwNGQ1N2RmY2I0ZmU3YzRhOGM3MmE3ZjcyMDQyYTgxYWZiNmE3ZDJmODE3YWFhMWUzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:00:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlRGS2VPTklPMGFaVHMwN1ZYN3IveFE9PSIsInZhbHVlIjoiREk4T3p0NTBJMGFFVGZudnY0V0Jtblh2VExvbG9rajBRZC81eTROcjhjcFZGUFJKZi9VV093MGNrbG1sek5nQ0JGREJyOW1rQmNFUHdmSWRzbkw4SzRnMm5SQjlMQlN3cGpFcExvSFFUc1VCVXVQUjJsa1QreHdKOVFqa0orR3U4Sm9LMlV6U3BxN1pubHRiQlUwVnRFeVJZeG1WQTlOTE1abDZWeDZsclBEOENqQkV1UTFoRmF1VjVIU05CNUZTa3JTWEd0ZmFtTXU3MTdNVm9CQThPSC9xZGRsWFZtbDJSblluZnlpd1IyZFRrbms1VlJ4bUhrV1BFemtIdnFTNGtlM2dnQmdkNHkxZVV4YndvVEtOMmJEKzRSMDB3VnREVmxJZjhRdEg0UjRLeVBXSzl4bjVMbWRZbWg0ODVUNHNrbDBHa3RyL3JSall3N3BCTVhBN29HSzk4SUVqUnFYNjdXbmdTWUR2cWR1VEhoa1JROFY2VU1sYXF4T2gyUFUxU1pneDMrMnBjdUd3dXF6VlBKSURJck9PaEIrSzI0UUVMb3dUellRNlFZT2FpTnBmSXJiL2JSOEl1YThVRlg4aUJsNnZwSXp4RnZSMVIxM1dPVG1yOUJFZHdWc2FMMzdPTWl2RVBTUXQzZUhsU2w1QUdMK3JVU0hvVDVwNFBXOUgiLCJtYWMiOiIyMDUzOWY1OThhNDJkY2Y0NGEzZjYzNzFhMmQxZDkwMjFiNzBlMTRlYmQ2YjA2YjkwMWIyZTczNTA1NmU0YmRiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:00:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025352176\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1096571951 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096571951\", {\"maxDepth\":0})</script>\n"}}