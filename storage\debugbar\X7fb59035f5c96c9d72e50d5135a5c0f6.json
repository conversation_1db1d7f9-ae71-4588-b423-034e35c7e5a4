{"__meta": {"id": "X7fb59035f5c96c9d72e50d5135a5c0f6", "datetime": "2025-07-31 12:03:51", "utime": **********.45098, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963429.113479, "end": **********.45101, "duration": 2.337531089782715, "duration_str": "2.34s", "measures": [{"label": "Booting", "start": 1753963429.113479, "relative_start": 0, "end": **********.063336, "relative_end": **********.063336, "duration": 1.9498569965362549, "duration_str": "1.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.063371, "relative_start": 1.9498920440673828, "end": **********.451013, "relative_end": 3.0994415283203125e-06, "duration": 0.38764214515686035, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47341432, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01665, "accumulated_duration_str": "16.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.289631, "duration": 0.01284, "duration_str": "12.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 77.117}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.373503, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 77.117, "width_percent": 13.754}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3965611, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 90.871, "width_percent": 9.129}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1642134253 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1642134253\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2098049764 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2098049764\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-404881019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-404881019\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImRobGF3MjFpYVcwOGxXK1VQNEZpMUE9PSIsInZhbHVlIjoiZFd4ZENtS0Y4dlB4NVJBanFvODhXY1B1SUt0L1dYRFc4bUdsZHNoSWVCcXpNRXhXWWZTaXppMUUvZTBseUFGQlZDRTZEMnh1VWRUNlFnNFk3ZURnVm9jTVR4d0tEY1hubHZxdXJXdjFBcG0yTWtydU9yTUNyK0E4L01nUCsrVkhaSWxjZnYycXZoWXJzQWJuK0ZtZldTSlRFYklTd0xjdS9XR3hacnJIVFdqVDB0Z0JHaUgvQzBOekt6VlRZN0FvaTZxSVB2cERwSWpHMGpvcXJtZzNJMVZ6NlhsK3BYN3ZpMW1sUHhoR0srbFFxdUdrcVpDdWo4bVFIeTBZMWJmVGhkZWVqbkVnZTNXNnhyNlhYd1A5aWlMcGdwRWI5c1l4ZExPR0pGRkgvWmtWc2RMT2dKWHBRV21jMndSME9hOUdqMTdaV3JQa3JTTlZoUzVPa2NqdElCS3B1Wjh0MkhnVWNjRTFNSmVaanNGeUVzRVowRGkvK2RkN1FrT1hydEdWWW9RY0FUb0hPbVcycy9ndWNjWHh3Z1V1cjVQMU1NT3VLZS9HcjdlV1FMdGRIS1B6a25YL0o5TkIwalZ0V2xuWkJUNy9RMDdGR3o5MTF6Wkkrb3VzV1Jnb2pWSk5GVXp5MzVvUzVRS3J4dXllYWhGeHoxcjk0b0VOTGZ0UVNPamoiLCJtYWMiOiIwMmEyOTg1Yjc3MWQ3N2ZmYjkyM2IwYTBlNDgxMGE2NWQ5MTdmNDVlYThlMWRjMjIzZjJiMmI0ZTFhZjE3ZDc5IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImdPN3RhRTdNQzdUZUk2UnVYWU1LZ2c9PSIsInZhbHVlIjoidnRpdlgwYkM5TzRvTExNTC9hNDRaeWlUbGNQa1FoNlhUN09rQjRObE53ZDN5ejYwVjY3NVZlUjVzTU1OVzgwOUFwcUlrS0x3QjQ1MFNzQ3p0Tmthemp4YWZHTlNNVjF1ZVcrU3BnS2pLNG9kZVBtMEVmWG5Nd0lhRlRHNnNGMGRXQmFZeWpIQy9BcWJtQ0Z1cjFVOFNhK09YaEVNdm0yVklsaXM2U2djcGhSZHVpWXlIYzdIK0xwZEhqc1VpM2NSa1NINzRhSnpidmMzMFl3eUdMM0xwQ3BOc0JWRHFoOU1kS0tUd052bTFhQVV0L1ZYL1VCMFI2c0FWL2pFck1rNTNIem9sL25oR0VlemJtSzdXNDg2eitBZy82ajZsVlRPbWhDTnZGb1JhbWt0MDFITUZvbGU2aVBJYkVUemNkeWRwUy9qMHZ3d0QyT1dMWUxLT3RqR2x0Umo3RU5oN2tFQllRNjl3Z1NLWFEzZUxxSjg5WHdITS9YYmtJNXRubFVCeGhkVHN2ZjBnTWZETTZWeFJhQm9ER0NvN0o1N3U3cXBHditVYU9vdyt4VmJoNXZCQXE1YUxVZTNUMGloRGhqSy80ZnJ6Q09HRVYzRXpwaTBFYURzWmtyODlKTW16eDdselJKNEVnV2VuMERsL3RybFUwaG11SnFxZVM1ZVhSd1IiLCJtYWMiOiI0ZGE3ZDkwYWJkNWQ5YzE0ZjY1OTg1NmZkYjJmZGNhNmM5MWExODM2ODQ3OWQ5Njc5MTdlYTY3NGVhZjg4MDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1921914740 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:03:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxoR1VyNlMxMjY2SnJqdmxtanRyRmc9PSIsInZhbHVlIjoiOUk4eGNwTitBMzBWNEtubm5kQy9TOWswM2NhUlJWeXlpckpxdUNnaVE0Y0gzd1daNmNoRGtJSnFBRUY5WDk2ajVnZnhCYnlkTEZQVjNkdkRxV1REaWRweGJJZHFkaEluaWRYbGZpT2Nsa2ZTT2NRcVFld3JaaUJlZEZ2Tk11Y0xYNS9zK1ovNDVhSHRyUUhYNkNwTjVWNFpnNVhrQnlXOVhlUUg3UzBjZXZBdklmRVJBQS9nYk8rcmNjNzljeDBWRlJ3dkdYcTFOUzNjWDdXREl2VnNtOWEvc1hrSjBVUnhlOHUydEdZTGZ0QmpzdTZsMG55K2V0U2g0N2ZhOHdmckhlSWJGVGtqVXNpbVF1a2pyckRCdDJmRWIwWHlFWkZwUStwQS9ONU9pY0R0b056OVBtK1FoSWZCbXkrbGlTNG4wVllUd2VSM1poRkIxMEdSUG9wSjIvaVBlZkMyYUlhV1pSd2E3amx1a0JockEyWWxLTXVoYmkzNWpjOVVrYjJmTUU0UEVOZ1FsYUliYVM4OUJsbzhOaVdOcU9tQytBU09iRWZGbmR5U01JeDJ3RThIK3J2Z20vVkJJdFplSGZIQUNuc0gzYU93QnF2MEx3c1RIRFlTZlZ5bDdnVTNyNDNsM1pxT3JiQ2ZaSXF6anR1ZDF3U1BrSU5HYWFVU1pLODUiLCJtYWMiOiJlMTZmYzJiZDVmZWM3M2VkYWI0OWU2MWFkNTM2NjU5MGYwZjZjZTRjNmU4ZGU1OTI5MmU5YTU5Zjk4M2I2MjIzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:03:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjZKMDhicWZONzdrRVB0alhwcjJDa2c9PSIsInZhbHVlIjoiUlJqUGhOYlYwc2o2NVV3ajdSUXBZRFRZMk14N1hlaDBwR3AwWnZ5NUFLUllxMDZMbmR2Tit0aGI3RGJBUjNLK3hvUmY3WWZyZ2k4TFpGQmdFY2ErbnQzczQwZGpGcUk4QUF4Q3pTN3R5aDF1Z3FPZFlFdmZiSzBSWFhUK0k5cTgrRnhmeHozWmo1Z3ZFbm9xM0xsVXM1c0FYNy9PTGZWM092OWVSQXpUR0VJTXhBaVJPOEI2VCtYOVJlK2VWbXhTM0JVZnZYSEJ0N1g2Ujk2UjJ6bHZMU0ViRmJpemxCQzVWUmpPN3J0clBTWjZSWmQrbjE1MWEzWTFBTnlCb21CMHd0NzVzMzErYnhNYVRJYVMrYWxBOXlZLzVNY1NNTm0zcFR4SnIyQmdyK2xFNjlVSUkwNFZzZStvQ0w3QlA2dXI2UmFoMEpJcU5iSzQyT0JkYWhkRmNlM0YzT0JTeldxdCtGVlBGYTkzRzh1ZUJ2czhFM3lCbitQWWo1MzdWd2pKaHVwZ0tTTHdyOTdndGNtTDBreUhGOXhCUXhyN2RpWTNCaDYxRk5UMnNPdnNURmwrTXl3NmZCT1RzbVR5dGQyd0lodEgxTm1mOFpWcUJYbE4rdGFTMCtBNGhYMzcxRWJYNGlHWGk3S1NackpyazVpVG9vZ3N4TzEycDdZWC96UkwiLCJtYWMiOiJhMzRjY2FlMWE5N2YyODVkOGYyOWM0ZDI1NjU5YmUyY2Y2MDdkNTIwMjQ2ZjlkMjc5YmM0NTdiYmVkNjc1NzI2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:03:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxoR1VyNlMxMjY2SnJqdmxtanRyRmc9PSIsInZhbHVlIjoiOUk4eGNwTitBMzBWNEtubm5kQy9TOWswM2NhUlJWeXlpckpxdUNnaVE0Y0gzd1daNmNoRGtJSnFBRUY5WDk2ajVnZnhCYnlkTEZQVjNkdkRxV1REaWRweGJJZHFkaEluaWRYbGZpT2Nsa2ZTT2NRcVFld3JaaUJlZEZ2Tk11Y0xYNS9zK1ovNDVhSHRyUUhYNkNwTjVWNFpnNVhrQnlXOVhlUUg3UzBjZXZBdklmRVJBQS9nYk8rcmNjNzljeDBWRlJ3dkdYcTFOUzNjWDdXREl2VnNtOWEvc1hrSjBVUnhlOHUydEdZTGZ0QmpzdTZsMG55K2V0U2g0N2ZhOHdmckhlSWJGVGtqVXNpbVF1a2pyckRCdDJmRWIwWHlFWkZwUStwQS9ONU9pY0R0b056OVBtK1FoSWZCbXkrbGlTNG4wVllUd2VSM1poRkIxMEdSUG9wSjIvaVBlZkMyYUlhV1pSd2E3amx1a0JockEyWWxLTXVoYmkzNWpjOVVrYjJmTUU0UEVOZ1FsYUliYVM4OUJsbzhOaVdOcU9tQytBU09iRWZGbmR5U01JeDJ3RThIK3J2Z20vVkJJdFplSGZIQUNuc0gzYU93QnF2MEx3c1RIRFlTZlZ5bDdnVTNyNDNsM1pxT3JiQ2ZaSXF6anR1ZDF3U1BrSU5HYWFVU1pLODUiLCJtYWMiOiJlMTZmYzJiZDVmZWM3M2VkYWI0OWU2MWFkNTM2NjU5MGYwZjZjZTRjNmU4ZGU1OTI5MmU5YTU5Zjk4M2I2MjIzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:03:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjZKMDhicWZONzdrRVB0alhwcjJDa2c9PSIsInZhbHVlIjoiUlJqUGhOYlYwc2o2NVV3ajdSUXBZRFRZMk14N1hlaDBwR3AwWnZ5NUFLUllxMDZMbmR2Tit0aGI3RGJBUjNLK3hvUmY3WWZyZ2k4TFpGQmdFY2ErbnQzczQwZGpGcUk4QUF4Q3pTN3R5aDF1Z3FPZFlFdmZiSzBSWFhUK0k5cTgrRnhmeHozWmo1Z3ZFbm9xM0xsVXM1c0FYNy9PTGZWM092OWVSQXpUR0VJTXhBaVJPOEI2VCtYOVJlK2VWbXhTM0JVZnZYSEJ0N1g2Ujk2UjJ6bHZMU0ViRmJpemxCQzVWUmpPN3J0clBTWjZSWmQrbjE1MWEzWTFBTnlCb21CMHd0NzVzMzErYnhNYVRJYVMrYWxBOXlZLzVNY1NNTm0zcFR4SnIyQmdyK2xFNjlVSUkwNFZzZStvQ0w3QlA2dXI2UmFoMEpJcU5iSzQyT0JkYWhkRmNlM0YzT0JTeldxdCtGVlBGYTkzRzh1ZUJ2czhFM3lCbitQWWo1MzdWd2pKaHVwZ0tTTHdyOTdndGNtTDBreUhGOXhCUXhyN2RpWTNCaDYxRk5UMnNPdnNURmwrTXl3NmZCT1RzbVR5dGQyd0lodEgxTm1mOFpWcUJYbE4rdGFTMCtBNGhYMzcxRWJYNGlHWGk3S1NackpyazVpVG9vZ3N4TzEycDdZWC96UkwiLCJtYWMiOiJhMzRjY2FlMWE5N2YyODVkOGYyOWM0ZDI1NjU5YmUyY2Y2MDdkNTIwMjQ2ZjlkMjc5YmM0NTdiYmVkNjc1NzI2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:03:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1921914740\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1234639521 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1234639521\", {\"maxDepth\":0})</script>\n"}}