{"__meta": {"id": "X780e9ea574f51e071f9ce4306db8fda3", "datetime": "2025-07-31 12:32:44", "utime": **********.382038, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965161.214027, "end": **********.382101, "duration": 3.168074131011963, "duration_str": "3.17s", "measures": [{"label": "Booting", "start": 1753965161.214027, "relative_start": 0, "end": 1753965163.913467, "relative_end": 1753965163.913467, "duration": 2.6994400024414062, "duration_str": "2.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753965163.913523, "relative_start": 2.699496030807495, "end": **********.382109, "relative_end": 7.867813110351562e-06, "duration": 0.4685859680175781, "duration_str": "469ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47340304, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01665, "accumulated_duration_str": "16.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2084482, "duration": 0.01188, "duration_str": "11.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 71.351}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.277609, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 71.351, "width_percent": 15.796}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.301111, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 87.147, "width_percent": 12.853}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-889064124 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-889064124\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1122956195 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1122956195\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-546358460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-546358460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1164735417 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjQweVU0TURjVjgxSHBrcnR1K2hVaHc9PSIsInZhbHVlIjoiTkFvWkE3VGNPeUFHVVVzUW02UVJrVGlNcHlLTWlnRU0vMVZHaE5pWG14U0NEcTROWnJKL21uMFRvQmljSHl0UjJlL01URS9uN2VKWElXQmlDQVlOaGFsSEUwckhsS3ZWeEtKell2UGtyZVM1elpKWmFDWDVmcEVwMm1kSndzaXo3UnAvbjVrazZtb2tadHc5NjJneWdiTE1ReUNaOGs4WkUrS01OQU5kSWlSMDBOT0NFNTA4VDdhWDJhTG1DNENIeERseEFKeGhhalpLTHlZbUpMdTlyeEVGL2c3ZEJuYkt0NG15c2d0Nm5XOTQwUjIwakk3alU3aXF1L1E2TUVKUVhLYmhrbWI0WVJqZytNa2xoSGhVZ2NQTHZlcHVxdWpyQWRGUG5XYWxZSGxpbnRqUGhKY01BY1NHd3hHeW1SM0hlbjJ6NzR0azNEb0kwTEF1OWJoV056WEcrdUlGNXFJeGZLWDRYVWNYTXhHdCt6OTF2eHJKZjE3TE0rb1ZkQkttWEVvWWtkMkZENUFmV29PaS9na2VXNGFMTWw2WElsTjFmL2JqcGNVKyswTjNqNHpGdWE5cmhOaFVQYlRCUForUzFnOTJERXhWczZqTVhSTFBoa2pIR0VjUnpDd09nclUyMndYemhyVVhML3ZDQ1JGQStGVjZ3Wm9CSzcvY3JRNTAiLCJtYWMiOiI4NTkyMmEyZWQyZGIyNTI2M2U2YTIwODhmYjI3NGI5ZDViNjEwYTdiM2JjYWRmYTY5YjJiZjUzZWYwYTM4OTA5IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6ImFtaUlxY2RybDBYZksrelZpVzZwQXc9PSIsInZhbHVlIjoiUHVsVGdXNjVWdjRzTm94RjJSVG5hQ0lqNDVSajVLTkVNZTY3NFpOVzlMdjB3NEhvYVFReU92d2pTR0ZWYzVvTWl5Ym5aTHZyajYxd1RJdm5YVm4xR1hoT3lrQWpBbEw3Ri9nR2xkQkpOcjg2M29pcWJoVUI5MzEzN3JnNjQ5ZDhuOUJHWUUrT3hBT3VSLzFpV0JaUktwM3YrdFpvQStDOWQwT3VKcjRTZHM0ZkJhV1c3SGJiTU1NZjRRTkZ0aFlBbktrNnR6YTlXY0ZHRDVvamNpbXcvTWdnY3RiSlBNcmZBNWY5cVhuRHVhSmVZczZyZHlzRm1tTWNLT01qR3pQdmxPNndLUG1TT0E0dEx2aTl0c2Z0TkNlRE9pbXFJcnI1dmIyWWhGWXBWVUhxTk13YVdGajJaR2RSZ2FMK0FNSlRDY2p4NXlwVFE1c2Z4U3d4RlpHbTNaK0FYMzdsdklJRUl1eFVrbHowZnBIRWpQMDl3ME15ZVYvMWpVRlZrRzJMOGxJK1p0dmdpS2tWN3JqR0dXUy9vdk9BQ3phZXJTdG9mVzJiQ1Rqbmg4c2JBNEFoNGFEWVhvcGIyRXJuaTZqelA1cmYxYmVGNmpLNHRua3F6b3RBVktpbFliZDdvNEtTakd4WHI3ZzR5Q0txeFVYbk1IenhhZHpRbTNqSk9Da2siLCJtYWMiOiJlMTljZGQ5YTQ2NTM4NzhlNWY0NzVhZmViYTU3ZjM4OTkyZDM5YzBkMTcwZTAzNTdlYjgyYjQ2Mzk4OTEzNjEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1164735417\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2056651807 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056651807\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-351328160 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:32:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJqMC9kQ0ZsdmFvYUJlTjFIdk1uM2c9PSIsInZhbHVlIjoia3ExaURMUy9KaGhaUUVRaFJSQ2dRUi9Wd0thdXp1VXFxbTI1RCtYVTNYelN2UjExK21BNHBhVllHLzJFUDBMRnludmdFQzFPQXVzRndJQXpBSTQ2WHQ1VVU0WXlId1lSbHRpcSt1SWhKdGh0eFBVMVVXdHgxL1ZPUUVScjdkajdSei9rbjZLYm1IWHVuZy9LQ2JUbjExT0xTUHVzUjVHT3JmWlM5YWdyS0NMbmw3alNSWHJ3ZSt2OTgrTVJoT3ZiVjdKM0FHUUEwMnRYS2ZYRHJFMkVmay9WNXFYZTFEZmV0YmpuWHJEQ2dVZVhpNXRSNTJwTUwvYlo4K3Aycnc1Z1BXNmlTUFNKcnptTDVFOWY0YlQ1bERjQnVWZUVHcEZIYjBQd3RJdlJCQ1VCc3FxYUE3T1RiWkd2WFQ0bE1VODBCMEZ0czJ3eXdGaHpoRXlqdHRzRHNjMThCdHIrRzZ6bmR0Nzc2NGdqYU9MNTFUb29SVURPQjBKVTNQNTF1SHNTajFCUDgrVW11VzdldUJCdzJBQld2czBNTGxFVVAva0ZUUE5KbnRFanYwdDFCc3dLcVVRMjZPOUNNMGxjZTV6VjhLQzlVVDdYa0FOMW0wYWtna0o0cml4R0oxWmNiQzlDUCsrMjlEanRKSE15VlM1SFVZZVUrSXI5NGFsLzhZZnYiLCJtYWMiOiJmNjM2MGU2OGQyNjY0N2Q3MjQ1OTE4YTAyOGRmODk5MDJkZWI4Y2Q5OTkyODhmYzkxODZkM2UyYzc1MGU0N2I5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:32:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Ikh1WWsvUmhlWGxOUGVZSkdWNFlHOUE9PSIsInZhbHVlIjoiZzh2OXppa1U2OWZTZkRXaFlzMkl1bnREZnJCb2kyalJXN0M3WWl0NVAyOElublkybmozczFvSWlOU2FTeVRYYTQyV3M1a3RLTTZ2aWwyRmR0SUY2SUtoRDlTWEJvdDIzWGhJK2p1b3dxS2xjNXFOaVFxMzZHdWN4bzRSWWxxa1E4M1RNRGlTSUNWWTJLVHlZOG52R0hUMjJLbkx3RzI2RE1iQjNWRHd4ZUhKWU9zYmlQb2w2dmlRRGJsUkp5TGpuRHVVbS9CWmlYVE0yN2VGcHVRei9aaXd6TzFQVEtMU1pOQVFUdzc3TlZIR21oRzZMZjlhZDc0eWk5dWh3eTZwWTZVK3pwdUxpaHo2VmYzaUF4TTNVVnpITjVDa2VITGlVUUVadVdUQWJOajJmT1UyMk5nUmVyS0NZUlczMDVvNzlOUC9uNlN0d3k4RDhGL29ydHJtOUhBU1ZyYS84VEdiR2x3OWlUSk9kQ2lTdFFpb3lYdmFzelF3RE5PdG1acDh2UVI2RVlWeFlxdjhNcTVTcWc5VFFIbDZ3MnFBcFI2VzRCRVJLM0pzUTVaU3I5MHpMVVorUWlqK09jVzlndjkyVm14Ynh3MFlnb0dRWDJ5bUpINVFCYWxTKzZXdmUxa1BWSUR6My80MFdGYVBjTGduMWFnbXhQNHhOYkVHTGxEbE8iLCJtYWMiOiIzMWNhOTdmNmY3NmNlZTMzMzRlNzA3YzZhNzg3Y2Q5ZDQxODZhMDlmMjA3MmVkMDQxMjI0NDQ4YjZlOWZiMTZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:32:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJqMC9kQ0ZsdmFvYUJlTjFIdk1uM2c9PSIsInZhbHVlIjoia3ExaURMUy9KaGhaUUVRaFJSQ2dRUi9Wd0thdXp1VXFxbTI1RCtYVTNYelN2UjExK21BNHBhVllHLzJFUDBMRnludmdFQzFPQXVzRndJQXpBSTQ2WHQ1VVU0WXlId1lSbHRpcSt1SWhKdGh0eFBVMVVXdHgxL1ZPUUVScjdkajdSei9rbjZLYm1IWHVuZy9LQ2JUbjExT0xTUHVzUjVHT3JmWlM5YWdyS0NMbmw3alNSWHJ3ZSt2OTgrTVJoT3ZiVjdKM0FHUUEwMnRYS2ZYRHJFMkVmay9WNXFYZTFEZmV0YmpuWHJEQ2dVZVhpNXRSNTJwTUwvYlo4K3Aycnc1Z1BXNmlTUFNKcnptTDVFOWY0YlQ1bERjQnVWZUVHcEZIYjBQd3RJdlJCQ1VCc3FxYUE3T1RiWkd2WFQ0bE1VODBCMEZ0czJ3eXdGaHpoRXlqdHRzRHNjMThCdHIrRzZ6bmR0Nzc2NGdqYU9MNTFUb29SVURPQjBKVTNQNTF1SHNTajFCUDgrVW11VzdldUJCdzJBQld2czBNTGxFVVAva0ZUUE5KbnRFanYwdDFCc3dLcVVRMjZPOUNNMGxjZTV6VjhLQzlVVDdYa0FOMW0wYWtna0o0cml4R0oxWmNiQzlDUCsrMjlEanRKSE15VlM1SFVZZVUrSXI5NGFsLzhZZnYiLCJtYWMiOiJmNjM2MGU2OGQyNjY0N2Q3MjQ1OTE4YTAyOGRmODk5MDJkZWI4Y2Q5OTkyODhmYzkxODZkM2UyYzc1MGU0N2I5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:32:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Ikh1WWsvUmhlWGxOUGVZSkdWNFlHOUE9PSIsInZhbHVlIjoiZzh2OXppa1U2OWZTZkRXaFlzMkl1bnREZnJCb2kyalJXN0M3WWl0NVAyOElublkybmozczFvSWlOU2FTeVRYYTQyV3M1a3RLTTZ2aWwyRmR0SUY2SUtoRDlTWEJvdDIzWGhJK2p1b3dxS2xjNXFOaVFxMzZHdWN4bzRSWWxxa1E4M1RNRGlTSUNWWTJLVHlZOG52R0hUMjJLbkx3RzI2RE1iQjNWRHd4ZUhKWU9zYmlQb2w2dmlRRGJsUkp5TGpuRHVVbS9CWmlYVE0yN2VGcHVRei9aaXd6TzFQVEtMU1pOQVFUdzc3TlZIR21oRzZMZjlhZDc0eWk5dWh3eTZwWTZVK3pwdUxpaHo2VmYzaUF4TTNVVnpITjVDa2VITGlVUUVadVdUQWJOajJmT1UyMk5nUmVyS0NZUlczMDVvNzlOUC9uNlN0d3k4RDhGL29ydHJtOUhBU1ZyYS84VEdiR2x3OWlUSk9kQ2lTdFFpb3lYdmFzelF3RE5PdG1acDh2UVI2RVlWeFlxdjhNcTVTcWc5VFFIbDZ3MnFBcFI2VzRCRVJLM0pzUTVaU3I5MHpMVVorUWlqK09jVzlndjkyVm14Ynh3MFlnb0dRWDJ5bUpINVFCYWxTKzZXdmUxa1BWSUR6My80MFdGYVBjTGduMWFnbXhQNHhOYkVHTGxEbE8iLCJtYWMiOiIzMWNhOTdmNmY3NmNlZTMzMzRlNzA3YzZhNzg3Y2Q5ZDQxODZhMDlmMjA3MmVkMDQxMjI0NDQ4YjZlOWZiMTZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:32:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351328160\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-688690273 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688690273\", {\"maxDepth\":0})</script>\n"}}