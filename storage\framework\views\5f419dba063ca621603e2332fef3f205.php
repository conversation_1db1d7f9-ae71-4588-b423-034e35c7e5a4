

<?php $__env->startPush('css-page'); ?>
    <style>
        .finance-tabs {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .finance-tab-nav {
            display: flex;
            flex-wrap: wrap;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
            margin: 0;
        }
        .finance-tab-item {
            flex: 1;
            min-width: 120px;
            text-align: center;
            border: none;
            background: transparent;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #6c757d;
            position: relative;
        }
        .finance-tab-item:hover {
            background: #e9ecef;
            color: #495057;
        }
        .finance-tab-item.active {
            background: #fff;
            color: #6fd943;
            border-bottom: 3px solid #6fd943;
        }
        .finance-tab-content {
            padding: 20px;
            min-height: 500px;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
        @media (max-width: 768px) {
            .finance-tab-nav {
                flex-direction: column;
            }
            .finance-tab-item {
                flex: none;
                text-align: left;
                padding: 12px 20px;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Finance Management')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Finance')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <!-- Finance Tabs Navigation -->
            <div class="finance-tabs">
                <div class="finance-tab-nav">
                    <button class="finance-tab-item active" data-tab="plan">
                        <i class="ti ti-package me-2"></i><?php echo e(__('Plan')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="sales">
                        <i class="ti ti-shopping-cart me-2"></i><?php echo e(__('Sales')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="invoices">
                        <i class="ti ti-file-invoice me-2"></i><?php echo e(__('Invoices')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="transactions">
                        <i class="ti ti-arrows-exchange me-2"></i><?php echo e(__('Transactions')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="expenses">
                        <i class="ti ti-receipt me-2"></i><?php echo e(__('Expenses')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="reports">
                        <i class="ti ti-chart-bar me-2"></i><?php echo e(__('Reports')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="payment-gateways">
                        <i class="ti ti-credit-card me-2"></i><?php echo e(__('Payment Gateways')); ?>

                    </button>
                    <button class="finance-tab-item" data-tab="business-info">
                        <i class="ti ti-building me-2"></i><?php echo e(__('Business Info')); ?>

                    </button>
                </div>

                <!-- Tab Content -->
                <div class="finance-tab-content">
                    <!-- Plan Tab -->
                    <div class="tab-pane active" id="plan-tab">
                        <?php echo $__env->make('finance.tabs.plan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Sales Tab -->
                    <div class="tab-pane" id="sales-tab">
                        <?php echo $__env->make('finance.tabs.sales', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Invoices Tab -->
                    <div class="tab-pane" id="invoices-tab">
                        <?php echo $__env->make('finance.tabs.invoices', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Transactions Tab -->
                    <div class="tab-pane" id="transactions-tab">
                        <?php echo $__env->make('finance.tabs.transactions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Expenses Tab -->
                    <div class="tab-pane" id="expenses-tab">
                        <?php echo $__env->make('finance.tabs.expenses', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Reports Tab -->
                    <div class="tab-pane" id="reports-tab">
                        <?php echo $__env->make('finance.tabs.reports', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Payment Gateways Tab -->
                    <div class="tab-pane" id="payment-gateways-tab">
                        <?php echo $__env->make('finance.tabs.payment-gateways', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>

                    <!-- Business Info Tab -->
                    <div class="tab-pane" id="business-info-tab">
                        <?php echo $__env->make('finance.tabs.business-info', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabItems = document.querySelectorAll('.finance-tab-item');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            tabItems.forEach(tab => tab.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding tab pane
            const targetPane = document.getElementById(targetTab + '-tab');
            if (targetPane) {
                targetPane.classList.add('active');
            }

            // Store active tab in localStorage
            localStorage.setItem('activeFinanceTab', targetTab);
        });
    });

    // Restore active tab from localStorage
    const savedTab = localStorage.getItem('activeFinanceTab');
    if (savedTab) {
        const savedTabItem = document.querySelector(`[data-tab="${savedTab}"]`);
        if (savedTabItem) {
            savedTabItem.click();
        }
    }

    // Handle URL hash for direct tab access
    const hash = window.location.hash.substring(1);
    if (hash) {
        const hashTabItem = document.querySelector(`[data-tab="${hash}"]`);
        if (hashTabItem) {
            hashTabItem.click();
        }
    }

    // Update URL hash when tab changes
    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            window.history.replaceState(null, null, '#' + targetTab);
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/dashboard.blade.php ENDPATH**/ ?>