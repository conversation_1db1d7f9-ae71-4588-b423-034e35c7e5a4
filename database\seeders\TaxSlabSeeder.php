<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TaxSlab;

class TaxSlabSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $taxSlabs = [
            ['label' => 'GST 0%', 'percentage' => 0.00, 'is_exempt' => true],
            ['label' => 'GST 5%', 'percentage' => 5.00, 'is_exempt' => false],
            ['label' => 'GST 12%', 'percentage' => 12.00, 'is_exempt' => false],
            ['label' => 'GST 18%', 'percentage' => 18.00, 'is_exempt' => false],
            ['label' => 'GST 28%', 'percentage' => 28.00, 'is_exempt' => false],
        ];

        foreach ($taxSlabs as $taxSlab) {
            TaxSlab::firstOrCreate(
                ['label' => $taxSlab['label']],
                $taxSlab
            );
        }
    }
}
