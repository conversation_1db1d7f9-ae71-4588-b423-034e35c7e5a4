{"__meta": {"id": "X807d81a46f20622acbf03014d8327918", "datetime": "2025-07-31 11:14:37", "utime": **********.542772, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960476.028733, "end": **********.542818, "duration": 1.514085054397583, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1753960476.028733, "relative_start": 0, "end": **********.307934, "relative_end": **********.307934, "duration": 1.2792010307312012, "duration_str": "1.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307957, "relative_start": 1.279223918914795, "end": **********.542823, "relative_end": 5.0067901611328125e-06, "duration": 0.23486614227294922, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47345312, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02553, "accumulated_duration_str": "25.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.435581, "duration": 0.020050000000000002, "duration_str": "20.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 78.535}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4831731, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 78.535, "width_percent": 5.17}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4937, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 83.705, "width_percent": 16.295}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-779757265 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-779757265\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1964060569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964060569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1489950334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1489950334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1351120312 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjN6d1ZOQnhIVFBqTlpoeVRDS0RuNnc9PSIsInZhbHVlIjoiQ0puRmwzTjlnem5aWjk5M0JlWnoycUxWaXIvVll5bWVaK0pkSW5na0x5bG1iWFltODBVZkI2YThQdnlWajZlNDRKY1JUdjBMOEVqN1NSS05nWXlyWncrYXVmT1k4KzgxMmpBejhCVFFDMzlndk9mdnZIL3Y4RDV6Zms2R01JSGltM3VIbXhHVW1tdzcyenlwcEhiQTNQclNFTlUxTUFHU1dlbE5HeUlzdGt1NFIzYlBVVmRpaVJSbkJsdUVYSTJMU0R3TFlkVHhsR2lkVVFRdjYrSjZhYW9kVXhSUDRlK25EUFpBdlVDdmRnb1Q0UlpNdFJZRVEzS0xWdVJKWnd4OG0xUG5sQWVsUHQ1Uy9xYXpCRUh3YmR2cU45NHo3MkdoVWVVU3Uxck1LRzkvZGJESXVpMjBybGgzYWVVMStHY3hNdHBXWlBXeXFRUEFzank1Si9OTDhwUXJGcmdGS000bVB2S2dDQ0lzVGRuYWwzZVJMTkd2NmQzUG9YazlWSHdONWJRV2hFY011SjBwUXB5NkZOUGxuZWZZMlgyWVlRY3RLRTJ4SWhxUDk4TVluK0xjbElyd0pUc3RtS2ZjS1JDOHEwUFR0R0JtUlFaN2xHamZPMXdDTmdpSEJPYUR5K1k0ditMajRQeGRYZExUMU82aHQ2WGExUmR4MTBxZmlmWjgiLCJtYWMiOiIyZTQxMGE1NWFjYWQ4NTM0OWIzNGY4MzE2Zjg5MWY1YmEwY2IxN2ZjN2JjNzBlZDk5MjQ0NmRiMjVmOTY4NzMyIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlpxQit5TTNKOUFpaW5lQTUrK3lPOFE9PSIsInZhbHVlIjoiWjVxRzM0eG9wY2VvYXpEblVSdDFYaWxjc0RrcFRhNStyT1NWc3FZb1pudmJ0TDF4bU1MLzUyWTQwRmNFeGNWRkxlQWt6aWZlZmlpQmtOY2xkSEg1eVAvWnc2TUt0b3dtcjVHS0V1eHgxMmxDS3VuZ0VnS3B6SFFyeXJrZEhoZnU0dHF3eXhFL09tU0kycFpiVWlXOFNkYmpmdGN2eEVwZWZjQVZ2N2xZVG1jNW1LWnU1L2VndldWWndJeisxaTBwU1ZXRkdsL2l2akx0cWZBTjJiLzZralFVRXBYTHVYYXFaWGR0T3lBdUw5dkNKRHVtZkFGSHNqWmUwMll0RXNNYmhtOTlPaWcvY1EwWi9qOU1pUkJYYVlWcndzNDdSSm1VWkxFY0JBbVRVQ3NPSnJWTWtaa3NVVjN2eE0xVDRvak9LY1VpNER6bFRaNEMzanE5T2QvNVN1MTB5WjRTYzR1THZNMExERjdtbHVGdTBMMDV2YWZaNlFTVVBlTTc4aVZtbjdXaTYzWkd5YmkzMUk5ZEs1UUlNMTlkWEhKek4xRkhhQm9BbUcvVFQxMytQVHJ5Q1J1R3BsSTlKYWgrWGhXK0pxZ3IzVm1oREpTL1Q2dUN3cERKVXA0ajdSa01jU3d5ZkZ0cndRN0FTYjg1QURwb3BHR3JSUDIrMisyem1aZnYiLCJtYWMiOiJjYzk0YTA2MmMwYTY4MWU2MTk3YjFjYjhlYzczMWM0YTAzODI2MmE1Zjk2MGE4Mzc2Njc5YjFlNmRmNWI2OWE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1351120312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-630571441 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630571441\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-953151732 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:14:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFoS2kwTGxFZXFxclZ6QmppRHZ5aFE9PSIsInZhbHVlIjoiTHdId2ZnUUduNzhsVERvaElwZ1hyTmFqY1F2M0NNYm5mZTBVZXU1OGI1Y09jN3BKM2psNnEvNmlFeGN2azJYT1luaTZvaHo4cjZnSjhRVGhQOGNUTmpVaTJIZHJEMWlWTVMzckFpUEJoaEpXRVpRK0FHL0JWRTdOMnQrdURKM2M3M2N2aTVUUWVRZmdhYkQwbWg1MjgxTHQ5S0tJYjE3a0FDVGdtZ0hQQWNIRGovYkY3d0RieVdCMm5pZ2liUHVNWmsxbk95WTdnWkpFTGNCVHpQUGk2Z012NGpiYUJVOHFQVDRGdnBkNHhxd0RaWllWa2F5amluazZFVkcvNUVoQUNyVkoxYjlsYUtKeUR4MWs5bDRDZ2lnSGFoQWlSYkFzbjc3Zlp5OGZuVWFYSnl1cUJZaURBTEhWc1VVU0tzU05IV3l2WWsxVlRMby9ZUmRNclJYb0pIbDZNRUxibnNmN1FLZTBQREtITTdPNnltdUduSGVHbmhaaHprdFBJQTFEcGcveURoamFZc2lLYklEYzNoN2tLYjdJL3IyYnJteUIxSHJHalgrV0xZbEcyeWMwTTlMMHY2YnlHeXYxRmhhbTRnMGlKYkhpRWJNcFZMTnJXQVZEbHl5cGwzMUdUOS9VQndYbnpLRFQ3dnl6bEFLTS9WU05paXlUQ2wyd1l5cHIiLCJtYWMiOiJiYjg1N2YzYzIxZWZiNTA0OGI2YzcxOGJjZjU1OWVkOTRhZjI5MjcwNTQzOTQ3YWUwYzE3MjM2NTI0NzIwOTM5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:14:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6ImRaT3VzanczSzFwOGhPWmIzWEJGT3c9PSIsInZhbHVlIjoiMnZWWU9IRGVZOHBXbGQzUWM5VitrUG5kbnZublVMMHJaZytLR0hjWURrRzN3UjU2VitUOTNZT3BtTGtwYnpoZGdYVlBOeVp6SHNsblphNXVpTFVhN1c1bjM0TnRjemxHV2hWRmlhUUhSZnJ2MHBXbUozL1Evdy9mY3VIeUdpR0FPWnoxdmllVjZxdlQwSENnOVd0TEJzZDZldUtSaTNnN2NvUGtscHFVb1h4cjlYV1BZaVdaa09kbXVCVStDSGJnOVpuQmtPT1ZPZmtvWkRSVTh5NGcyZFZMcGlvTE1vRGYyRzBySjNkbWMrWkxuM0ZFV2xnWUN6OWdMWVprcGtLUFhiOFI3OHhMRlJkWlpMQU1Pa1U3dGNkTlFnaUdEcFM3WmxielVDR0pJT0V5Q1FWVVJQR3R6QmhNS3Rqb0ZlakRNRXN1M3UzenM2TUZlTVA3dG1ocUlDaWMvU3pNQXFkUFJZdDNyTUg2SE4xS2NkdDB5V3Ntb1Jnazh6V0FrR2ZmQ3FUS00rUExBekd4RFJkcFhrd2lEWEt0RUdYV3RwRE9CWTJJZXRrRjNtNUxKSllGN0xLS1o4SWthUkVrYlp5eGV5NTk1cGtkUncwZzNSdFFuWkV3a24raGFzdjhFYzF2d2t5ZHJ3Z1FVQnFxMm9DK050WmNmNEM2dDNvZVlHT0UiLCJtYWMiOiIyZTkxOWE0MGJlOTJlM2FiYzk1YTZkNDQ5OTg4NzFlNGU2ZWI4NThjMjRkNjFjZTFjODE1OTg5NGNkY2E1M2FmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:14:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFoS2kwTGxFZXFxclZ6QmppRHZ5aFE9PSIsInZhbHVlIjoiTHdId2ZnUUduNzhsVERvaElwZ1hyTmFqY1F2M0NNYm5mZTBVZXU1OGI1Y09jN3BKM2psNnEvNmlFeGN2azJYT1luaTZvaHo4cjZnSjhRVGhQOGNUTmpVaTJIZHJEMWlWTVMzckFpUEJoaEpXRVpRK0FHL0JWRTdOMnQrdURKM2M3M2N2aTVUUWVRZmdhYkQwbWg1MjgxTHQ5S0tJYjE3a0FDVGdtZ0hQQWNIRGovYkY3d0RieVdCMm5pZ2liUHVNWmsxbk95WTdnWkpFTGNCVHpQUGk2Z012NGpiYUJVOHFQVDRGdnBkNHhxd0RaWllWa2F5amluazZFVkcvNUVoQUNyVkoxYjlsYUtKeUR4MWs5bDRDZ2lnSGFoQWlSYkFzbjc3Zlp5OGZuVWFYSnl1cUJZaURBTEhWc1VVU0tzU05IV3l2WWsxVlRMby9ZUmRNclJYb0pIbDZNRUxibnNmN1FLZTBQREtITTdPNnltdUduSGVHbmhaaHprdFBJQTFEcGcveURoamFZc2lLYklEYzNoN2tLYjdJL3IyYnJteUIxSHJHalgrV0xZbEcyeWMwTTlMMHY2YnlHeXYxRmhhbTRnMGlKYkhpRWJNcFZMTnJXQVZEbHl5cGwzMUdUOS9VQndYbnpLRFQ3dnl6bEFLTS9WU05paXlUQ2wyd1l5cHIiLCJtYWMiOiJiYjg1N2YzYzIxZWZiNTA0OGI2YzcxOGJjZjU1OWVkOTRhZjI5MjcwNTQzOTQ3YWUwYzE3MjM2NTI0NzIwOTM5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:14:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6ImRaT3VzanczSzFwOGhPWmIzWEJGT3c9PSIsInZhbHVlIjoiMnZWWU9IRGVZOHBXbGQzUWM5VitrUG5kbnZublVMMHJaZytLR0hjWURrRzN3UjU2VitUOTNZT3BtTGtwYnpoZGdYVlBOeVp6SHNsblphNXVpTFVhN1c1bjM0TnRjemxHV2hWRmlhUUhSZnJ2MHBXbUozL1Evdy9mY3VIeUdpR0FPWnoxdmllVjZxdlQwSENnOVd0TEJzZDZldUtSaTNnN2NvUGtscHFVb1h4cjlYV1BZaVdaa09kbXVCVStDSGJnOVpuQmtPT1ZPZmtvWkRSVTh5NGcyZFZMcGlvTE1vRGYyRzBySjNkbWMrWkxuM0ZFV2xnWUN6OWdMWVprcGtLUFhiOFI3OHhMRlJkWlpMQU1Pa1U3dGNkTlFnaUdEcFM3WmxielVDR0pJT0V5Q1FWVVJQR3R6QmhNS3Rqb0ZlakRNRXN1M3UzenM2TUZlTVA3dG1ocUlDaWMvU3pNQXFkUFJZdDNyTUg2SE4xS2NkdDB5V3Ntb1Jnazh6V0FrR2ZmQ3FUS00rUExBekd4RFJkcFhrd2lEWEt0RUdYV3RwRE9CWTJJZXRrRjNtNUxKSllGN0xLS1o4SWthUkVrYlp5eGV5NTk1cGtkUncwZzNSdFFuWkV3a24raGFzdjhFYzF2d2t5ZHJ3Z1FVQnFxMm9DK050WmNmNEM2dDNvZVlHT0UiLCJtYWMiOiIyZTkxOWE0MGJlOTJlM2FiYzk1YTZkNDQ5OTg4NzFlNGU2ZWI4NThjMjRkNjFjZTFjODE1OTg5NGNkY2E1M2FmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:14:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953151732\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-807999436 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-807999436\", {\"maxDepth\":0})</script>\n"}}