<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductBumpOffer extends Model
{
    protected $fillable = [
        'product_id',
        'is_optional',
        'title',
        'price',
        'description',
    ];

    protected $casts = [
        'product_id' => 'integer',
        'is_optional' => 'boolean',
        'price' => 'decimal:2',
    ];

    /**
     * Get the product that owns this bump offer
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get formatted price with currency symbol
     */
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }
}
