{"__meta": {"id": "X64a9c44f567212d0577f19a5b91253a0", "datetime": "2025-07-31 12:33:14", "utime": **********.434144, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965192.8787, "end": **********.43419, "duration": 1.5554900169372559, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1753965192.8787, "relative_start": 0, "end": **********.255789, "relative_end": **********.255789, "duration": 1.377089023590088, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.255824, "relative_start": 1.3771240711212158, "end": **********.434196, "relative_end": 5.9604644775390625e-06, "duration": 0.17837190628051758, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46937392, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1604\" onclick=\"\">app/Http/Controllers/FinanceController.php:1604-1663</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00835, "accumulated_duration_str": "8.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3663378, "duration": 0.00588, "duration_str": "5.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 70.419}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.401412, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 70.419, "width_percent": 14.371}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.411596, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1630", "source": "app/Http/Controllers/FinanceController.php:1630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1630", "ajax": false, "filename": "FinanceController.php", "line": "1630"}, "connection": "radhe_same", "start_percent": 84.79, "width_percent": 15.21}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-92338146 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-92338146\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1440525254 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1440525254\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-343468068 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-343468068\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-175970361 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ijh6YkZFTnJYYnYxdEY0dDlpVlJEOWc9PSIsInZhbHVlIjoiMkJwYXdPZkc1ODZaeG9GMEN2bmt1QXlOL1pWVk8rSHNnSG9NZkdGbDRRNWRNbncwUWtSYzYycFh2YTZDWTVzaElhMWcxeUJlaDVwNi9XSlAwQnhaOFVOaTc3WEM3THlGLzVWQ1pVRUZzZitadFlBVzF2RUhwSE5RZkkrRkFBL2o3Q3dFaTA3KzNhcWZlaXYrMDZudTdUQTdROW9QYmpTaWU3UVRXUjU3REV2bGdOZ0VvY1phelk2ZWQyeG1zV1JMTDkydTVJSlprM05TNGQ4U0hwb29qRDF0Y2hhTTFoYW1hZFFrUnIxQnZjWXRWNVFtTld6Y0tFTlZEY0h0dkhHLzVCU2kyeGpqVkExY3FLbTZRdlo3TjNHaTJwemc1VW1EK3NCQWltaXljVGgrMnBrMHl4aWZrQXA1aHBOSHhZdldzNWZWUVVPK3hvS09DNEtnUEF3NTQweFNnL0JDRWZwaFl2QTZOM09pU1c5SVpSYzZLU0xzN2JvaFpDZy9JSVpwR3FwbmVaY0ZtMUdGakpLNDk5bnJYVFgxWVc2aTNscE15aHozQ3VZTVRtdnNjUWRaVSs3WjBQVjl4WXhXNi8wTFo4OC9mOGtRemRvNnFHdG1qL3dJLzFCdWdNbndjR0l1OEQ4ekpITHcxYUlVVWZvbWV0a0NkZkhKODBtanBmWDgiLCJtYWMiOiJiODUzZWFkNTg0MWZiNTgxZTI3YzMyZTdkNjgwZDllN2VmY2I3Mjc3MzgwYmZlN2JkODk4NDNiYjdiMjNjN2Y0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IjNRb2ViUlVIZkNzcml0TGRVUEZpT3c9PSIsInZhbHVlIjoiK1hmY1NET3NaaHpXRHFWcjFRbGh2MHp4VmVyelhqN0doaWZLbmpTRlRsdzcwKzVZTTFLU1dGc0hobEc5TXBIMVh3ZmVlMHlrNXFmVDA1V1h4YnJjK0szMVFaRGRTNUZ6Ylk1a3p2cFR1YUZaeFZmS2VrSHdEYU41RU1yT08yd1lMUGtFc1dkNGY5d2RkZk42b2dKdGhlN1dEMDJ6eGM0TDBzbHlYM0E2OGlwZlY5akk0M2ZUanc2aXVxVUhPSUJPNTdhYjNEdHp2dzFDaVduUjdDcFJ0TCtBSlpTcUJ2djdhYnBmVnc5ZytzaFZpRHV1Nnc1L3c5U2hSTXhsMlRIcTNtRG9LcHFNSjR0VitSRC9Wa1h1SitsUmlzWllRbFR5aVZiSlQwajlzS09GOTdNSlI0b0JyWTN6Sm9rSXN5dDlmVzVaM2NLNndXVWlaYnJ6VWkvWXIyR0JxUk5kQkMzTmhoM0Zib29zUDJKU3BOMmxUeENyUjRORGFTK2tic3F6SS8rd0tPeEJ6dFREc2xEYVpjL29yNEJoeit5Zklpa3hkN3RhenRCNk9jbWJnYnVuZlYyd2ZpNXZDRDY2SzNOMnBHLytQbTgzZUU1Uis2cS9Fb1Y0QjRISnBlQXBza1RxK2lUWEF5cEN0NjRCejhONlpGZ0VoNUhHYXN0QkE0RTYiLCJtYWMiOiIzNDUyNDRiNTVmYTBmYWIwZDhhOWNhM2Y1Mzc4ZmMyNWJhYzQ4Njk2MGE3ZTVkNWNkNWJjYThkNGExYTEyOWJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175970361\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2024081147 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024081147\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2109879315 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:33:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1zT2xHYmVCaWhlUFI1Zm9KUmI5clE9PSIsInZhbHVlIjoiTy9ySTBUMi9VSm5ubk1JNkErZVFqQ3p2TC9FNjErZjNneTNaUnVtMzduOHY1SmtpYnJQMzhOSUhjdWZxQnNOSGZJUGZyeXUwSnBaeXFFMk5MbWtmM0J1Y0ZDQlIyNUtUZUROVGplbE9mQ0tyNU9manFPaE5KQmJZTEE4VkdrT1U5cWMwV3JDNEdwVzM4Z1hFN051Uk1QQTZidC9RbkltOXBRenlDc1RVV2l3WnNHSFJXblNWcVFHNElZejNnTjcyWjlDZE9ZdmlDSDNtUXR3elFaOVEyYjNmZ1V5Y1JFMnVJSXhKRjRWR0o1aWVUVVdwVXJFL2J2MWlYRlpFSEtMVldEY0oweVA0WGUyMCtGaFErNlBMWkJMT0EraXBvblhwdERyNHRZKzJKd1VZblcwVTMzLzhJc2ZBTkJFaDFISzFWejZnZUZZM0pUcW1VOE42R2FpTEdFRmY1RU8wZ2dPTzFWb1lEeXhiMFdQOVh1WHE1bnNraHVpU1JpVnZwbU83ZVA1cjhRU1lNTmc3R3lydFNZRzcxditvWUpGYkY0Q2c1My9oOG5qcGR6SG1xTWlvTGZXVWh3aHc3Y0tORGlJZUplTHpCNjNiY2JSbUV5VmUxM0xLaU4zUlNTdFY0SjF5RkdlN0E3aCtBbWpPUEs1ak8ydlMvVEdES1BjSkJGREMiLCJtYWMiOiJmMGFkNjhmYTQwZWYyYzU0ODBlNDlkNzdiNTk0YzQ0ZTI3ODBlYzY4NjU2N2M5N2EwZmE0OWIxZDkwMWFlNTBmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:33:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IjZ0aUQ1emxZVE4vMnA3WWhHZWU3Z1E9PSIsInZhbHVlIjoiNXZKN2RJS2RXM0lvQ3pMOUpwWjBvbEZ4SkpYdi85T3ZqTStzOW5DaDVGTjQ2YXU5MFk2M0VFZGVhaE5waytEbSt3c3FKL3lnQk02S3JYSnBDalRoWlA0YThsOXZaUnRKL2Vyd25ZMEpTSHM0Ykl5cm5lS1JEWjJZMWtMbjRhbFhlb2l5dTVGUTVicmRJYnl3TENrbVpnN3d0dXlFVlFqRXgralVYMDRFbFZsNHJFaExnNFBScDVHRSs0N1oxTjc1V0xHcEsyaUtRT09mK011d0pKdUQwSE41VDRhaEIxekpNWEt3UEw0VlQ2KzNYMStUSExSWmg3SEdkNDduazdoWUM4QTQzNDNGMFNlQUFnb3pYajFweEhMc0gwYzFWcUdYVW1ySzFtTTNRaHZZRGgxcDhuMTJIUGd5WE5wSHRzcTR6YXZTUzBxZTVQRERPYXFBRlhpanZ0UUJTNkN3NldaY2RQcEZKNEVzR3NnVXhtYTFsc3BmZXRrS3dQR2lrOW1XS0ZvOWZKbU5nT0VEZzFnYTBIMGFtS3JzUXVTNStiNXBmVkI1Q0lPMldVZ216Vm1CemJqOUt5V2gyNXJORHNGd1lQa014cGQ1cXJXWUV0VGpNaXZJSTZ6SXVDYkJtUjhsYUtvRnFZVmcrb09SZ0NMUG5aNXNWSmlUaVdlak12WlkiLCJtYWMiOiI5NzgxMWVkZjNlNzdiNGE1ZGZiZjJhOWM0Y2Y0YTc0ODgxNzk5MjAzYTcxNjE2NWE3Y2M5MTcwMTc3ODZiMTNjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:33:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1zT2xHYmVCaWhlUFI1Zm9KUmI5clE9PSIsInZhbHVlIjoiTy9ySTBUMi9VSm5ubk1JNkErZVFqQ3p2TC9FNjErZjNneTNaUnVtMzduOHY1SmtpYnJQMzhOSUhjdWZxQnNOSGZJUGZyeXUwSnBaeXFFMk5MbWtmM0J1Y0ZDQlIyNUtUZUROVGplbE9mQ0tyNU9manFPaE5KQmJZTEE4VkdrT1U5cWMwV3JDNEdwVzM4Z1hFN051Uk1QQTZidC9RbkltOXBRenlDc1RVV2l3WnNHSFJXblNWcVFHNElZejNnTjcyWjlDZE9ZdmlDSDNtUXR3elFaOVEyYjNmZ1V5Y1JFMnVJSXhKRjRWR0o1aWVUVVdwVXJFL2J2MWlYRlpFSEtMVldEY0oweVA0WGUyMCtGaFErNlBMWkJMT0EraXBvblhwdERyNHRZKzJKd1VZblcwVTMzLzhJc2ZBTkJFaDFISzFWejZnZUZZM0pUcW1VOE42R2FpTEdFRmY1RU8wZ2dPTzFWb1lEeXhiMFdQOVh1WHE1bnNraHVpU1JpVnZwbU83ZVA1cjhRU1lNTmc3R3lydFNZRzcxditvWUpGYkY0Q2c1My9oOG5qcGR6SG1xTWlvTGZXVWh3aHc3Y0tORGlJZUplTHpCNjNiY2JSbUV5VmUxM0xLaU4zUlNTdFY0SjF5RkdlN0E3aCtBbWpPUEs1ak8ydlMvVEdES1BjSkJGREMiLCJtYWMiOiJmMGFkNjhmYTQwZWYyYzU0ODBlNDlkNzdiNTk0YzQ0ZTI3ODBlYzY4NjU2N2M5N2EwZmE0OWIxZDkwMWFlNTBmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:33:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IjZ0aUQ1emxZVE4vMnA3WWhHZWU3Z1E9PSIsInZhbHVlIjoiNXZKN2RJS2RXM0lvQ3pMOUpwWjBvbEZ4SkpYdi85T3ZqTStzOW5DaDVGTjQ2YXU5MFk2M0VFZGVhaE5waytEbSt3c3FKL3lnQk02S3JYSnBDalRoWlA0YThsOXZaUnRKL2Vyd25ZMEpTSHM0Ykl5cm5lS1JEWjJZMWtMbjRhbFhlb2l5dTVGUTVicmRJYnl3TENrbVpnN3d0dXlFVlFqRXgralVYMDRFbFZsNHJFaExnNFBScDVHRSs0N1oxTjc1V0xHcEsyaUtRT09mK011d0pKdUQwSE41VDRhaEIxekpNWEt3UEw0VlQ2KzNYMStUSExSWmg3SEdkNDduazdoWUM4QTQzNDNGMFNlQUFnb3pYajFweEhMc0gwYzFWcUdYVW1ySzFtTTNRaHZZRGgxcDhuMTJIUGd5WE5wSHRzcTR6YXZTUzBxZTVQRERPYXFBRlhpanZ0UUJTNkN3NldaY2RQcEZKNEVzR3NnVXhtYTFsc3BmZXRrS3dQR2lrOW1XS0ZvOWZKbU5nT0VEZzFnYTBIMGFtS3JzUXVTNStiNXBmVkI1Q0lPMldVZ216Vm1CemJqOUt5V2gyNXJORHNGd1lQa014cGQ1cXJXWUV0VGpNaXZJSTZ6SXVDYkJtUjhsYUtvRnFZVmcrb09SZ0NMUG5aNXNWSmlUaVdlak12WlkiLCJtYWMiOiI5NzgxMWVkZjNlNzdiNGE1ZGZiZjJhOWM0Y2Y0YTc0ODgxNzk5MjAzYTcxNjE2NWE3Y2M5MTcwMTc3ODZiMTNjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:33:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2109879315\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-623573520 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623573520\", {\"maxDepth\":0})</script>\n"}}