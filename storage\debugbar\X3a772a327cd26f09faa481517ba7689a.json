{"__meta": {"id": "X3a772a327cd26f09faa481517ba7689a", "datetime": "2025-07-31 12:22:31", "utime": **********.226297, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964549.647586, "end": **********.226333, "duration": 1.5787467956542969, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": 1753964549.647586, "relative_start": 0, "end": **********.011075, "relative_end": **********.011075, "duration": 1.3634889125823975, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.011108, "relative_start": 1.3635218143463135, "end": **********.226337, "relative_end": 4.0531158447265625e-06, "duration": 0.21522903442382812, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936960, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1604\" onclick=\"\">app/Http/Controllers/FinanceController.php:1604-1663</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00843, "accumulated_duration_str": "8.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.144662, "duration": 0.00563, "duration_str": "5.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 66.785}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.187398, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 66.785, "width_percent": 14.116}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.198859, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1630", "source": "app/Http/Controllers/FinanceController.php:1630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1630", "ajax": false, "filename": "FinanceController.php", "line": "1630"}, "connection": "radhe_same", "start_percent": 80.902, "width_percent": 19.098}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-850075144 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-850075144\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-51224222 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-51224222\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-681678559 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-681678559\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2035535804 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFHSnIzbWR3cURWWWlKL0VucVRlWHc9PSIsInZhbHVlIjoiVnBjaDNWU0xWVTIreXRTSkhGODFPeHh5QWY1empDNUFkckthMGJYaHVzeGVzUEwzUGc5WDM5LzFVUis4MEcxQy9pMCtmVUd2MEVQQmYxRHB2UjdBdy9PRFo3U2FYKzR6S2g3WWxsREt1UWpPb0grcnNreGRWSDQ3TnZ3L2cxVXBScWFHekt4UDQ3ZGhFWGdGNURzUUhkZldMWXFWc0Z2eDN6bXVSL0t3NVBJa3pJdzc2S0w0VHUrallkNjY0RHlOM0x0NDRJWmp4MmZHc29CRzBHZWxDOTMreWFNWm1qMytpQjBYcTFCbEhXZnlLdmJidzlZbmNOWS9HOHVDcWFISUo3M3FnSk1jaUN0VTRKaVYzc2lCckg3RUF3NWhOcE1ROXlqUnZOdVlKL0l3bGRzV0MvZHNQaW1Mb0ZIQitQaVdtck10UXpFTE03dk5GYWFacmlMY28xY0NUbkRBdk1iaGhpaG54aldsSW9wYWRVbld5MU1CSS9Lb3JVMjFuL05uUFJIWGpoV2ZycCs3TmRJKzNmYVFjMThkT3pxcXBxWDk4cmgzRG82UFVlSDZsZUtuWDdFM3J3NGxIZUdBSTJveTNRaUVCRVo3Q01kNUMwR3Ird3RtM25ISkhqT3pDV3RYeDE5QXk0TTMzRG1PczVYYzY4cTVYR2VCb2RMNjB0OUIiLCJtYWMiOiJjY2E3YjVjNGUyMmExZTdiZmJkYmM0YWUwNGI5Y2UzM2U4ZTgxZWVmYzJlNzQzOTUwNGRmZTlhOTg0NTA5Y2EwIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkFpUm5TK3lVZ0tHaU5SMkVUcVhVRUE9PSIsInZhbHVlIjoiV01KRzlrM0hXTU9seGNTWFFjTWZCUFdPQ2dZcUJnUWgxQXJMczBWS2crMnpvdzNQRDNvenc3MEhscm9SR2prVkRzWHhvWlVJNVVoNmJLQUNvVU4xYTFYa2FiMTFtMWxKVGF0RkZHSlRINGNPWmpYY0twNFRuZnNBSWswVkFHeXNhbDhvMkpRbmxOTVRxWEtyQXBGYytSZmw3Z1ExL3FWSEl1dEdlRkw5YUtYd1orZVFPSlRXRlQzcGt6WjVhc3VnSHZpTnE4cXg4WmNxZXZUK2Y1cnVDaXhVVUlBTTlpMDFFNUlSUXk2cDJMY29OcFFQRTQ4Si9sQzYvdmFqL1h5WlFVN3Q2WTRKNGszNnFEMHcwRWxoRU1KNnpralFKRVRudlMwZ2w3WXpPUXZSN0ZGN3JJYkdXNHJLYzBlZ0JINERwSzB3aHJiMEpzdnVwUzhoeTlLemJaWkU4Z3dpek5KejNwRWpHaGZsNSttL1AveG1SNXJPb0JBVUQ3cmNFZ2NVeThIbHhiVDRtMTBPWUk2cXRHUTMzZ0F4NUN3RTB4RUk0M0Jud0gvN1c5UjR0cThldFVZNmgwcjVDeVdWTkFTMVhybFZxcGE2bVdnWVdyTHFlV3JUT3hSWXV0YUdubUFiK1dIQUFtYVlPMkdXb0k2UFVuam9xR2pNejFqNzBWOVEiLCJtYWMiOiI2NGUwMDRkM2EzZTI3NmEzZDYxN2I1NGYyN2Y3YTc5ZjA1M2UzNTZkNzUyYzE5MjU0NzcyNTQ4ODBmMDRlZDcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035535804\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-823700608 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823700608\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-149450489 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:22:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdzMnRnbzR3U3FFOUFSSkR1amtPeEE9PSIsInZhbHVlIjoiMmw3anltc3RpWStKZ3BHK1AzUUp0enBIaXJPS1d1elV0cjQ4RXNlV3d5dEpYbTMrNys3TzY4ZUNJWU5NbWdmR3piYzF0eXFCbFVDYmJKc1VNVG9NYnlPZlgvOWlwLzlUNU1wYUhJa2pGMW1KZ3BZby9jZkNEN0dXZTArTTY1MGVoQmpqeTMwTDZQb2tSc0VKNytpbUFtWHU2Sk12ZTZmRVB5ZGFtbS9RbzFUSzNvT2pYSU9HR0c3MVlUUFF1MHEzbitaWGJuY1dlZjZNV2NsclYwM0V0SWhMbCsxOUJ3WUtwa2pFNWpZQ1ZLV0VadnBQeTFuYXFuMWxaWGJnV1VUcmh6TWdISHZGKzYrcVhITTMxR0JUN1IxR1BPYlgvRGpkTjYzak14eTk4RzUvcGJzUU8rbXFqYlljU3V4cDQwYzRMSU45MW55clBHVFVCTkRDaFZMMTRYOW5MVjZQbkhyNWsrUnhzR2psNStmT0kxaUJ1MDFZeUkreWs5a3ZibXFzeHFxUjVycjFHMEZvVW4vOG9vU0JvMS8ybkgrK3BlSnljK0JpcFVhSU4xQTJiMFloMWJtUDFIOCt1TkU1dC9uR0xucFpJY3VqNnR5SDZiTVk2V2pTWG9BbEdCZW5NTXI2cEw5YjROTlNXcUVwdmFHRlJLNkwrd1phNDlvWmUrWGEiLCJtYWMiOiIyYzI5ZTQzY2RjMTU2NGRiZjNmNTdjNzdmMjAzYzYwYTg3MWE2N2IwZTBiZmMyYzBhYTkxZDM5NjBhYWI2YzI5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:22:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkZiL1ZYblgyZ1lKMFViWGdvRER3WEE9PSIsInZhbHVlIjoiZDFCdlY0dWRZaFZzM1c3NG1pZEpud1RtMnZqWFlOeTVYc1BocmVtWjUzQnB0eWxBUklieWxyQmkvcVlReXpLbHZUeWMzS1IwUDdvb1hWRm8vNWZFNmYvZzhmUldMeHQ2T3VhemtDUnh0bXpHOHRzUUNVUDM3WEhWL002MjRVY3A3cW9QdE9jK05UQkF2L0MwRnhrbTY1RDR5TjhYdFp6cVR3cHRDV1d1WFlBWHhQRi80eXB5VUQ5c2h2NlFRcW5PWEU1WU5vS1YwQzVzNys1YS84RjVMMUJlSFVGRC9JVUw3bG5sRGc0ZkVJaHFQMXdRWS8ycmI5SkYvSGZwU3g5SFQwbGtseFArYTZNUWdhQ01oQlFJK2dPSGl3SFhwOXJLSHZEQjhhQXF2ZEFqYUVIditzVHBZaW5yNVlXRk92R2ZBdDFmL2Zpa1NBdWc4TWhVdEQrUGVSVEtaeGdLc1VUQXc3d1gxRGdmVUlrem5tNC9RVm92ZzM2ZTFxRDluUDA4QkRaNXdHUTdPUEgzU3crZzVHZDNvSTJpamYyT1I4MVFlUzg2RHFIdXBqRi9wclAwUHFhRnR2UkRWVWZuandIcFJ4dHJpWFNIcUlNU28xRVpBZTh0MWNYRlYwWndLQWQ2c01taUNEZFpyUXJlM0xDR3pCZS9iL3B2eVJVMnBWSk8iLCJtYWMiOiJjYTg1NTZhYzQxOWRmZGI3NmQxMTEwN2YyMzEyMTUwNjVhNjRlZDZjNzU1YzBiMTUyYzg5Yjc2Yjg4YzNhMTRjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:22:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdzMnRnbzR3U3FFOUFSSkR1amtPeEE9PSIsInZhbHVlIjoiMmw3anltc3RpWStKZ3BHK1AzUUp0enBIaXJPS1d1elV0cjQ4RXNlV3d5dEpYbTMrNys3TzY4ZUNJWU5NbWdmR3piYzF0eXFCbFVDYmJKc1VNVG9NYnlPZlgvOWlwLzlUNU1wYUhJa2pGMW1KZ3BZby9jZkNEN0dXZTArTTY1MGVoQmpqeTMwTDZQb2tSc0VKNytpbUFtWHU2Sk12ZTZmRVB5ZGFtbS9RbzFUSzNvT2pYSU9HR0c3MVlUUFF1MHEzbitaWGJuY1dlZjZNV2NsclYwM0V0SWhMbCsxOUJ3WUtwa2pFNWpZQ1ZLV0VadnBQeTFuYXFuMWxaWGJnV1VUcmh6TWdISHZGKzYrcVhITTMxR0JUN1IxR1BPYlgvRGpkTjYzak14eTk4RzUvcGJzUU8rbXFqYlljU3V4cDQwYzRMSU45MW55clBHVFVCTkRDaFZMMTRYOW5MVjZQbkhyNWsrUnhzR2psNStmT0kxaUJ1MDFZeUkreWs5a3ZibXFzeHFxUjVycjFHMEZvVW4vOG9vU0JvMS8ybkgrK3BlSnljK0JpcFVhSU4xQTJiMFloMWJtUDFIOCt1TkU1dC9uR0xucFpJY3VqNnR5SDZiTVk2V2pTWG9BbEdCZW5NTXI2cEw5YjROTlNXcUVwdmFHRlJLNkwrd1phNDlvWmUrWGEiLCJtYWMiOiIyYzI5ZTQzY2RjMTU2NGRiZjNmNTdjNzdmMjAzYzYwYTg3MWE2N2IwZTBiZmMyYzBhYTkxZDM5NjBhYWI2YzI5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:22:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkZiL1ZYblgyZ1lKMFViWGdvRER3WEE9PSIsInZhbHVlIjoiZDFCdlY0dWRZaFZzM1c3NG1pZEpud1RtMnZqWFlOeTVYc1BocmVtWjUzQnB0eWxBUklieWxyQmkvcVlReXpLbHZUeWMzS1IwUDdvb1hWRm8vNWZFNmYvZzhmUldMeHQ2T3VhemtDUnh0bXpHOHRzUUNVUDM3WEhWL002MjRVY3A3cW9QdE9jK05UQkF2L0MwRnhrbTY1RDR5TjhYdFp6cVR3cHRDV1d1WFlBWHhQRi80eXB5VUQ5c2h2NlFRcW5PWEU1WU5vS1YwQzVzNys1YS84RjVMMUJlSFVGRC9JVUw3bG5sRGc0ZkVJaHFQMXdRWS8ycmI5SkYvSGZwU3g5SFQwbGtseFArYTZNUWdhQ01oQlFJK2dPSGl3SFhwOXJLSHZEQjhhQXF2ZEFqYUVIditzVHBZaW5yNVlXRk92R2ZBdDFmL2Zpa1NBdWc4TWhVdEQrUGVSVEtaeGdLc1VUQXc3d1gxRGdmVUlrem5tNC9RVm92ZzM2ZTFxRDluUDA4QkRaNXdHUTdPUEgzU3crZzVHZDNvSTJpamYyT1I4MVFlUzg2RHFIdXBqRi9wclAwUHFhRnR2UkRWVWZuandIcFJ4dHJpWFNIcUlNU28xRVpBZTh0MWNYRlYwWndLQWQ2c01taUNEZFpyUXJlM0xDR3pCZS9iL3B2eVJVMnBWSk8iLCJtYWMiOiJjYTg1NTZhYzQxOWRmZGI3NmQxMTEwN2YyMzEyMTUwNjVhNjRlZDZjNzU1YzBiMTUyYzg5Yjc2Yjg4YzNhMTRjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:22:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149450489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-195235626 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195235626\", {\"maxDepth\":0})</script>\n"}}