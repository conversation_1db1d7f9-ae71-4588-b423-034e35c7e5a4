{"__meta": {"id": "X27c98ce1e80160909e38dee0c4922644", "datetime": "2025-07-31 11:20:06", "utime": **********.919484, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960805.780191, "end": **********.91953, "duration": 1.1393389701843262, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753960805.780191, "relative_start": 0, "end": **********.768975, "relative_end": **********.768975, "duration": 0.9887840747833252, "duration_str": "989ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.768991, "relative_start": 0.988800048828125, "end": **********.919533, "relative_end": 3.0994415283203125e-06, "duration": 0.1505420207977295, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47344496, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02058, "accumulated_duration_str": "20.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8468602, "duration": 0.01769, "duration_str": "17.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 85.957}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.882745, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 85.957, "width_percent": 6.706}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8917332, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 92.663, "width_percent": 7.337}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-1753029104 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1753029104\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2098383072 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2098383072\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-852362202 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-852362202\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1967891203 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjRma1JnZTJYOE1VZXNUMVBMUDdqb1E9PSIsInZhbHVlIjoiYUNPR3dxWVVoODBsM2ZlVmtVcXZ0RU1tRVhiTEVmSTJJMDRIZExxaW5XZjVOdVVhWkt5c29OdTFXNjVZWVhya0NxWHl4UkxmWmJpQnFRM3Nkd2JvYXZrUFAwUmcvaVRTRVRMSCtueEZSWVdoazE0cUJxdTR5SG5OSmkrSUdTVk1abmROcUp5elNCNEJwRFNKdlduVUgxaHNiNzdqaXBBTUpwL1RmdXhVQS9FbTIrdXNjYjhoL2haNlV1VDkydEkvbzNuZzBrVHRZY2lQSDhlTUxzcWZTWnFJUExEajZXRjlDWGp3dmRNUFRRRDdxNlRxQXNVNWZRcnIwZy9OM1U5TFZjR0xyem45cVRqdEZHMWZQMlNzSytrQW93elptR0dRR0FVSGYvZmlLWmc5S1ZaWWRkWVVhUkthV1Nzdm9pa2FEMi8wdFNlQnkvcWxMLy9oQWdGZi93VDJkdUNXb0k5WitXOExqTVA1cmdGZWxMTFVMd25COXJuRDhkanFvUVBYdVpFTmVFcDNNTVczOG53dDYyRGplWVZiNldPZThnVTRLejFKVlFIYncyYkNpVkxlUVpUWmFrRVJPb0pBQ3dsYzNRTTE5c3lWRTcwWmFWRkZJVGVqbWh6cWowenpCcUxsUlBIcDV2dmVOc0xXVTUzZXFXQnBWSnZOV2lEditVWkMiLCJtYWMiOiJmZWI4Y2NiMDNmODI0ZTk3MjFmZDYyNDEzNmQxNTYxMmU4MmRkNzMxNGU4MDljYjVhM2FlYWY5YWM0NTEzNjk3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkM4anplSGNHUnlIOElhRjBLSDhtN3c9PSIsInZhbHVlIjoiNjd6Y1k0Q1IwcVIrZ2d6U2VWRFNxTEV3cysxZ2krZVhmU0F3d2tqRTlYTUx2NjZUVTMxNFBGQzh5ZFJ3ZHVqUmU5T0oyVHhYbFhrNkxEdUgyK0U1ZzBBOGY5K21ObGYxVmgwM3pJUDYzYjUvdDdkTk5ORWhJa0haVmJHdnM3dDF0Y2dBWFNXV25JcldJRmdsYkdyWWJITlRybG56OVNrQ0cxNXNvQkFSVlJtdUk2TzdyUHlaQjVqajU1NEplM2tzTlhQdkZBLzBidG9SODY2YlgvVjFESTUyQXVNYmRwZ3dBNnk4WlN2bnYxdjFycTFUc1BtK0JrSG5sQ2RpQWNsVXgyeDJwUU9KTlJiVlVxMXc2eXBjM002SW02aUhlQWJNbU9iNVJFUHQ3RFdDOCtUTEZzSFlianZtamo2Y1RmOFpwVzkvZVZVZXRWTWNjOXV6UHJtNUI0b0lVZnpVdW02QVExYlBsN2xiYkFoT2hGQVQvWUhFZFQvNEdKS2JlenBqeWdIc1oxRVBsSUhIWHh6M1E4T0FrUndZT2RBc2VWVkFLOWpteWwvMVhkUVUrRXF5U0RsbEhGV0pQK2dSZlJRaDEzMDlaT2NST1JXRnJXUkR5b2xTajY5eHVLbU5iSjhTM2ZLbEZDaFhNd1ZGMUxTbEh1d2pLUzFpSzVIcFFFdXUiLCJtYWMiOiI3NGNiOWVmOWRhNTc1ZmIxOWZmZDRhZTVlNmIzN2EwYWUxMmNjNGE4ZjdmZWZmNGUwMjg0ZTY1MjI3ZjRmZGUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967891203\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-544288636 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544288636\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-715852230 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:20:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhUU2p2eGFzVjFxWE5FL2xtaU54Q2c9PSIsInZhbHVlIjoiNExMQUg0L2NUZGc1OFRXWVpmdzNTZTAvbnZFYWZVM3JPNFJwSkFMNEZIUHdVREk5RFhPejVURWRpeUgxc2lHaXMyU3JzbXgyb3BNRWNqZEt3cFViVytjU0c3d2dJVTJIclpOVzBGdEY5OUxtNjA1WUszbDR1MlZZclR1cWJHdEFhQXFHRTczaHFKUnJ1Um1BN244YUMvZEh3MHdac2xRZ3lvbmFEUHRxYTAvNFVNeVdRbytXUnovZVptbWxHNDlrNnA2dEdJVmlrQnpTYVFjWjdzYXFPcEJIUlJxcnhpU1o1UkM1d000M2grK3ZlM3BVb3RxSC9SL21HNEtwc0lMQTVsSU91WlhwbEVvZDdKSy9ENWRJbk1ITW4ya1pYY0lqUWQwdjBnalZhTFo1a3JNMURUbmh5TUNHcXZTdW8yVUVGbWIraTVyeXVjWGFsR2E4Vk5RcXZYZlFROWxPcFQvRnlRVmthVTRsT1orQmI0OVFQdXdKYXdBd1lwTzROdFZ2T2IxbEdFVWVXcjVSU3lqeVJwRjN6ZkZieXNXTSt1YllmWjlkT0VKSjkxKzZzNG04V1I4bXY0ZUZNQ29FR1JKZ0FmZHB4KzZsK3ljdDM2MVM0dUNHaS80Vm9tMTVNL2FWN21vVm9XV2dkTGdkRlNPZlZjQWR3MXFtWVA4Y3RvMnkiLCJtYWMiOiIyNDBhNGExMmRiZmFlODA4ZjVmZWU0NDAwMmYwYmVmOGI2NzY1MDY5NjNmZGVhNTUyOWZiOTliYmQzOTgwNmM4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:20:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlRKZ0tiREo4WGJoZ1pYOXZ4bWZ2cnc9PSIsInZhbHVlIjoieVlrNDhMK3pZd1M1NmtRSUxRR29PRmN1Y0E3NURvdmc3eHJJKzF0TS9mTStObzZSQ0V4T3dQcjFLbi9Cd1o3ZEYzaFhmbFZBYkxtWEJNMGJ5VWRQYUtRYjBPSG0wd3JaSkoxR2xBdzhlTmUxbkVpVjBuN2tPeS9VZUxvSTlVVXBpaHVVOWNTU2hlL1Y0YzQ0TzZMeldGMHZ0SnM3c3NJV3Rsd1BvUzJrdXNUdnovbWNVM2U2VUxTZ1JHa3VaakJSWU1FaWR0TVpIMTFVYTdWbkZLQitLL0UvWUw2WWhTRGNseWlVT1JXZng4Qm9SNTlXZXQwMWZRM0lJQTUzb01XWTNGcldRS1pkQlBJcjI0c3dNc3U5MTlheENINVEyTHNTbWpPUWtIdFZJcVVnc3BKN1Vic1VIaFBJanR5ODNHYzFpOUw5VGk4b3F3UTNJNFpoL1p4eHZNOC9Pa3d5amJ6bElidytRNXFOaHZHeWJ5M1lZVmtwMlV5Qmo1ZTBFb1M1NTIxeUdVbXdqSkZjNUowaUFDdHNueDh5Uyt1QWhLWGduaXZRODhkZlREVFlkQ2xWQkthOURyaWhIZXhFNmozSkM4aGlJZUw3N05rUzBiNHovc1BIbVFML0dybHRUWWFJb05QVmVOYU84bTlWamxEbHZoaDhBSWkxT0d5N3NhRnIiLCJtYWMiOiIyNTU3NTZlOWVhZTE4NzE3ZjY4YmM0Njk4NzYyNGQ0NmI0ODNlMWU1ZThhOWYyODljOTQyZDRkMzk4YTgyZmU1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:20:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhUU2p2eGFzVjFxWE5FL2xtaU54Q2c9PSIsInZhbHVlIjoiNExMQUg0L2NUZGc1OFRXWVpmdzNTZTAvbnZFYWZVM3JPNFJwSkFMNEZIUHdVREk5RFhPejVURWRpeUgxc2lHaXMyU3JzbXgyb3BNRWNqZEt3cFViVytjU0c3d2dJVTJIclpOVzBGdEY5OUxtNjA1WUszbDR1MlZZclR1cWJHdEFhQXFHRTczaHFKUnJ1Um1BN244YUMvZEh3MHdac2xRZ3lvbmFEUHRxYTAvNFVNeVdRbytXUnovZVptbWxHNDlrNnA2dEdJVmlrQnpTYVFjWjdzYXFPcEJIUlJxcnhpU1o1UkM1d000M2grK3ZlM3BVb3RxSC9SL21HNEtwc0lMQTVsSU91WlhwbEVvZDdKSy9ENWRJbk1ITW4ya1pYY0lqUWQwdjBnalZhTFo1a3JNMURUbmh5TUNHcXZTdW8yVUVGbWIraTVyeXVjWGFsR2E4Vk5RcXZYZlFROWxPcFQvRnlRVmthVTRsT1orQmI0OVFQdXdKYXdBd1lwTzROdFZ2T2IxbEdFVWVXcjVSU3lqeVJwRjN6ZkZieXNXTSt1YllmWjlkT0VKSjkxKzZzNG04V1I4bXY0ZUZNQ29FR1JKZ0FmZHB4KzZsK3ljdDM2MVM0dUNHaS80Vm9tMTVNL2FWN21vVm9XV2dkTGdkRlNPZlZjQWR3MXFtWVA4Y3RvMnkiLCJtYWMiOiIyNDBhNGExMmRiZmFlODA4ZjVmZWU0NDAwMmYwYmVmOGI2NzY1MDY5NjNmZGVhNTUyOWZiOTliYmQzOTgwNmM4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:20:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlRKZ0tiREo4WGJoZ1pYOXZ4bWZ2cnc9PSIsInZhbHVlIjoieVlrNDhMK3pZd1M1NmtRSUxRR29PRmN1Y0E3NURvdmc3eHJJKzF0TS9mTStObzZSQ0V4T3dQcjFLbi9Cd1o3ZEYzaFhmbFZBYkxtWEJNMGJ5VWRQYUtRYjBPSG0wd3JaSkoxR2xBdzhlTmUxbkVpVjBuN2tPeS9VZUxvSTlVVXBpaHVVOWNTU2hlL1Y0YzQ0TzZMeldGMHZ0SnM3c3NJV3Rsd1BvUzJrdXNUdnovbWNVM2U2VUxTZ1JHa3VaakJSWU1FaWR0TVpIMTFVYTdWbkZLQitLL0UvWUw2WWhTRGNseWlVT1JXZng4Qm9SNTlXZXQwMWZRM0lJQTUzb01XWTNGcldRS1pkQlBJcjI0c3dNc3U5MTlheENINVEyTHNTbWpPUWtIdFZJcVVnc3BKN1Vic1VIaFBJanR5ODNHYzFpOUw5VGk4b3F3UTNJNFpoL1p4eHZNOC9Pa3d5amJ6bElidytRNXFOaHZHeWJ5M1lZVmtwMlV5Qmo1ZTBFb1M1NTIxeUdVbXdqSkZjNUowaUFDdHNueDh5Uyt1QWhLWGduaXZRODhkZlREVFlkQ2xWQkthOURyaWhIZXhFNmozSkM4aGlJZUw3N05rUzBiNHovc1BIbVFML0dybHRUWWFJb05QVmVOYU84bTlWamxEbHZoaDhBSWkxT0d5N3NhRnIiLCJtYWMiOiIyNTU3NTZlOWVhZTE4NzE3ZjY4YmM0Njk4NzYyNGQ0NmI0ODNlMWU1ZThhOWYyODljOTQyZDRkMzk4YTgyZmU1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:20:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715852230\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-478512221 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478512221\", {\"maxDepth\":0})</script>\n"}}