{"__meta": {"id": "X96b2ac75dc243015c37ad5439b094fe8", "datetime": "2025-07-31 12:06:33", "utime": **********.974419, "method": "GET", "uri": "/invoice/contact-details?contact_id=lead_12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963592.58506, "end": **********.974455, "duration": 1.3893952369689941, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1753963592.58506, "relative_start": 0, "end": **********.772979, "relative_end": **********.772979, "duration": 1.1879191398620605, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.773005, "relative_start": 1.1879451274871826, "end": **********.974458, "relative_end": 2.86102294921875e-06, "duration": 0.20145297050476074, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46309136, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET invoice/contact-details", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\InvoiceController@getContactDetails", "namespace": null, "prefix": "", "where": [], "as": "invoice.contact.details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1593\" onclick=\"\">app/Http/Controllers/InvoiceController.php:1593-1651</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02528, "accumulated_duration_str": "25.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.877864, "duration": 0.022350000000000002, "duration_str": "22.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 88.41}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9309402, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 88.41, "width_percent": 6.804}, {"sql": "select `id`, `name`, `email`, `phone` as `contact` from `leads` where `id` = '12' and `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["12", "79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/InvoiceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\InvoiceController.php", "line": 1624}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.942306, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "InvoiceController.php:1624", "source": "app/Http/Controllers/InvoiceController.php:1624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FInvoiceController.php&line=1624", "ajax": false, "filename": "InvoiceController.php", "line": "1624"}, "connection": "radhe_same", "start_percent": 95.214, "width_percent": 4.786}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/invoice/contact-details", "status_code": "<pre class=sf-dump id=sf-dump-1587745410 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1587745410\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1542159017 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>contact_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">lead_12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542159017\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1723303915 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1723303915\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319500466 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpBQU95Z2N4NmhFM0l6b3R4T0IxbUE9PSIsInZhbHVlIjoib3VIZTRIYTMzenFkUkpCSlhua2NzNWRqa2t1NUZ2YXgzTHFJSTVHTkcwMDlrS2NpRE1hODdIZFMrMWg5ZmpIbWxxZFBteEJiUzlvWm5RSXd4bG5FTmRhWU1ycjIzdlJaTTV0TDJoN2NYS09UVlBOTGhNWmhNbExoNWRxU2pxMWs2MnYySmpTZkZuVllnYXM3djgrR2ZpMFNPb2p3cHpoWWJMVnFFUSt0THgwSkZ5YnlIQXBDUTF0U2ZRTzJBaWJlNmpiNVRDS0lYVktKSm5KRDZsZUpjL29Mell6NVhFNERZenhRZXkrTkl2Y0N6eWhCSklmTDNBZ0JIN1JueEF6NU5NaHFkZDJjc0ZBU0dvejZwUTc4ajJ1MTVUTWFpTjNJTVBHV0JoNjRxcWxQaVZmYmpRV3psRkloanpLaVVlSnlxS3laU1orSTExTXFwOXlJNU9BbHlqOU01SWtGc0JtSUFuYVZrVVhRT2hhUnpJdTkrUWtlR1dFR1RCRytUSTZJeUt2QUJoMW1ZeGNkd1VHdkhOeVJoOU1ubi9BOGY4Q0pxcm5HeWF5TTNrMENXdXRzQ29IclExSnhTTDZHbzVUZ0sxREZPVnNWNS9Cak1peVVTNWg0RSt3bFhiOXpKNE5lUFlFN25zNFpWb0ViaXhKS1dqbThkalpIY0ZSdmdlc3YiLCJtYWMiOiIwZTUzOTNmNDdjMTkzZTFiOGQwNzQxZmRhOTUwYjU2N2Q2MDJlZDY4MzYyMzJiOGU1ODA5MTdiZTgxOGU1ZTcxIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IkZraHQyckEwK3AyNVd4YWFUUmRzamc9PSIsInZhbHVlIjoiK3hTVFA2ZVRJZHR2WFRidzRPQm4yclNQV1VkTGhtcjIzOVh4TnpXQUMrTXdNRXZuRmJXVUxSWFA4U2UyUVlCV2t5ZEtUS2VmNzZ1aUtpYzBHclljV05ZYlprR0hmdmc0dFhMZHpkMnJHRGpVNkFxZnphUDhPODVpSGRCdXlmTkZUWmY0dlZsampTWkhIOERaODNZd0paaGZ6b0pKOGlsQVp1RmZWWGg4TU1iN25MMk9rbFc2MFJYM01qd0dKeVk3azg3RytMTDhNQkhXTXZpRU9nSzd1NnVTR2xxTTBPSmhKdGZoaWNQQlV5OFgyakswVi8zV2ZTUlo1TFh0VXpyYnI0TURscVFrNUpWK1B4Qjd3d0d3SytuR0dwV1JjU2lGdEN1b2E5T1AzSHRVeDJ4bE1YOHhjN09DV0RIT2trVHRpRStucFRkSXF2OGliaktXUnFMbEZQRDZ1cTJpbVZ4WkQxTXVITmlDN2dnQVhWNFRKMlEyd3MzTDZvU2ExMnRSU3M0ejlxT3YvUUdHcVMvZHRwc2tCcmpQcVdjZmFiMkxwKzZHd2JjdUx0WWJIczllL1ZXbFpDcUlSNWFwSTVOSFJsQUkyVFg4dW80V2VDNWhxY3RKMGZoaFd1c25VMXh4eHBFcDhGZlEzaEw0NndZUkhscS9xTC9PRk9HTUY5bXMiLCJtYWMiOiI4YzE4ZGQyOTUwM2E1Mjk4ODFkNTA3ZjA1ZGRlMWYwZmI0NDY4YzUzMjMxYjViMjY1YTkxMmRjMzQwMDgzZTNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319500466\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-932386730 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932386730\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1304827152 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:06:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks2bDJjdXJ4YTJEK2xYbWh3a0d1V2c9PSIsInZhbHVlIjoiVHgrdERjalU0NGJjZ2FqejBNejBHYVNxYkhlcDBJQkJQZHlVS1YzT1RPUWExMnNzM2MySGFKRWk4anBrWnlQOEVYcytLMnhMdFRZSTEvK2JIM2VacUhsakpuK05JbmZJUmtPVjA1eCtmRWt2MHdBNWphaFlPZ3JIZDV6SEx1RWNuYktCK3lTRlN2RXlWdll0elNkS2xQYlZhajRsUjJpbWdES0VBZngxUEJ4V3U1QXMzTkF4UWxZRDY2S3hSWGwvdmhrdXhGTFY1OXZJTkFFWlJQSXEyWmtLY2J1Y3ZWRFJaZVJrdE5ZYk9oc1dsbVVLVENPdUNrU2I3Q2hqWTFRWFNKTmc3NzV0Z1VzM1JLRURZM0hjeFY5WVBzWkRuUE5zMURWb0xhZisxM3Y5TXJSWmVDVzhTZkx5eGVIeUU3TUU4S3FYMnFlelRzeWVqcll2aEpYRFlyVTE5ZVYrR2JKNGV5b29ZMnNMaEs3OEJzRXVHYS9XRTNjaFNQaHF1SmloaVlLVHFjQkl5UWt6d3k4WGpiVnBQVmE2bTgzeW42LzB6MEJqbGZyWXBCY3pXbzJBMlM2dGNKdk01cW82Yk41cVgzQjVySjlya0NuOGFhd09reGRUeFVKRE1RY2V1VVYwNWN5MUlZb1NpUEF3T3phRkZZMUVjNG9jZGxkNGtrSUkiLCJtYWMiOiIwNmU0ODY2ODM3MDNlNWVlNzY5MDY5NzJlOGI2ZDY3YWQxYzM5NmJiZWQ3Y2M5ZmU5NWRmMzg3NmI1N2UyNTZmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:06:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlhhNmxVeGdNZ2diSUxrQWJVbUNzU0E9PSIsInZhbHVlIjoiYVVDd0Y5NkdreUNhL2hPSEVmZUswU1JWT1VyR1h6dTVtemdYWW91eW96WVFXYVpvMjhkYmRVYndPY0tpWGxTMXpYZmNwU2hxTUwzKzd0cDB2bnNKcGVuQU8vUUhxWTRuYnZ6VE5PdGJGQVpCT1g2emk0emo4eGdKV0VJbWh3eENJWkxoVGlvZDNzV1BrcFhDNy9pOG1xQU1SRlZJTW1yWU9vaVprSWZKL244RWJPTXNvWWVYT0p2MWxWaDEvVDFjUVdHTUNuUndpeWZ0dWE5T3psOGN0alNwdVpCZzQ0aWxjNWRWMk1UVWNuTm5IbXpsdkZpd1VhbVM0WlFrMVZXRGpkWmd1cU1xQTdCemoyMll2bUFac3BsdE15dUR5STd6b3VwKzBpY2RET3JXY2xYeXdhbzhMV29KUnA2Vy9icHR2WmhPWlpFZjNyVXNTS1FEVVR6QXpwQ0lIUEFmWFRnVzBzV3VrS2V2eXhZTS9PVmdqWG9BemdYMldsTk9uVzZtb0VoUVVTTW5PUFZnZTFyUnB6WTdXbkM1VUxrRStUOWdtOWRWcWhBekJlOUhKLzhYdC94TzdGQUYwSkpqVzFJN2lGajJUMHNiOTJRMDMxcGFqSGxTZ0E4VU15Q3JnVlNBd042MTk1TEFNWmlrYVBOQU1WTHF1cjlYS1R4UEJQWU4iLCJtYWMiOiIzMjUwM2Y1ODYyMjZhZDAwZGY5ZDU3ODAxYzZlYzAzYzc3OGI1YmU4NzdkMzU1ZTdlZDRhNzM4MWE5NTg0OWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:06:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks2bDJjdXJ4YTJEK2xYbWh3a0d1V2c9PSIsInZhbHVlIjoiVHgrdERjalU0NGJjZ2FqejBNejBHYVNxYkhlcDBJQkJQZHlVS1YzT1RPUWExMnNzM2MySGFKRWk4anBrWnlQOEVYcytLMnhMdFRZSTEvK2JIM2VacUhsakpuK05JbmZJUmtPVjA1eCtmRWt2MHdBNWphaFlPZ3JIZDV6SEx1RWNuYktCK3lTRlN2RXlWdll0elNkS2xQYlZhajRsUjJpbWdES0VBZngxUEJ4V3U1QXMzTkF4UWxZRDY2S3hSWGwvdmhrdXhGTFY1OXZJTkFFWlJQSXEyWmtLY2J1Y3ZWRFJaZVJrdE5ZYk9oc1dsbVVLVENPdUNrU2I3Q2hqWTFRWFNKTmc3NzV0Z1VzM1JLRURZM0hjeFY5WVBzWkRuUE5zMURWb0xhZisxM3Y5TXJSWmVDVzhTZkx5eGVIeUU3TUU4S3FYMnFlelRzeWVqcll2aEpYRFlyVTE5ZVYrR2JKNGV5b29ZMnNMaEs3OEJzRXVHYS9XRTNjaFNQaHF1SmloaVlLVHFjQkl5UWt6d3k4WGpiVnBQVmE2bTgzeW42LzB6MEJqbGZyWXBCY3pXbzJBMlM2dGNKdk01cW82Yk41cVgzQjVySjlya0NuOGFhd09reGRUeFVKRE1RY2V1VVYwNWN5MUlZb1NpUEF3T3phRkZZMUVjNG9jZGxkNGtrSUkiLCJtYWMiOiIwNmU0ODY2ODM3MDNlNWVlNzY5MDY5NzJlOGI2ZDY3YWQxYzM5NmJiZWQ3Y2M5ZmU5NWRmMzg3NmI1N2UyNTZmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:06:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlhhNmxVeGdNZ2diSUxrQWJVbUNzU0E9PSIsInZhbHVlIjoiYVVDd0Y5NkdreUNhL2hPSEVmZUswU1JWT1VyR1h6dTVtemdYWW91eW96WVFXYVpvMjhkYmRVYndPY0tpWGxTMXpYZmNwU2hxTUwzKzd0cDB2bnNKcGVuQU8vUUhxWTRuYnZ6VE5PdGJGQVpCT1g2emk0emo4eGdKV0VJbWh3eENJWkxoVGlvZDNzV1BrcFhDNy9pOG1xQU1SRlZJTW1yWU9vaVprSWZKL244RWJPTXNvWWVYT0p2MWxWaDEvVDFjUVdHTUNuUndpeWZ0dWE5T3psOGN0alNwdVpCZzQ0aWxjNWRWMk1UVWNuTm5IbXpsdkZpd1VhbVM0WlFrMVZXRGpkWmd1cU1xQTdCemoyMll2bUFac3BsdE15dUR5STd6b3VwKzBpY2RET3JXY2xYeXdhbzhMV29KUnA2Vy9icHR2WmhPWlpFZjNyVXNTS1FEVVR6QXpwQ0lIUEFmWFRnVzBzV3VrS2V2eXhZTS9PVmdqWG9BemdYMldsTk9uVzZtb0VoUVVTTW5PUFZnZTFyUnB6WTdXbkM1VUxrRStUOWdtOWRWcWhBekJlOUhKLzhYdC94TzdGQUYwSkpqVzFJN2lGajJUMHNiOTJRMDMxcGFqSGxTZ0E4VU15Q3JnVlNBd042MTk1TEFNWmlrYVBOQU1WTHF1cjlYS1R4UEJQWU4iLCJtYWMiOiIzMjUwM2Y1ODYyMjZhZDAwZGY5ZDU3ODAxYzZlYzAzYzc3OGI1YmU4NzdkMzU1ZTdlZDRhNzM4MWE5NTg0OWMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:06:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304827152\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1180733377 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180733377\", {\"maxDepth\":0})</script>\n"}}