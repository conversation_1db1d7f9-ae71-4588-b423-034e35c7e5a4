<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductShippingField extends Model
{
    protected $fillable = [
        'product_id',
        'is_required',
    ];

    protected $casts = [
        'product_id' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the product that owns this shipping field
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
