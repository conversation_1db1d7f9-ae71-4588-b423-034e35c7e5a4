<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<!-- Plan Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?php echo e(__('Plan Management')); ?></h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active" data-plan-view="products">
                    <i class="ti ti-box me-1"></i><?php echo e(__('Products')); ?>

                </button>
                <button type="button" class="btn btn-outline-primary" data-plan-view="coupons">
                    <i class="ti ti-ticket me-1"></i><?php echo e(__('Coupons')); ?>

                </button>
                <button type="button" class="btn btn-outline-primary" data-plan-view="links">
                    <i class="ti ti-link me-1"></i><?php echo e(__('Links')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Products View -->
<div id="products-view" class="plan-view active">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Products Table')); ?></h5>
                    <div class="d-flex gap-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create product & service')): ?>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                <i class="ti ti-plus me-1"></i><?php echo e(__('Add Product')); ?>

                            </button>
                        <?php endif; ?>
                        <button class="btn btn-primary btn-sm">
                            <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

                        </button>
                        <div class="dropdown">
                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ti ti-settings me-1"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i><?php echo e(__('Refresh')); ?></a></li>
                                <li><a class="dropdown-item" href="#"><i class="ti ti-filter me-2"></i><?php echo e(__('Filter')); ?></a></li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <?php echo e(__('10')); ?>

                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">10</a></li>
                                <li><a class="dropdown-item" href="#">25</a></li>
                                <li><a class="dropdown-item" href="#">50</a></li>
                                <li><a class="dropdown-item" href="#">100</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0 text-center ">
                            <thead class="table-light">
                                <tr>
                                    <th><?php echo e(__('Name')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Image')); ?></th>
                                    <th><?php echo e(__('Price')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Down Payment')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Tax')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Total Sales')); ?> <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $products = \App\Models\Product::with(['taxSlab', 'emiOptions', 'shippingFields', 'bumpOffers'])->limit(5)->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="<?php echo e($product->name); ?>" readonly>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <?php if($product->product_image): ?>
                                                <img src="<?php echo e(asset('storage/products/' . $product->product_image)); ?>" alt="<?php echo e($product->name); ?>" class="img-fluid rounded" style="width: 40px; height: 40px; object-fit: cover;">
                                            <?php else: ?>
                                                <i class="ti ti-package text-muted"></i>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="$<?php echo e(number_format($product->product_price, 2)); ?>" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="<?php echo e(number_format($product->downpayment, 2)); ?>" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="<?php echo e($product->taxSlab ? $product->taxSlab->percentage . '%' : '0.00%'); ?>" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="0" readonly>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" <?php echo e($product->is_active ? 'checked' : ''); ?>>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-primary" title="<?php echo e(__('View')); ?>">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" title="<?php echo e(__('Edit')); ?>">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="<?php echo e(__('Duplicate')); ?>">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="<?php echo e(__('Share')); ?>">
                                                <i class="ti ti-share"></i>
                                            </button>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#"><i class="ti ti-edit me-2"></i><?php echo e(__('Edit')); ?></a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="ti ti-copy me-2"></i><?php echo e(__('Duplicate')); ?></a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#"><i class="ti ti-trash me-2"></i><?php echo e(__('Delete')); ?></a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-package" style="font-size: 3rem;"></i>
                                            <p class="mt-2"><?php echo e(__('No products found')); ?></p>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create product & service')): ?>
                                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add First Product')); ?>

                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Coupons View -->
<div id="coupons-view" class="plan-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Coupons Management')); ?></h5>
                    <div class="d-flex gap-2">
                        
                        
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Coupon')); ?>

                            </button>
                        
                        <button class="btn btn-primary btn-sm">
                            <i class="ti ti-download me-1"></i><?php echo e(__('Export')); ?>

                        </button>
                        <div class="dropdown">
                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ti ti-settings me-1"></i>
                            </button>
                            <!-- <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i><?php echo e(__('Refresh')); ?></a></li>
                                <li><a class="dropdown-item" href="#"><i class="ti ti-filter me-2"></i><?php echo e(__('Filter')); ?></a></li>
                            </ul> -->
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Name')); ?></th>
                                    <th><?php echo e(__('Code')); ?></th>
                                    <th><?php echo e(__('Product')); ?></th>
                                    <th><?php echo e(__('Start`s From')); ?></th>
                                    <th><?php echo e(__('End`s at')); ?></th>
                                    <th><?php echo e(__('Amount')); ?></th>
                                    <th><?php echo e(__('Status')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    try {
                                        $coupons = \App\Models\Coupon::where('created_by', \Auth::user()->creatorId())->limit(5)->get();
                                    } catch (\Exception $e) {
                                        $coupons = collect([]);
                                    }
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $coupons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coupon): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($coupon->name); ?></td>
                                    <td><code><?php echo e($coupon->code); ?></code></td>
                                    <td><?php echo e($coupon->product_name ?? __('All Products')); ?></td>
                                    <td><?php echo e($coupon->start_date ? \Carbon\Carbon::parse($coupon->start_date)->format('M d, Y') : __('No Start Date')); ?></td>
                                    <td><?php echo e($coupon->end_date ? \Carbon\Carbon::parse($coupon->end_date)->format('M d, Y') : __('No End Date')); ?></td>
                                    <td><?php echo e($coupon->discount); ?><?php echo e(($coupon->discount_type ?? 'percentage') == 'percentage' ? '%' : \Auth::user()->currencySymbol()); ?></td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input coupon-status-toggle" type="checkbox"
                                                   data-coupon-id="<?php echo e($coupon->id); ?>"
                                                   <?php echo e($coupon->is_active ? 'checked' : ''); ?>>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-primary view-coupon" data-coupon-id="<?php echo e($coupon->id); ?>" title="<?php echo e(__('View')); ?>">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary edit-coupon" data-coupon-id="<?php echo e($coupon->id); ?>" title="<?php echo e(__('Edit')); ?>">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-coupon" data-coupon-id="<?php echo e($coupon->id); ?>" title="<?php echo e(__('Delete')); ?>">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-ticket" style="font-size: 3rem;"></i>
                                            <p class="mt-2"><?php echo e(__('No coupons found')); ?></p>
                                            
                                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                                    <i class="ti ti-plus me-1"></i><?php echo e(__('Add First Coupon')); ?>

                                                </button>
                                            
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Links View -->
<div id="links-view" class="plan-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('Payment Links')); ?></h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Create Link')); ?>

                    </button>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="ti ti-link text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3"><?php echo e(__('Payment Links')); ?></h5>
                        <p class="text-muted"><?php echo e(__('Create shareable payment links for quick transactions')); ?></p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                            <i class="ti ti-plus me-1"></i><?php echo e(__('Create Your First Link')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Plan view switching
    const planViewButtons = document.querySelectorAll('[data-plan-view]');
    const planViews = document.querySelectorAll('.plan-view');
    
    planViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-plan-view');
            
            // Remove active class from all buttons
            planViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all views
            planViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });
});
</script>

<style>
/* #addProductModal .toolbar button {
    border: 1px solid #dee2e6;
    background: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

#addProductModal .toolbar button:hover {
    background: #e9ecef;
}

#addProductModal .form-select-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

#addProductModal #imagePlaceholder {
    border: 2px dashed #dee2e6;
    background: #f8f9fa;
}

#addProductModal .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#addProductModal .modal-xl {
    max-width: 1200px;
}

@media (max-width: 1200px) {
    #addProductModal .modal-xl {
        max-width: 95%;
    }
}

#addCouponModal .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#addCouponModal .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#addCouponModal .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
} */
 :root {
    --primary-gradient: linear-gradient(135deg, #38ef7d 0%, #11998e 100%);
    --secondary-gradient: linear-gradient(135deg, #a8e063 0%, #56ab2f 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --glass-bg: rgba(240, 255, 240, 0.1); 
    --glass-border: rgba(144, 238, 144, 0.2);
    --shadow-lg: 0 20px 40px rgba(0, 128, 0, 0.1);
    --shadow-hover: 0 8px 25px rgba(0, 128, 0, 0.15);
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.modal-backdrop {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.3);
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1.5rem 2rem;
    position: relative;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="25" cy="75" r="1" fill="white" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.modal-title {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-title::before {
    font-size: 1.5rem;
}

.modal-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

.form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-label i {
    color: #667eea;
    font-size: 0.9rem;
}

.form-control, .form-select {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: white;
    transform: translateY(-1px);
}

.form-check {
    margin-bottom: 1rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #cbd5e0;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background: var(--success-gradient);
    border-color: transparent;
    transform: scale(1.1);
}

.form-check-label {
    font-weight: 500;
    color: #4a5568;
    margin-left: 0.5rem;
}

.section-card {
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.section-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.section-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bump-offer-item {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 2px dashed #6fd943;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
    transition: all 0.3s ease;
}

.bump-offer-item:hover {
    border-style: solid;
    transform: scale(1.02);
}

.btn-modern {
    border: none;
    font-size: 14px;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-success-modern {
    background: var(--success-gradient);
    color: white;
}

.btn-success-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline-modern {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #6fd943;
    color: #6fd943;
}

.btn-outline-modern:hover {
    background: #6fd943;
    color: white;
    transform: translateY(-2px);
}

.image-upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: #6fd943;
    background: rgba(102, 126, 234, 0.05);
}

.image-upload-area.dragover {
    border-color: #6fd943;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.toolbar {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px 12px 0 0;
    padding: 1rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.toolbar .btn {
    border-radius: 8px;
    margin-right: 0.25rem;
    transition: all 0.3s ease;
}

.modal-footer {
    background: rgba(248, 250, 252, 0.8);
    border: none;
    padding: 1.5rem 2rem;
    gap: 1rem;
}

.text-danger {
    color: #f56565 !important;
}

.remove-bump-offer {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: #f56565;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.bump-offer-item:hover .remove-bump-offer {
    opacity: 1;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: slideInUp 0.3s ease;
}

@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .section-card {
        padding: 1rem;
    }
}
</style>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">Add Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addProductForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <!-- Basic Information -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-info-circle"></i>
                            Basic Information
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="productName" class="form-label">
                                    <i class="fas fa-tag"></i>
                                    Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="productName" name="name" placeholder="Enter product name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="productNickname" class="form-label">
                                    <i class="fas fa-user-tag"></i>
                                    Nickname (Internal use only)
                                </label>
                                <input type="text" class="form-control" id="productNickname" name="nickname" placeholder="Internal nickname">
                            </div>
                        </div>
                    </div>

                    <!-- Product Settings -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-cogs"></i>
                            Product Settings
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="restrictOneTime" name="restrict_one_time">
                                    <label class="form-check-label" for="restrictOneTime">
                                        Restrict to one time purchase
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isFreeTrial" name="is_free_trial" onchange="handleFreeTrialChange(this)">
                                    <label class="form-check-label" for="isFreeTrial">
                                        Is it a free trial?
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isSubscription" name="is_subscription" onchange="handleSubscriptionChange(this)">
                                    <label class="form-check-label" for="isSubscription">
                                        Is it a subscription?
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trial Fields -->
                    <div id="trialFields" class="section-card fade-in" style="display: none;">
                        <div class="section-title">
                            <i class="fas fa-clock"></i>
                            Free Trial Settings
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="trialDurationType" class="form-label">Duration Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="trialDurationType" name="trial_duration_type">
                                    <option value="">Select duration type</option>
                                    <option value="day">Days</option>
                                    <option value="month">Months</option>
                                    <option value="year">Years</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="trialDuration" class="form-label">Duration <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="trialDuration" name="trial_duration" placeholder="Enter duration">
                                <small class="text-muted">Number of days, months, or years</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="trialPrice" class="form-label">Trial Price <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control" id="trialPrice" name="trial_price" value="0">
                            </div>
                        </div>
                    </div>

                    <!-- Subscription Fields -->
                    <div id="subscriptionFields" class="section-card fade-in" style="display: none;">
                        <div class="section-title">
                            <i class="fas fa-sync-alt"></i>
                            Subscription Settings
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isCancellable" name="is_cancellable">
                                    <label class="form-check-label" for="isCancellable">
                                        Allow cancellation
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6"></div>
                            <div class="col-md-4 mb-3">
                                <label for="billedEvery" class="form-label">Billed Every <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="billedEvery" name="billed_every" value="1" min="1">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="billingCycleType" class="form-label">Billing Cycle <span class="text-danger">*</span></label>
                                <select class="form-select" id="billingCycleType" name="billing_cycle_type">
                                    <option value="forever">Forever</option>
                                    <option value="limited">Limited</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="billingCycle" class="form-label">Display Text</label>
                                <input type="text" class="form-control" id="billingCycle" name="billing_cycle_display" value="Forever" readonly>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-dollar-sign"></i>
                            Pricing & Tax
                        </div>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="productPrice" class="form-label">
                                    <i class="fas fa-money-bill"></i>
                                    Price <span class="text-danger">*</span>
                                </label>
                                <input type="number" step="0.01" class="form-control" id="productPrice" name="price" placeholder="0.00" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="strikedPrice" class="form-label">
                                    <i class="fas fa-strikethrough"></i>
                                    Striked Price
                                </label>
                                <input type="number" step="0.01" class="form-control" id="strikedPrice" name="striked_price" placeholder="0.00">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="taxSlab" class="form-label">
                                    <i class="fas fa-receipt"></i>
                                    Tax Slab
                                </label>
                                <select class="form-select" id="taxSlab" name="tax_slab">
                                    <option value="">Select Tax Slab</option>
                                    <?php
                                        try {
                                            $taxSlabs = \App\Models\TaxSlab::all();
                                        } catch (\Exception $e) {
                                            $taxSlabs = collect([]);
                                        }
                                    ?>
                                    <?php $__currentLoopData = $taxSlabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $taxSlab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($taxSlab->id); ?>"><?php echo e($taxSlab->label); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="taxRate" class="form-label">
                                    <i class="fas fa-percentage"></i>
                                    Tax Rate
                                </label>
                                <input type="number" step="0.01" class="form-control" id="taxRate" name="tax_rate" placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Additional Options -->
                    <div class="section-card">
                    <div class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        Additional Options
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emiOptions" name="emi_options" onchange="handleEmiChange(this)">
                                <label class="form-check-label" for="emiOptions">
                                    EMI Options Available
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showShippingField" name="show_shipping_field" onchange="handleShippingChange(this)">
                                <label class="form-check-label" for="showShippingField">
                                    Show shipping field
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="paymentGateway" name="payment_gateway" onchange="handlePaymentGatewayChange(this)">
                                <label class="form-check-label" for="paymentGateway">
                                    Enable Payment Gateway
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- EMI Options Fields -->
                    <div id="emiFields" class="fade-in" style="display: none;">
                        <div class="row border-top pt-3">
                            <div class="col-md-6 mb-3">
                                <label for="emiOptionsInput" class="form-label">
                                    <i class="fas fa-credit-card"></i>
                                    EMI Options <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="emiOptionsInput" name="emi_options_value[]" multiple>
                                    <option value="1">1 Month</option>
                                    <option value="2">2 Months</option>
                                    <option value="3">3 Months</option>
                                    <option value="4">4 Months</option>
                                    <option value="5">5 Months</option>
                                    <option value="6">6 Months</option>
                                    <option value="7">7 Months</option>
                                    <option value="8">8 Months</option>
                                    <option value="9">9 Months</option>
                                    <option value="10">10 Months</option>
                                    <option value="11">11 Months</option>
                                    <option value="12">12 Months</option>
                                    <option value="13">13 Months</option>
                                    <option value="14">14 Months</option>
                                    <option value="15">15 Months</option>
                                </select>
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple options</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="downPayment" class="form-label">
                                    <i class="fas fa-money-bill-wave"></i>
                                    Down Payment
                                </label>
                                <input type="number" class="form-control" id="downPayment" name="down_payment" placeholder="0.00">
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Fields -->
                    <div id="shippingFields" class="fade-in" style="display: none;">
                        <div class="row border-top pt-3">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requiredShippingField" name="required_shipping_field">
                                    <label class="form-check-label" for="requiredShippingField">
                                        <i class="fas fa-shipping-fast me-2"></i>
                                        Required Shipping Field
                                    </label>
                                </div>
                                <small class="text-muted">Make shipping address mandatory for checkout</small>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Gateway Fields -->
                    <div id="paymentGatewayFields" class="fade-in" style="display: none;">
                        <div class="row border-top pt-3">
                            <div class="col-12 mb-3">
                                <label for="paymentGatewayLink" class="form-label">
                                    <i class="fas fa-link"></i>
                                    Payment Gateway
                                </label>
                                <select class="form-select" id="paymentGatewayLink" name="payment_gateway_link">
                                    <option value="">Select Payment Gateway</option>
                                    <optgroup label="Popular Gateways">
                                        <option value="razorpay">Razorpay</option>
                                        <option value="stripe">Stripe</option>
                                        <option value="payu">PayU</option>
                                        <option value="paypal">PayPal</option>
                                        <option value="instamojo">Instamojo</option>
                                    </optgroup>
                                    <optgroup label="Indian Gateways">
                                        <option value="ccavenue">CCAvenue</option>
                                        <option value="billdesk">BillDesk</option>
                                        <option value="atom">Atom Payment</option>
                                        <option value="paytm">Paytm</option>
                                        <option value="phonepe">PhonePe</option>
                                    </optgroup>
                                    <optgroup label="International">
                                        <option value="square">Square</option>
                                        <option value="authorize">Authorize.Net</option>
                                        <option value="braintree">Braintree</option>
                                        <option value="worldpay">Worldpay</option>
                                        <option value="adyen">Adyen</option>
                                    </optgroup>
                                    <optgroup label="Crypto">
                                        <option value="coinbase">Coinbase Commerce</option>
                                        <option value="bitpay">BitPay</option>
                                        <option value="coingate">CoinGate</option>
                                    </optgroup>
                                    <option value="custom">Custom Gateway</option>
                                </select>
                            </div>
                            <div class="col-12 mb-3" id="customGatewayField" style="display: none;">
                                    <label for="customGatewayUrl" class="form-label">
                                        <i class="fas fa-globe"></i>
                                        Custom Gateway URL
                                    </label>
                                    <input type="url" class="form-control" id="customGatewayUrl" name="custom_gateway_url" placeholder="https://your-payment-gateway.com/api">
                                    <small class="text-muted">Enter your custom payment gateway API endpoint</small>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Bump Offers -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-rocket"></i>
                            Bump Offers
                        </div>
                        <div id="bumpOffersContainer">
                            <!-- Bump offers will be added here -->
                        </div>
                        <button type="button" class="btn btn-success-modern btn-modern" id="addBumpOfferBtn">
                            <i class="fas fa-plus me-2"></i>Add Bump Offer
                        </button>
                    </div>

                    <!-- Product Image -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-image"></i>
                            Product Image
                        </div>
                        <!-- Hidden file input outside the upload area to prevent infinite loop -->
                        <input type="file" class="d-none" id="productImage" name="pro_image" accept="image/*">
                        <div class="image-upload-area" id="imageUploadArea">
                            <div id="imagePlaceholder">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted mb-2">Click to upload or drag and drop</p>
                                <small class="text-muted">Supported: JPG, JPEG, PNG</small>
                                <br>
                                <a href="https://www.canva.com" target="_blank" class="text-primary">Create with Canva</a>
                            </div>
                            <img id="imagePreview" src="#" alt="Preview" style="max-width: 200px; max-height: 200px; display: none;" class="img-thumbnail mt-3">
                        </div>
                    </div>

                    <!-- GST Settings -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-file-invoice"></i>
                            GST & Compliance
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="skipGstForm" name="skip_gst_form">
                                    <label class="form-check-label" for="skipGstForm">
                                        Skip GST form collection
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="hsnSacNo" class="form-label">
                                    <i class="fas fa-barcode"></i>
                                    HSN/SAC No.
                                </label>
                                <input type="text" class="form-control" id="hsnSacNo" name="hsn_sac_no" placeholder="Enter HSN/SAC number">
                            </div>
                        </div>
                    </div>

                    <!-- Redirect URL -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-external-link-alt"></i>
                            Redirect Settings
                        </div>
                        <div class="col-12 mb-3">
                            <label for="redirectUrl" class="form-label">
                                <i class="fas fa-link"></i>
                                Redirect URL
                            </label>
                            <input type="url" class="form-control" id="redirectUrl" name="redirect_url" placeholder="https://example.com/success">
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-align-left"></i>
                            Product Description
                        </div>
                        <div class="border rounded-3">
                            <div class="toolbar">
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-bold"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-italic"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-underline"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-strikethrough"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-quote-right"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-code"></i></button>
                            </div>
                            <textarea class="form-control border-0 rounded-0 rounded-bottom-3" id="description" name="product_description" rows="6" placeholder="Describe your product..." style="resize: vertical;"></textarea>
                        </div>
                    </div>

                    <!-- Invoice Footer -->
                    <div class="section-card">
                        <div class="section-title">
                            <i class="fas fa-file-invoice-dollar"></i>
                            Invoice Footer
                        </div>
                        <div class="border rounded-3">
                            <div class="toolbar">
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-bold"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-italic"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-underline"></i></button>
                                <button type="button" class="btn btn-sm btn-outline-secondary me-1"><i class="fas fa-strikethrough"></i></button>
                            </div>
                            <textarea class="form-control border-0 rounded-0 rounded-bottom-3" id="invoiceFooter" name="invoice_footer_description" rows="4" placeholder="Enter bank details, policies, or terms..." style="resize: vertical;"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-modern btn-modern d-flex align-items-center" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success-modern btn-modern">
                        <i class="fas fa-save me-2"></i>Create Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Coupon Modal -->
<div class="modal fade" id="addCouponModal" tabindex="-1" aria-labelledby="addCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCouponModalLabel"><?php echo e(__('Create Coupon')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCouponForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row">
                        <!-- Coupon Name -->
                        <div class="col-md-6 mb-3">
                            <label for="couponName" class="form-label"><?php echo e(__('Coupon Name')); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="couponName" name="name" placeholder="<?php echo e(__('Enter coupon name')); ?>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Coupon Code -->
                        <div class="col-md-6 mb-3">
                            <label for="couponCode" class="form-label"><?php echo e(__('Coupon Code')); ?> <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="couponCode" name="code" placeholder="<?php echo e(__('Enter coupon code')); ?>" required>
                                <button type="button" class="btn btn-outline-secondary generate_coupon" id="generateCodeBtn">
                                    <i class="ti ti-refresh"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Product Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="couponProduct" class="form-label"><?php echo e(__('Product')); ?></label>
                            <select class="form-select" id="couponProduct" name="product_id">
                                <option value=""><?php echo e(__('All Products')); ?></option>
                                <?php
                                    try {
                                        $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                ?>
                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($product->id); ?>"><?php echo e($product->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Discount Type -->
                        <div class="col-md-6 mb-3">
                            <label for="discountType" class="form-label"><?php echo e(__('Discount Type')); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="discountType" name="discount_type" required>
                                <option value="percentage"><?php echo e(__('Percentage')); ?></option>
                                <option value="fixed"><?php echo e(__('Fixed Amount')); ?></option>
                            </select>
                        </div>

                        <!-- Discount Amount -->
                        <div class="col-md-6 mb-3">
                            <label for="discountAmount" class="form-label"><?php echo e(__('Discount Amount')); ?> <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="discountAmount" name="discount" placeholder="<?php echo e(__('Enter discount amount')); ?>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Usage Limit -->
                        <div class="col-md-6 mb-3">
                            <label for="usageLimit" class="form-label"><?php echo e(__('Usage Limit')); ?> <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="usageLimit" name="limit" placeholder="<?php echo e(__('Enter usage limit')); ?>" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Start Date -->
                        <div class="col-md-6 mb-3">
                            <label for="startDate" class="form-label"><?php echo e(__('Start Date')); ?></label>
                            <input type="date" class="form-control" id="startDate" name="start_date">
                        </div>

                        <!-- End Date -->
                        <div class="col-md-6 mb-3">
                            <label for="endDate" class="form-label"><?php echo e(__('End Date')); ?></label>
                            <input type="date" class="form-control" id="endDate" name="end_date">
                        </div>

                        <!-- Status -->
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                <label class="form-check-label" for="isActive">
                                    <?php echo e(__('Active')); ?>

                                </label>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="couponDescription" class="form-label"><?php echo e(__('Description')); ?></label>
                            <textarea class="form-control" id="couponDescription" name="description" rows="3" placeholder="<?php echo e(__('Enter coupon description (optional)')); ?>"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-success"><?php echo e(__('Create Coupon')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Coupon Modal -->
<div class="modal fade" id="viewCouponModal" tabindex="-1" aria-labelledby="viewCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewCouponModalLabel"><?php echo e(__('View Coupon')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Close')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Coupon Confirmation Modal -->
<div class="modal fade" id="deleteCouponModal" tabindex="-1" aria-labelledby="deleteCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCouponModalLabel"><?php echo e(__('Delete Coupon')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="ti ti-alert-triangle text-warning" style="font-size: 3rem;"></i>
                    <h4 class="mt-3"><?php echo e(__('Are you sure?')); ?></h4>
                    <p class="text-muted"><?php echo e(__('This action cannot be undone. This will permanently delete the coupon.')); ?></p>
                    <p class="fw-bold" id="couponNameToDelete"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                <button type="button" class="btn btn-danger" id="confirmDeleteCoupon">
                    <i class="ti ti-trash me-1"></i><?php echo e(__('Delete Coupon')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Inline functions for immediate execution
function handleFreeTrialChange(checkbox) {
    console.log('Free trial changed (inline):', checkbox.checked);

    // Show or hide trial fields
    if (checkbox.checked) {
        document.getElementById('trialFields').style.display = 'block';

        // Make fields required
        document.getElementById('trialDurationType').required = true;
        document.getElementById('trialDuration').required = true;
        document.getElementById('trialPrice').required = true;
    } else {
        document.getElementById('trialFields').style.display = 'none';

        // Remove required attributes
        document.getElementById('trialDurationType').required = false;
        document.getElementById('trialDuration').required = false;
        document.getElementById('trialPrice').required = false;
    }
}

function handleSubscriptionChange(checkbox) {
    console.log('Subscription changed (inline):', checkbox.checked);

    // Show or hide subscription fields
    if (checkbox.checked) {
        document.getElementById('subscriptionFields').style.display = 'block';

        // Make field required
        document.getElementById('billedEvery').required = true;
    } else {
        document.getElementById('subscriptionFields').style.display = 'none';

        // Remove required attribute
        document.getElementById('billedEvery').required = false;
    }
}


$(document).ready(function() {
    console.log('Product modal script loaded');



    // Handle free trial checkbox - Allow both trial and subscription to be selected
    $('#isFreeTrial').on('change', function() {
        console.log('Free trial checkbox changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#trialFields').show();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', true);
            console.log('Trial fields shown');
        } else {
            $('#trialFields').hide();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
            console.log('Trial fields hidden');
        }
    });

    // Handle subscription checkbox - Allow both trial and subscription to be selected
    $('#isSubscription').on('change', function() {
        console.log('Subscription checkbox changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#subscriptionFields').show();
            $('#billedEvery').prop('required', true);
            console.log('Subscription fields shown');
        } else {
            $('#subscriptionFields').hide();
            $('#billedEvery').prop('required', false);
            console.log('Subscription fields hidden');
        }
    });

    // Handle form submission
    $('#addProductForm').on('submit', function(e) {
        e.preventDefault();

        // Custom validation
        let isValid = true;

        // Check if free trial is selected and required fields are filled
        if ($('#isFreeTrial').is(':checked')) {
            if (!$('#trialDurationType').val()) {
                $('#trialDurationType').addClass('is-invalid');
                $('#trialDurationType').siblings('.invalid-feedback').text('<?php echo e(__("Trial Duration Type is required")); ?>');
                isValid = false;
            }
            if (!$('#trialDuration').val()) {
                $('#trialDuration').addClass('is-invalid');
                $('#trialDuration').siblings('.invalid-feedback').text('<?php echo e(__("Trial Duration is required")); ?>');
                isValid = false;
            }
        }

        // Check if subscription is selected and required fields are filled
        if ($('#isSubscription').is(':checked')) {
            if (!$('#billedEvery').val()) {
                $('#billedEvery').addClass('is-invalid');
                $('#billedEvery').siblings('.invalid-feedback').text('<?php echo e(__("Billed Every is required")); ?>');
                isValid = false;
            }
        }

        if (!isValid) {
            show_toastr('error', '<?php echo e(__("Please fill in all required fields")); ?>');
            return;
        }

        const formData = new FormData(this);

        // Ensure CSRF token is included
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Convert checkbox values from "on" to boolean strings
        const checkboxFields = [
            'restrict_one_time',
            'is_free_trial',
            'is_subscription',
            'is_cancellable',
            'emi_options',
            'show_shipping_field',
            'required_shipping_field',
            'payment_gateway',
            'skip_gst_form'
        ];

        checkboxFields.forEach(function(fieldName) {
            const checkbox = $(`input[name="${fieldName}"]`);
            if (checkbox.length) {
                // Remove the existing value (which might be "on")
                formData.delete(fieldName);
                // Add the boolean value
                formData.append(fieldName, checkbox.is(':checked') ? '1' : '0');
            }
        });



        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('<?php echo e(__("Creating...")); ?>');

        $.ajax({
            url: '<?php echo e(route("finance.plan.store-product")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    show_toastr('success', response.message);

                    // Close modal
                    $('#addProductModal').modal('hide');

                    // Reset form
                    $('#addProductForm')[0].reset();
                    $('#imagePreview').hide();
                    $('#imagePlaceholder').show();
                    $('#trialFields').hide();
                    $('#subscriptionFields').hide();
                    $('#emiFields').hide();
                    $('#shippingFields').hide();
                    $('#paymentGatewayFields').hide();
                    $('#customGatewayField').hide();

                    // Reload the page or update the products table
                    location.reload();
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                }
            },
            error: function(xhr) {
                let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

                // Clear previous validation errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Handle validation errors
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"], [name="${field}[]"]`);
                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Clear validation errors when user starts typing
    $('#addProductForm input, #addProductForm select, #addProductForm textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Reset modal when closed
    $('#addProductModal').on('hidden.bs.modal', function() {
        $('#addProductForm')[0].reset();
        $('#imagePreview').hide();
        $('#imagePlaceholder').show();
        $('#trialFields').hide();
        $('#subscriptionFields').hide();
        $('#emiFields').hide();
        $('#shippingFields').hide();
        $('#paymentGatewayFields').hide();
        $('#customGatewayField').hide();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    });

    // Debug when modal is shown
    $('#addProductModal').on('shown.bs.modal', function() {
        console.log('Modal shown');
        console.log('Free trial checkbox exists:', $('#isFreeTrial').length);
        console.log('Subscription checkbox exists:', $('#isSubscription').length);
        console.log('Trial fields exists:', $('#trialFields').length);
        console.log('Subscription fields exists:', $('#subscriptionFields').length);
        console.log('Image upload area exists:', $('#imageUploadArea').length);
        console.log('Product image input exists:', $('#productImage').length);
        console.log('Image preview exists:', $('#imagePreview').length);
        console.log('Image placeholder exists:', $('#imagePlaceholder').length);
    });

    // Alternative event delegation approach in case elements are not ready - Allow both to be selected
    $(document).on('change', '#isFreeTrial', function() {
        console.log('Free trial checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#trialFields').show();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', true);
        } else {
            $('#trialFields').hide();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
        }
    });

    $(document).on('change', '#isSubscription', function() {
        console.log('Subscription checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#subscriptionFields').show();
            $('#billedEvery').prop('required', true);
        } else {
            $('#subscriptionFields').hide();
            $('#billedEvery').prop('required', false);
        }
    });

    // EMI Options event handlers
    $(document).on('change', '#emiOptions', function() {
        console.log('EMI checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#emiFields').show();
            $('#emiOptionsInput').prop('required', true);
        } else {
            $('#emiFields').hide();
            $('#emiOptionsInput').prop('required', false);
            $('#emiOptionsInput').val('');
            $('#downPayment').val('');
        }
    });

    // Shipping Field event handlers
    $(document).on('change', '#showShippingField', function() {
        console.log('Shipping checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#shippingFields').show();
        } else {
            $('#shippingFields').hide();
            $('#requiredShippingField').prop('checked', false);
        }
    });

    // Payment Gateway event handlers
    $(document).on('change', '#paymentGateway', function() {
        console.log('Payment Gateway checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#paymentGatewayFields').show();
            $('#paymentGatewayLink').prop('required', true);
        } else {
            $('#paymentGatewayFields').hide();
            $('#paymentGatewayLink').prop('required', false);
            $('#paymentGatewayLink').val('');
            $('#customGatewayUrl').val('');
            $('#customGatewayField').hide();
        }
    });

    // Payment Gateway dropdown change handler
    $(document).on('change', '#paymentGatewayLink', function() {
        const selectedValue = $(this).val();
        if (selectedValue === 'custom') {
            $('#customGatewayField').show();
            $('#customGatewayUrl').prop('required', true);
        } else {
            $('#customGatewayField').hide();
            $('#customGatewayUrl').prop('required', false);
            $('#customGatewayUrl').val('');
        }
    });

    // Coupon Modal Functionality
    // Generate random coupon code
    $('#generateCodeBtn').on('click', function() {
        const randomCode = 'COUPON' + Math.random().toString(36).substr(2, 6).toUpperCase();
        $('#couponCode').val(randomCode);
    });

    // Validate discount based on type
    $('#discountType, #discountAmount').on('change input', function() {
        const discountType = $('#discountType').val();
        const discountAmount = parseFloat($('#discountAmount').val());

        if (discountType === 'percentage' && discountAmount > 100) {
            $('#discountAmount').addClass('is-invalid');
            $('#discountAmount').siblings('.invalid-feedback').text('<?php echo e(__("Percentage discount cannot exceed 100%")); ?>');
        } else {
            $('#discountAmount').removeClass('is-invalid');
            $('#discountAmount').siblings('.invalid-feedback').text('');
        }
    });

    // Validate date range
    $('#startDate, #endDate').on('change', function() {
        const startDate = $('#startDate').val();
        const endDate = $('#endDate').val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            $('#endDate').addClass('is-invalid');
            $('#endDate').siblings('.invalid-feedback').text('<?php echo e(__("End date must be after start date")); ?>');
        } else {
            $('#endDate').removeClass('is-invalid');
            $('#endDate').siblings('.invalid-feedback').text('');
        }
    });

    // Handle coupon form submission (both create and update)
    $('#addCouponForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();
        const isEdit = $('#editCouponId').length > 0;
        const url = isEdit ? '<?php echo e(route("finance.plan.update-coupon")); ?>' : '<?php echo e(route("finance.plan.store-coupon")); ?>';

        submitBtn.prop('disabled', true).text(isEdit ? '<?php echo e(__("Updating...")); ?>' : '<?php echo e(__("Creating...")); ?>');

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    $('#addCouponModal').modal('hide');

                    if (isEdit) {
                        // Update the existing row without page reload
                        updateCouponRow(response.coupon || formDataToObject(formData));
                    } else {
                        // Add new row to table without page reload
                        addNewCouponRow(response.coupon);
                    }
                } else {
                    show_toastr('error', response.message || '<?php echo e(__("Something went wrong")); ?>');
                }
            },
            error: function(xhr) {
                let errorMessage = '<?php echo e(__("Something went wrong")); ?>';

                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON?.errors) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"]`);
                        const feedback = input.closest('.mb-3').find('.invalid-feedback');
                        input.addClass('is-invalid');
                        feedback.text(errors[field][0]);
                    });
                    errorMessage = '<?php echo e(__("Please check the form for errors")); ?>';
                } else if (xhr.responseJSON?.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON?.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });


    // Clear validation errors when user starts typing
    $('#addCouponForm input, #addCouponForm select, #addCouponForm textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Reset modal when closed
    $('#addCouponModal').on('hidden.bs.modal', function() {
        $('#addCouponForm')[0].reset();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        // Reset modal to create mode
        $('#addCouponModalLabel').text("<?php echo e(__('Create Coupon')); ?>");
        $('#addCouponForm button[type=submit]').text("<?php echo e(__('Create Coupon')); ?>");
        $('#editCouponId').remove();
    });

    // Generate coupon
    $(".generate_coupon").on('click', function(e) {
        e.preventDefault();

        var name = $('#couponName').val().toUpperCase().replace(/\s+/g, '');

        if (name.length < 3) {
            alert("Enter a longer coupon name to generate a valid code.");
            return;
        }

        // Append 3 random digits to the name
        var randomDigits = Math.floor(100 + Math.random() * 900); // generates a number between 100-999
        var finalCode = name + randomDigits;

        $('#couponCode').val(finalCode);
    });

    // Edit Coupon Event
    $(document).on('click', '.edit-coupon', function () {
        var couponId = $(this).data('coupon-id');
        $.ajax({
            url: '<?php echo e(route("finance.plan.edit-coupon")); ?>', // Route should return coupon data
            type: 'GET',
            data: {
                coupon_id: couponId
            },
            success: function (response) {
                if (response.success) {
                    const coupon = response.data;

                    // Set modal fields
                    $('#couponName').val(coupon.name);
                    $('#couponCode').val(coupon.code);
                    $('#couponProduct').val(coupon.product_id);
                    $('#discountType').val(coupon.discount_type);
                    $('#discountAmount').val(coupon.discount);
                    $('#usageLimit').val(coupon.limit);
                    $('#startDate').val(coupon.start_date);
                    $('#endDate').val(coupon.end_date);
                    $('#isActive').prop('checked', coupon.is_active == 1);
                    $('#couponDescription').val(coupon.description);

                    // Change modal UI for edit
                    $('#addCouponModalLabel').text("<?php echo e(__('Edit Coupon')); ?>");
                    $('#addCouponForm button[type=submit]').text("<?php echo e(__('Update Coupon')); ?>");

                    // Add hidden input to track edit
                    $('#addCouponForm').append(`<input type="hidden" name="coupon_id" id="editCouponId" value="${coupon.id}">`);

                    // Show modal
                    $('#addCouponModal').modal('show');
                } else {
                    show_toastr('error', response.message || 'Failed to load coupon');
                }
            },
            error: function () {
                show_toastr('error', 'Failed to load coupon');
            }
        });
    });

    // View Coupon Event
    $(document).on('click', '.view-coupon', function () {
        var couponId = $(this).data('coupon-id');
        $.ajax({
            url: '<?php echo e(route("finance.plan.edit-coupon")); ?>',
            type: 'GET',
            data: {
                coupon_id: couponId
            },
            success: function (response) {
                if (response.success) {
                    const coupon = response.data;

                    // Create view modal content
                    const viewContent = `
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Coupon Name')); ?></label>
                                <p class="form-control-plaintext">${coupon.name}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Coupon Code')); ?></label>
                                <p class="form-control-plaintext"><code>${coupon.code}</code></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Product')); ?></label>
                                <p class="form-control-plaintext">${coupon.product_id ? $('#couponProduct option[value="' + coupon.product_id + '"]').text() : '<?php echo e(__("All Products")); ?>'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Discount Type')); ?></label>
                                <p class="form-control-plaintext">${coupon.discount_type === 'percentage' ? '<?php echo e(__("Percentage")); ?>' : '<?php echo e(__("Fixed Amount")); ?>'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Discount Amount')); ?></label>
                                <p class="form-control-plaintext">${coupon.discount}${coupon.discount_type === 'percentage' ? '%' : ''}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Usage Limit')); ?></label>
                                <p class="form-control-plaintext">${coupon.limit}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Start Date')); ?></label>
                                <p class="form-control-plaintext">${coupon.start_date || '<?php echo e(__("No Start Date")); ?>'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('End Date')); ?></label>
                                <p class="form-control-plaintext">${coupon.end_date || '<?php echo e(__("No End Date")); ?>'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Status')); ?></label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-${coupon.is_active ? 'success' : 'danger'}">
                                        ${coupon.is_active ? '<?php echo e(__("Active")); ?>' : '<?php echo e(__("Inactive")); ?>'}
                                    </span>
                                </p>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold"><?php echo e(__('Description')); ?></label>
                                <p class="form-control-plaintext">${coupon.description || '<?php echo e(__("No description")); ?>'}</p>
                            </div>
                        </div>
                    `;

                    // Show view modal
                    $('#viewCouponModal .modal-body').html(viewContent);
                    $('#viewCouponModal').modal('show');
                } else {
                    show_toastr('error', response.message || 'Failed to load coupon');
                }
            },
            error: function () {
                show_toastr('error', 'Failed to load coupon');
            }
        });
    });

    // Delete Coupon Event - Show Modal
    $(document).on('click', '.delete-coupon', function () {
        var couponId = $(this).data('coupon-id');
        var couponRow = $(this).closest('tr');
        var couponName = couponRow.find('td:first').text();

        // Store coupon ID and row reference for later use
        $('#confirmDeleteCoupon').data('coupon-id', couponId);
        $('#confirmDeleteCoupon').data('coupon-row', couponRow);
        $('#couponNameToDelete').text(couponName);

        // Show delete confirmation modal
        $('#deleteCouponModal').modal('show');
    });

    // Confirm Delete Coupon
    $(document).on('click', '#confirmDeleteCoupon', function () {
        var couponId = $(this).data('coupon-id');
        var couponRow = $(this).data('coupon-row');
        var deleteBtn = $(this);
        var originalText = deleteBtn.html();

        deleteBtn.prop('disabled', true).html('<i class="ti ti-loader ti-spin me-1"></i><?php echo e(__("Deleting...")); ?>');

        $.ajax({
            url: '<?php echo e(route("finance.plan.delete-coupon")); ?>',
            type: 'POST',
            data: {
                coupon_id: couponId,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    // Remove the row from table without page reload
                    couponRow.fadeOut(300, function() {
                        $(this).remove();
                        // Check if table is empty and show empty state
                        if ($('#coupons-view tbody tr').length === 0) {
                            $('#coupons-view tbody').html(`
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-ticket" style="font-size: 3rem;"></i>
                                            <p class="mt-2"><?php echo e(__('No coupons found')); ?></p>
                                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                                <i class="ti ti-plus me-1"></i><?php echo e(__('Add First Coupon')); ?>

                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `);
                        }
                    });
                    $('#deleteCouponModal').modal('hide');
                } else {
                    show_toastr('error', response.message || 'Failed to delete coupon');
                }
            },
            error: function () {
                show_toastr('error', 'Failed to delete coupon');
            },
            complete: function() {
                deleteBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Toggle Coupon Status
    $(document).on('change', '.coupon-status-toggle', function () {
        var couponId = $(this).data('coupon-id');
        var isActive = $(this).is(':checked') ? 1 : 0;
        var toggleSwitch = $(this);

        // Disable the toggle while processing
        toggleSwitch.prop('disabled', true);

        $.ajax({
            url: '<?php echo e(route("finance.plan.toggle-coupon-status")); ?>',
            type: 'POST',
            data: {
                coupon_id: couponId,
                is_active: isActive,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                if (response.success) {
                    show_toastr('success', response.message);
                } else {
                    // Revert the toggle if failed
                    toggleSwitch.prop('checked', !isActive);
                    show_toastr('error', response.message || 'Failed to update status');
                }
            },
            error: function () {
                // Revert the toggle if failed
                toggleSwitch.prop('checked', !isActive);
                show_toastr('error', 'Failed to update coupon status');
            },
            complete: function() {
                toggleSwitch.prop('disabled', false);
            }
        });
    });

    // Helper function to convert FormData to object
    function formDataToObject(formData) {
        var object = {};
        formData.forEach(function(value, key) {
            object[key] = value;
        });
        return object;
    }

    // Helper function to add new coupon row after create
    function addNewCouponRow(coupon) {
        // Check if table has empty state and remove it
        var emptyRow = $('#coupons-view tbody tr td[colspan="8"]').closest('tr');
        if (emptyRow.length > 0) {
            emptyRow.remove();
        }

        // Format dates
        var startDate = coupon.start_date ? new Date(coupon.start_date).toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'}) : '<?php echo e(__("No Start Date")); ?>';
        var endDate = coupon.end_date ? new Date(coupon.end_date).toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'}) : '<?php echo e(__("No End Date")); ?>';

        // Format discount
        var discountText = coupon.discount + (coupon.discount_type === 'percentage' ? '%' : '<?php echo e(\Auth::user()->currencySymbol()); ?>');

        // Create new row HTML
        var newRow = `
            <tr style="display: none;">
                <td>${coupon.name}</td>
                <td><code>${coupon.code}</code></td>
                <td>${coupon.product_name || '<?php echo e(__("All Products")); ?>'}</td>
                <td>${startDate}</td>
                <td>${endDate}</td>
                <td>${discountText}</td>
                <td>
                    <div class="form-check form-switch">
                        <input class="form-check-input coupon-status-toggle" type="checkbox"
                               data-coupon-id="${coupon.id}"
                               ${coupon.is_active ? 'checked' : ''}>
                    </div>
                </td>
                <td>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-primary view-coupon" data-coupon-id="${coupon.id}" title="<?php echo e(__('View')); ?>">
                            <i class="ti ti-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary edit-coupon" data-coupon-id="${coupon.id}" title="<?php echo e(__('Edit')); ?>">
                            <i class="ti ti-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-coupon" data-coupon-id="${coupon.id}" title="<?php echo e(__('Delete')); ?>">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Add the new row to the table and show it with animation
        $('#coupons-view tbody').prepend(newRow);
        $('#coupons-view tbody tr:first').fadeIn(300);
    }

    // Helper function to update coupon row after edit
    function updateCouponRow(coupon) {
        var couponId = coupon.id || coupon.coupon_id;
        var row = $(`button[data-coupon-id="${couponId}"]`).closest('tr');

        if (row.length > 0) {
            // Update the row data
            row.find('td:nth-child(1)').text(coupon.name);
            row.find('td:nth-child(2)').html(`<code>${coupon.code}</code>`);
            row.find('td:nth-child(3)').text(coupon.product_name || '<?php echo e(__("All Products")); ?>');

            // Update dates
            var startDate = coupon.start_date ? new Date(coupon.start_date).toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'}) : '<?php echo e(__("No Start Date")); ?>';
            var endDate = coupon.end_date ? new Date(coupon.end_date).toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'}) : '<?php echo e(__("No End Date")); ?>';

            row.find('td:nth-child(4)').text(startDate);
            row.find('td:nth-child(5)').text(endDate);

            // Update discount
            var discountText = coupon.discount + (coupon.discount_type === 'percentage' ? '%' : '<?php echo e(\Auth::user()->currencySymbol()); ?>');
            row.find('td:nth-child(6)').text(discountText);

            // Update status toggle
            row.find('.coupon-status-toggle').prop('checked', coupon.is_active == 1);

            // Add a brief highlight effect to show the row was updated
            row.addClass('table-warning');
            setTimeout(function() {
                row.removeClass('table-warning');
            }, 2000);
        }
    }

    let bumpOfferCount = 0;

    // Add Bump Offer functionality
    $('#addBumpOfferBtn').on('click', function() {
        bumpOfferCount++;
        const bumpOfferHtml = `
            <div class="bump-offer-item fade-in" data-offer-id="${bumpOfferCount}">
                <button type="button" class="remove-bump-offer" onclick="removeBumpOffer(${bumpOfferCount})">
                    <i class="fas fa-times"></i>
                </button>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">
                            <i class="fas fa-gift"></i>
                            Offer Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" name="bump_offers[${bumpOfferCount}][name]" placeholder="Enter offer name" required>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">
                            <i class="fas fa-dollar-sign"></i>
                            Price <span class="text-danger">*</span>
                        </label>
                        <input type="number" step="0.01" class="form-control" name="bump_offers[${bumpOfferCount}][price]" placeholder="0.00" required>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">
                            <i class="fas fa-percentage"></i>
                            Discount %
                        </label>
                        <input type="number" step="0.01" class="form-control" name="bump_offers[${bumpOfferCount}][discount]" placeholder="0" min="0" max="100">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">
                            <i class="fas fa-info-circle"></i>
                            Description
                        </label>
                        <textarea class="form-control" name="bump_offers[${bumpOfferCount}][description]" rows="3" placeholder="Describe this bump offer..."></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="bumpActive${bumpOfferCount}" name="bump_offers[${bumpOfferCount}][active]" checked>
                            <label class="form-check-label" for="bumpActive${bumpOfferCount}">
                                Active
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="bumpDefault${bumpOfferCount}" name="bump_offers[${bumpOfferCount}][default_selected]">
                            <label class="form-check-label" for="bumpDefault${bumpOfferCount}">
                                Default Selected
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $('#bumpOffersContainer').append(bumpOfferHtml);
        
        // Add animation
        setTimeout(() => {
            $(`[data-offer-id="${bumpOfferCount}"]`).addClass('show');
        }, 100);
    });

    // Image upload functionality - Use event delegation to prevent infinite loops
    $(document).on('click', '#imageUploadArea', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Image upload area clicked');
        document.getElementById('productImage').click();
    });

    $(document).on('change', '#productImage', function(e) {
        console.log('File input changed');
        const file = e.target.files[0];
        if (file) {
            console.log('File selected:', file.name, file.type);
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('File loaded, showing preview');
                $('#imagePreview').attr('src', e.target.result).show();
                $('#imagePlaceholder').hide();
            };
            reader.readAsDataURL(file);
        } else {
            console.log('No file selected');
            $('#imagePreview').hide();
            $('#imagePlaceholder').show();
        }
    });

    // Drag and drop functionality - Use event delegation
    $(document).on('dragover', '#imageUploadArea', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Drag over');
        $(this).addClass('dragover');
    });

    $(document).on('dragleave', '#imageUploadArea', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Drag leave');
        $(this).removeClass('dragover');
    });

    $(document).on('drop', '#imageUploadArea', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('File dropped');
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            console.log('Dropped file:', file.name, file.type);
            if (file.type.startsWith('image/')) {
                const fileInput = document.getElementById('productImage');
                fileInput.files = files;
                // Manually trigger the change event
                const event = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(event);
            } else {
                console.log('File is not an image');
            }
        }
    });



    // Tax slab change handler
    $('#taxSlab').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const selectedText = selectedOption.text();

        // Extract percentage from the label (e.g., "GST 18%" -> 18)
        const percentageMatch = selectedText.match(/(\d+(?:\.\d+)?)/);
        const taxRate = percentageMatch ? parseFloat(percentageMatch[1]) : 0;

        $('#taxRate').val(taxRate);
    });

    // Billing cycle update
    $('#billedEvery, #billingCycleType').on('change', function() {
        const billedEvery = $('#billedEvery').val();
        const cycleType = $('#billingCycleType').val();
        
        if (billedEvery && cycleType) {
            const displayText = billedEvery == 1 ? 
                cycleType.charAt(0).toUpperCase() + cycleType.slice(1) :
                `Every ${billedEvery} ${cycleType}`;
            $('#billingCycle').val(displayText);
        }
    });

    function handleEmiChange(checkbox) {
        const emiFields = $('#emiFields');
        if (checkbox.checked) {
            emiFields.slideDown(300).addClass('show');
            // Make EMI options required when enabled
            $('#emiOptionsInput').attr('required', true);
        } else {
            emiFields.slideUp(300).removeClass('show');
            $('#emiOptionsInput').removeAttr('required');
            // Clear values when disabled
            $('#emiOptionsInput').val('');
            $('#downPayment').val('');
        }
    }

    function handleShippingChange(checkbox) {
        const shippingFields = $('#shippingFields');
        if (checkbox.checked) {
            shippingFields.slideDown(300).addClass('show');
        } else {
            shippingFields.slideUp(300).removeClass('show');
            // Uncheck required shipping when disabled
            $('#requiredShippingField').prop('checked', false);
        }
    }

    function handlePaymentGatewayChange(checkbox) {
        const paymentGatewayFields = $('#paymentGatewayFields');
        if (checkbox.checked) {
            paymentGatewayFields.slideDown(300).addClass('show');
            $('#paymentGatewayLink').attr('required', true);
        } else {
            paymentGatewayFields.slideUp(300).removeClass('show');
            $('#paymentGatewayLink').removeAttr('required');
            // Clear values when disabled
            $('#paymentGatewayLink').val('');
            $('#customGatewayUrl').val('');
            $('#customGatewayField').hide();
        }
    }
});

function removeBumpOffer(offerId) {
    const offerElement = $(`[data-offer-id="${offerId}"]`);
    offerElement.fadeOut(300, function() {
        $(this).remove();
    });
}

function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(notification);
    
    setTimeout(() => {
        notification.fadeOut(() => notification.remove());
    }, 4000);
}

// Toolbar button functionality (basic implementation)
$('.toolbar .btn').on('click', function(e) {
    e.preventDefault();
    const button = $(this);
    const icon = button.find('i').attr('class');
    
    // Get the associated textarea
    const textarea = button.closest('.border').find('textarea');
    const textareaElement = textarea[0];
    
    // Basic text formatting (you can extend this)
    if (textareaElement) {
        const start = textareaElement.selectionStart;
        const end = textareaElement.selectionEnd;
        const selectedText = textareaElement.value.substring(start, end);
        
        let wrappedText = selectedText;
        
        if (icon.includes('fa-bold')) {
            wrappedText = `**${selectedText}**`;
        } else if (icon.includes('fa-italic')) {
            wrappedText = `*${selectedText}*`;
        } else if (icon.includes('fa-underline')) {
            wrappedText = `<u>${selectedText}</u>`;
        } else if (icon.includes('fa-quote-right')) {
            wrappedText = `> ${selectedText}`;
        }
        
        // Replace selected text
        const newValue = textareaElement.value.substring(0, start) + 
                        wrappedText + 
                        textareaElement.value.substring(end);
        
        textarea.val(newValue);
        textareaElement.focus();
        textareaElement.setSelectionRange(start, start + wrappedText.length);
    }
});

// Form field animations
$('.form-control, .form-select').on('focus', function() {
    $(this).parent().addClass('focused');
}).on('blur', function() {
    $(this).parent().removeClass('focused');
});

// Price formatting
$('#productPrice, #strikedPrice, #trialPrice').on('blur', function() {
    const value = parseFloat($(this).val());
    if (!isNaN(value)) {
        $(this).val(value.toFixed(2));
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/finance/tabs/plan.blade.php ENDPATH**/ ?>