{"__meta": {"id": "Xfbfbae60dda7a46953da4645d5f82f5e", "datetime": "2025-07-31 11:15:09", "utime": **********.189755, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960508.546083, "end": **********.18978, "duration": 0.6436970233917236, "duration_str": "644ms", "measures": [{"label": "Booting", "start": 1753960508.546083, "relative_start": 0, "end": **********.07616, "relative_end": **********.07616, "duration": 0.5300769805908203, "duration_str": "530ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076174, "relative_start": 0.5300910472869873, "end": **********.189784, "relative_end": 4.0531158447265625e-06, "duration": 0.11361002922058105, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936848, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1532\" onclick=\"\">app/Http/Controllers/FinanceController.php:1532-1599</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02758, "accumulated_duration_str": "27.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.131803, "duration": 0.0101, "duration_str": "10.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 36.621}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.152814, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 36.621, "width_percent": 2.864}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1548}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.158838, "duration": 0.00723, "duration_str": "7.23ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1548", "source": "app/Http/Controllers/FinanceController.php:1548", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1548", "ajax": false, "filename": "FinanceController.php", "line": "1548"}, "connection": "radhe_same", "start_percent": 39.485, "width_percent": 26.215}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1572}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.170052, "duration": 0.009460000000000001, "duration_str": "9.46ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1572", "source": "app/Http/Controllers/FinanceController.php:1572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1572", "ajax": false, "filename": "FinanceController.php", "line": "1572"}, "connection": "radhe_same", "start_percent": 65.7, "width_percent": 34.3}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-156258087 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-156258087\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-944146789 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944146789\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1987067011 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1987067011\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-535140162 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlRoYk84ZC9UeXN6WW95OWpWS01KbGc9PSIsInZhbHVlIjoibER5WkFiNy9zazh0S1lTOXlIdHdXTVFQUTN4SXhGZ2FBcm8rYlUveWd2UjBzYmlLMHJXQVRQRG5kSmQxS3YyaW9XTW9SMC9XcFRuQmEvM3paM3RnWnhndzdrVmk2cE15SGpCM2Rpd2lLbHQyb05jVW56Mm9zSHZ3Q3dFTVhrRStldTQ5S0FNRE1ja2czSE55eEY0dWZiMFF6T04zQVgxbDdEKzBqOXVoalhOY3RKUkQxcFZjMTFHZHk0ZkJ4eDBHUi9CUEY1Ym5CV3BOZHlmc1BVQ2l6bEtoOHJBTFplRlloN0FuSW1xRWhHRm95UXl6UytPRkZrWWY4YkFDQ2FUWnZlVHY1L0NrbE4zS2Z1bDhyU2RmcHdnU0wrYUdKTld4UzVMaGpjQUpaZGpaYXU4a0x0MWVuR3R2SjRqckh6UXhiUnZ3U1dXS3FHU3YzeFE2ZGl3Y1pBbFp0S0lueTRtNzNucXNGMzNGN3dyR254dUp0ZzdNMzZIR3RFa2NWbTVmV2dXcVNwY0dCYlpHcEpiMWdleVhLelFIdTF2bXlacGRibmtmMlFOeXAyaEVTeXNneGRtbXhaOHZWVkZ0Ny9yejRQMG44TzFoRkVVRVVMcjFGVmdxL1htMDFnbm14dm1tbDRua2xKYmNvZ0R1emFVZnNvbVlOQXRjT0JPR3JCNFUiLCJtYWMiOiJlMzZlZjE0OWRiMjgxMzcxZjBkYWNiOGE5MzNhYjIxM2NhZTg2MWUzY2I1Njk3MmJiM2UxOTNhZjIwMThiYzNmIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Ik5wNzhIcGlqZG05ZkVhV2I4U2k1RFE9PSIsInZhbHVlIjoiUWZLK0s1UkMvUEprVTE0cTlxd29oNlFkdHk1Uy90RUF0QXpicFA0OGRpQjUvZnMvSFl5ZW42b1pFNmVzY3hxK3UrSjFtN3J4OHlYQkltWk9vSjhGbFpselRWR3I3YTh4S2RIbVdWSUJxOHppNFhJS1ZQNFI1d0FUTGNpVWdaUzF4NHkzendzaUJPUS9sdTR6K1RpcHdrZXRyYWRZOFMvVkNuU09UeTZjUzF2N1EvMkswbFdSc0YzSWdDOUtwQWlHcmJ5WkttZDdFdVBvZnQ5VzRLYnpoRzB6Y2lRWlpsUmxFdzYzbXdMNEhqc3Zjbnpqb3F1eWZDditIU2lRNVRrK0tLc0o0NGJkempsVmFmWXlGTDAwSzcwQWY0OHdBaERtbW9TZkdNY1VuQnR0MHpHRDQ0WjI2TlJ5V1JQWG1uYmx2ajhuL01OMk0ySEFZMjBaOUxuNjM1elRoeTZHQmVLUzdqVGNjNEdkR3lXTk9HUVFvNmI1VHUwNUFtOEdFRXFRaVdJWHNqNzJqWG1VVzFETVRWeUdFK2Y3Y1BRVThBbDNCTVFRZHhQRWh6SUxXallKU3BETnhHRVNLNTJUUWlTeHc1TWhLSExrWUhPcmlNdGpMTGxYTCtTOHVMclE1WlJrSEt2SmtWN0ZrWk9XbEdLOVltbWIyL0FkSkdxdlRFYmoiLCJtYWMiOiJmZGYzNjZkNGMwMTg1MWIzOWZmNjcyZDc5NDA4OTIyNzdlNmQ1MjVjMzNhODdmYzIyYzFmOTRmNDFmMjllNDdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535140162\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1086419864 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086419864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:15:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikh3R1JMM2FZUmVwOEJtQThXMFM3TlE9PSIsInZhbHVlIjoiWU1uVXpvL2VaeVN4a2tySnZoY2VVcStMdFJFaEdIb3JrVHJXeXVrc1A5aGozWll1OVg1T0ljVElsVEE3N0JXakpsN2EyMWtYejFUd2hpZ0NZQWphTGQrL3NGT0VobkNISDVmRjlCR0RMSjd0dVEvdUFJQkVidVI1Z1lSWm1YOGZsd3l4YUNNb1p1eGZqODJWRGhNcDVTbklJN3gwSzNuS3pFMXdkNjlQa0QyT1V2NUVRdlc3NnRsUmJ6RDhMMElCSkpveEVWSEZ0eitYQUc4MmNIV1FrbUZ3Znd6Qjg5YzRtZ0s2ZTNGOXdnZkNDdGN3ZXh6VmZxTExiUFUxMjdjYS9vVzV3TlFqSDA1bVNwMjd3NFIwcWRPcXRNak9RaHNUTGlPd0tvL0FOU3hJVHNGTlpKVnVEdlFEbVo5SHRVdkljSXBydzZld0FpMDVadHlGTTZJZUVFbjdVSkN1Q08xOHkxMEpycm9sWm9zMWZiYkhydXNOMitBdHo0THhKcXpGRGFkRGI3RFRvR3psOXRvZVluTUhOVVdYSHI3Skd6RzRvRWwxdEhtN1hRWUNwZjNnZTh3TWgyOTdqU3dqQmQ1TlNxdGlJYjlGWUo3MFR1UndIcEVDUThJUGMvYjkrVEl0Z2ZncEIrVzhBYjBMejJnR0V1ejE2T00vNmVWUDVyZnUiLCJtYWMiOiI1OTg4YjEwY2I1Y2E4N2FjYTdhZWZmOTBjNWFmMWE0YWQ4ZTMxYzEwN2E2NTMwZjhjMzc2ZGMxZDJkYzViMTk0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6Im5kQ29oaVdLZ2NiTHY3MEJ6RmNBVmc9PSIsInZhbHVlIjoiQktpSzRTTnEvVzF6ZmZMUUpnRTBvWVQ5Q1hGM21YREVMcWROOU5haFI3b2tIck01d2s5VGV6VUVWdUxpdU1xT2ROVUJYQkMzbGtRcmNFSmxFUjRhNGlXbXc4Wmp1aEhpMWVWaGtXbG5Lc1FZZHk4SGFXVWtXN0ZuRzJHZEpoYzN1ajcxTDU3UG0yemczWTl5KzUwRkhFN2dmMXFYTWxIYmRESjdsQ3BZbWhPMjQrOFhWUkRxbEVrTlF6VU9SUy9Tam1FU3ZjbldGMEZ6MWpmL3dFQ0NGcDZaZVZ3VkhEWk0yRGFKaDlSZmgxWE9pUTkyc2FOejRLbTlGTnN6aEN6YXdHQzdUNUNIRktiWWdyblhKVnk4c1dNS01yNjRLK0VxY2J0WGRORWZudDlYdWxZNHd5S0JQdnVLNW50VmZvekZYak5LVktNbHNxT1pRb0xuZzlEMkVRL091L2s4NHR4UXVWTjhpaTFKa2p1dGFOS0RVRDBuZGx4RUhxb2hLSUlzMTk1RzE3ZHZZNHNBV01iK3dXekJHaFpiOU03YWNhSFRBSW13QXVETS9KWnB3T1FWUUwyV2poT3o0MGFLVEpRdVg1VmxreVlyS3k5YlZodjNtWnlSR1RnVjBMQWdaem9GSVhCWW80enRHL1lBNFhEbXpmT2FSdnpzVjBwMkg0dHAiLCJtYWMiOiI2NmY0OWQxZjQxNWY2YTNmMzliZDE5MTg2ODJjN2M0YTFmMmY3YmY1ODExYTkyZmMwMmU2Njg2MTk4MjNlYjgxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikh3R1JMM2FZUmVwOEJtQThXMFM3TlE9PSIsInZhbHVlIjoiWU1uVXpvL2VaeVN4a2tySnZoY2VVcStMdFJFaEdIb3JrVHJXeXVrc1A5aGozWll1OVg1T0ljVElsVEE3N0JXakpsN2EyMWtYejFUd2hpZ0NZQWphTGQrL3NGT0VobkNISDVmRjlCR0RMSjd0dVEvdUFJQkVidVI1Z1lSWm1YOGZsd3l4YUNNb1p1eGZqODJWRGhNcDVTbklJN3gwSzNuS3pFMXdkNjlQa0QyT1V2NUVRdlc3NnRsUmJ6RDhMMElCSkpveEVWSEZ0eitYQUc4MmNIV1FrbUZ3Znd6Qjg5YzRtZ0s2ZTNGOXdnZkNDdGN3ZXh6VmZxTExiUFUxMjdjYS9vVzV3TlFqSDA1bVNwMjd3NFIwcWRPcXRNak9RaHNUTGlPd0tvL0FOU3hJVHNGTlpKVnVEdlFEbVo5SHRVdkljSXBydzZld0FpMDVadHlGTTZJZUVFbjdVSkN1Q08xOHkxMEpycm9sWm9zMWZiYkhydXNOMitBdHo0THhKcXpGRGFkRGI3RFRvR3psOXRvZVluTUhOVVdYSHI3Skd6RzRvRWwxdEhtN1hRWUNwZjNnZTh3TWgyOTdqU3dqQmQ1TlNxdGlJYjlGWUo3MFR1UndIcEVDUThJUGMvYjkrVEl0Z2ZncEIrVzhBYjBMejJnR0V1ejE2T00vNmVWUDVyZnUiLCJtYWMiOiI1OTg4YjEwY2I1Y2E4N2FjYTdhZWZmOTBjNWFmMWE0YWQ4ZTMxYzEwN2E2NTMwZjhjMzc2ZGMxZDJkYzViMTk0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6Im5kQ29oaVdLZ2NiTHY3MEJ6RmNBVmc9PSIsInZhbHVlIjoiQktpSzRTTnEvVzF6ZmZMUUpnRTBvWVQ5Q1hGM21YREVMcWROOU5haFI3b2tIck01d2s5VGV6VUVWdUxpdU1xT2ROVUJYQkMzbGtRcmNFSmxFUjRhNGlXbXc4Wmp1aEhpMWVWaGtXbG5Lc1FZZHk4SGFXVWtXN0ZuRzJHZEpoYzN1ajcxTDU3UG0yemczWTl5KzUwRkhFN2dmMXFYTWxIYmRESjdsQ3BZbWhPMjQrOFhWUkRxbEVrTlF6VU9SUy9Tam1FU3ZjbldGMEZ6MWpmL3dFQ0NGcDZaZVZ3VkhEWk0yRGFKaDlSZmgxWE9pUTkyc2FOejRLbTlGTnN6aEN6YXdHQzdUNUNIRktiWWdyblhKVnk4c1dNS01yNjRLK0VxY2J0WGRORWZudDlYdWxZNHd5S0JQdnVLNW50VmZvekZYak5LVktNbHNxT1pRb0xuZzlEMkVRL091L2s4NHR4UXVWTjhpaTFKa2p1dGFOS0RVRDBuZGx4RUhxb2hLSUlzMTk1RzE3ZHZZNHNBV01iK3dXekJHaFpiOU03YWNhSFRBSW13QXVETS9KWnB3T1FWUUwyV2poT3o0MGFLVEpRdVg1VmxreVlyS3k5YlZodjNtWnlSR1RnVjBMQWdaem9GSVhCWW80enRHL1lBNFhEbXpmT2FSdnpzVjBwMkg0dHAiLCJtYWMiOiI2NmY0OWQxZjQxNWY2YTNmMzliZDE5MTg2ODJjN2M0YTFmMmY3YmY1ODExYTkyZmMwMmU2Njg2MTk4MjNlYjgxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-218474281 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218474281\", {\"maxDepth\":0})</script>\n"}}