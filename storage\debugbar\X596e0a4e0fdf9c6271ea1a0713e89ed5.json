{"__meta": {"id": "X596e0a4e0fdf9c6271ea1a0713e89ed5", "datetime": "2025-07-31 12:39:13", "utime": **********.256439, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965550.944174, "end": **********.25649, "duration": 2.3123159408569336, "duration_str": "2.31s", "measures": [{"label": "Booting", "start": 1753965550.944174, "relative_start": 0, "end": **********.027127, "relative_end": **********.027127, "duration": 2.0829529762268066, "duration_str": "2.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.027149, "relative_start": 2.082974910736084, "end": **********.256495, "relative_end": 5.0067901611328125e-06, "duration": 0.22934603691101074, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46936848, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1532\" onclick=\"\">app/Http/Controllers/FinanceController.php:1532-1599</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00938, "accumulated_duration_str": "9.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1592128, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 52.878}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.196234, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 52.878, "width_percent": 17.591}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1548}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.209037, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1548", "source": "app/Http/Controllers/FinanceController.php:1548", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1548", "ajax": false, "filename": "FinanceController.php", "line": "1548"}, "connection": "radhe_same", "start_percent": 70.469, "width_percent": 13.326}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1572}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.223133, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1572", "source": "app/Http/Controllers/FinanceController.php:1572", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1572", "ajax": false, "filename": "FinanceController.php", "line": "1572"}, "connection": "radhe_same", "start_percent": 83.795, "width_percent": 16.205}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-797447236 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-797447236\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-969607933 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969607933\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1328632045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1328632045\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inc2N0txTTdZMzh6KzVpWXp4UnVjTVE9PSIsInZhbHVlIjoiOWVmdzQwdkpKSmpZNXhIRWtwQzVRNU9xWldLUEZFWTh2dkhpekV5ajY4dkhZemNLM2JMMEZXOWZCNVpOU0w1ZTR1SStTMUo2cEtrSGdvTmxRYWlMRU9KZndBckNsM0dvTGZIemlFajZ4alFKVCs3d3RVVGpIUHgxMzlvVTIwVmIzNm1kSVdVTGdYRFpMbC9ROEc3UkFNNE5UcU1UTnR6eHJoY0RlYjJWVVh2Ui9IdERJVGZGTnYrd3lVSm1sVTA1R04vY1YxbVBGZU52Wlo4M2N3TThQK1lOMll4OFNTcGhpVmIxbWw4ZDFza1BIK0JIY01tR3Q4akQ3Q2tQV3JZbnVtT0hOd2h5L1M3NWJtS2NXNGNkb1VHWGZMajRnN3FGb2Z5TEtzNWQrOWx0ZmtxMklISGJwUGxCTGlncFBBSmlYMGc2elV3emlwak0wSEF3MG9uYTFTZkxpRUNtazJ3aFlZa2NiT2l5ZVhIUTAxR0cvSE1MUzY1NGdGZzMzU09JSVgzNXhmcHNHQVJSMWpleFBORFFHekQyK3pjSkZ2VUovcmt2WWRmWFBhUUtmNkNBV1plRzR1M0d6SS9RTkozNXY5V1BwRTF5c0x0MjZtUnpaUzRmcjM1eTZNWExkVmtnUW1BaXQrM1JicGNxZkI5ejhqRjEzbHhRbGxObUhaSkgiLCJtYWMiOiI2YmQxMzM0MmI2NWMwNDQ4MWRjODY5ODkzOTQzYjE1YjljN2RhZDY2NzczYzhlNmMyZTQ1ODgwZTNmYTNlZjY2IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6Iis2aDdiSmROT3M4V0dzOGtZbmtaY0E9PSIsInZhbHVlIjoiaXZYWDJTQTF4MEtEUFg4dDRBcFFuTWtWeDNPWFhGd1NheWVzaE9OVjN1SmpDdThOUEd1UEJrRDZKM2RaTldtNHBlaG1YTjgzV1dBcTdRbFYwUnNYdGZxcC95TnFDWVY3NUVNMER3WnJJY1hrb1dYcDZJL2tSaDR1dm1jbnY2K0hZdC93UzJFQ1RxcitqOGsrOW5tZVhxUTRyUWI2elNwOFl0dUkvVkZNN1h5amdqbmNnMjZkZDhPNnZNWTZTV0pkWmE0Zmljb3VBTUF3ZUtpL2lGc3JQOUpBYjBQUU9jalI3UmpIOGd0Vy9vck9ieXZMZ1hTR3hvcHQvT1JyckZDMzZwRXowWkRMaGZMRHdRMW5KUnM3L1g0VnZ0cVE0MDNYc1F6TFNETmt2NkJpeGFRWWVObDJvRVJOc1hYOGVBc0xvMlRtdGRNLzliS2Y3R3krSGFjTEJ2aGgyYXRTa2ZoVU1PZTZNbHF4OS9zOW56VzRURlZ5V2xYZ1UwR1JWS3p1ZUp0N2txcEtVNGh2WmQwTDRiaVhJcitPRVBEMEI5N1V3dHpRdkp5d290OHRsSGRwNWUvSXVFNXUyQ2oyZmw5eHZjQmEvNTd5VnFTM3dyak9qZW9BRzYwMjkwcFgvcHNFa1RicUxoWFhqd2dLbzNoRnJONjFMYUErZVlYZFltWW8iLCJtYWMiOiJjOTUzNTAyZDE2NDBhYzQ2NWNkOTZkMGY5YzdmN2RiNTFjNmIxZDU2NzlmZjhiZDUxZDRmZDk5OWNhMmQ2ODM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1565887716 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:39:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilg4ekIrSjhUbHVRN2Yrb1UyU2JzMlE9PSIsInZhbHVlIjoidkplRnFjd0laVGhYd1ZTSWxuKzhIOUdLMk5LYW1tOXRwa1NRUXhpRTducGM3OU96YmdENlcxNFVnSnhqUUgxbHVhcXFlR1BtdExNYTVndWFFWEkzL0FCby9PM0pSRlYzNDlLM29nZHlITU5RWDFOTmdiWWFqQW5GbTgrRktHblVUcEwrdnhBSCs3Mmd6VDl0SVl0bW81aDlZUjFxT3prK09LbE5Xd0dhUzkwdVRyYlk5VGtmclV3Vk1wZldhWFg5YWhvcDZmVGRJb1FUSjI2bEtLM1ZHenB2VmxGazF3UWtWSkNEdi9INENENVljeGJGdXVPUUtwaGRMblBBOGFjeGJXcEprUW9YK3BUckh5RVF4bWlGaUUrOHpXU01xdXhJYlJVVmg2MDdIYjV6Yjd4ZDZjR1hSZDhLekdlRk16cUt6Zy9DOWVTditlUFNNVE1qT0ZSaWludzB0Rk9ia1Ezakswc1pyTkhRMnpQMUVGV0tUNm42VEI0S2UrcGh0VU5HYm85dW0zNGVQYW1NU1hmNFEzMkpCc2hhMDVhK3I3SGY2RnVNbTV6L2l1VDVXOXFuS3dwN0JRb3hoYzRuOHNsT0tGaFdRdGpVeGo4djhySnU1K0NVdVI2UGlFNFNUU3lvYUdRSjI0RFhWUlh6YzRsU2Q5YTJMeStTVnEvSWFuSnMiLCJtYWMiOiJjMjZhMjUwYjIyOWRhYjkzYTVhYWM0ZTRlOTEyNTk1YWM0ZmNkZWRhMDBkOTdhYWM4MDU4M2JkMTcxYzdhODczIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:39:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IlBqbUFzQTl2VnRhUjgrN2dmeGYveHc9PSIsInZhbHVlIjoibkFYRHVTT3FiU2VRdmt4RkNldFFhblFTdTBHaHBiVkJpbkdVNlNPNFArUVRlcHo0bGhFaGRWWHpkdW96MzNpMHlKVDhyYTZWOGtmcEVGam5HWnZEQ09HTWhONlhzVG50RDRDZnlLejdFVG43bnhJaGF4RTdqUkZjVU01QmVzWEtxVlJrL21nVkhBN2VNMTZuTEtVMFNncVVOdWVENmQ2SEdYVDE0dE1xUVVUZXZOaVBLY2grcW5tTjRjak5pWXpyaGU2SDJTZmNjaFF6eStjY2hUVTlJaTNMS2FlOFlLUDM4dEp1a3JVYXhmQW9OQXlsZGlEUnN5c29vU1g3V1ZTZk9vN1p5U3lDdWVuUVptT1hsay9Sczg3NUd0OGd0akduWDNQMDlURUtKV0owci9KMEtLbGdtMHZHcHZMYko4U1AzL3I0RWxXN0JqNFpZeVpKcm1mNnpVVGExWDhLU3liMlhhVWt2Rm9ad0tVOVFqYlljUmJzRVpJay9jUW5VMloyM1ZObDU0ZG9xMHJBek5wcFEzc3gwN21LWVRIdG4xQTNkTmlMckZlYW1vT0tYM0d6a043VlA5OXJrb2pWTFkybldTWkhYT3pVNGhYUTNJR25FbXZhNHNHR0hlaHlYZUU0NVJNN0ppUDBoRjFPR0VnZVIydGJJeWh5eDAxbE5DclciLCJtYWMiOiI4Mzc3OTgyNDVmYTg0YzU0ZjZkM2NlN2RmZjM2ZTc2YzcyZmIxOTNiOTBlOGZiODdhYTkzMmVhZWU2YTE1OWY1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:39:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilg4ekIrSjhUbHVRN2Yrb1UyU2JzMlE9PSIsInZhbHVlIjoidkplRnFjd0laVGhYd1ZTSWxuKzhIOUdLMk5LYW1tOXRwa1NRUXhpRTducGM3OU96YmdENlcxNFVnSnhqUUgxbHVhcXFlR1BtdExNYTVndWFFWEkzL0FCby9PM0pSRlYzNDlLM29nZHlITU5RWDFOTmdiWWFqQW5GbTgrRktHblVUcEwrdnhBSCs3Mmd6VDl0SVl0bW81aDlZUjFxT3prK09LbE5Xd0dhUzkwdVRyYlk5VGtmclV3Vk1wZldhWFg5YWhvcDZmVGRJb1FUSjI2bEtLM1ZHenB2VmxGazF3UWtWSkNEdi9INENENVljeGJGdXVPUUtwaGRMblBBOGFjeGJXcEprUW9YK3BUckh5RVF4bWlGaUUrOHpXU01xdXhJYlJVVmg2MDdIYjV6Yjd4ZDZjR1hSZDhLekdlRk16cUt6Zy9DOWVTditlUFNNVE1qT0ZSaWludzB0Rk9ia1Ezakswc1pyTkhRMnpQMUVGV0tUNm42VEI0S2UrcGh0VU5HYm85dW0zNGVQYW1NU1hmNFEzMkpCc2hhMDVhK3I3SGY2RnVNbTV6L2l1VDVXOXFuS3dwN0JRb3hoYzRuOHNsT0tGaFdRdGpVeGo4djhySnU1K0NVdVI2UGlFNFNUU3lvYUdRSjI0RFhWUlh6YzRsU2Q5YTJMeStTVnEvSWFuSnMiLCJtYWMiOiJjMjZhMjUwYjIyOWRhYjkzYTVhYWM0ZTRlOTEyNTk1YWM0ZmNkZWRhMDBkOTdhYWM4MDU4M2JkMTcxYzdhODczIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:39:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IlBqbUFzQTl2VnRhUjgrN2dmeGYveHc9PSIsInZhbHVlIjoibkFYRHVTT3FiU2VRdmt4RkNldFFhblFTdTBHaHBiVkJpbkdVNlNPNFArUVRlcHo0bGhFaGRWWHpkdW96MzNpMHlKVDhyYTZWOGtmcEVGam5HWnZEQ09HTWhONlhzVG50RDRDZnlLejdFVG43bnhJaGF4RTdqUkZjVU01QmVzWEtxVlJrL21nVkhBN2VNMTZuTEtVMFNncVVOdWVENmQ2SEdYVDE0dE1xUVVUZXZOaVBLY2grcW5tTjRjak5pWXpyaGU2SDJTZmNjaFF6eStjY2hUVTlJaTNMS2FlOFlLUDM4dEp1a3JVYXhmQW9OQXlsZGlEUnN5c29vU1g3V1ZTZk9vN1p5U3lDdWVuUVptT1hsay9Sczg3NUd0OGd0akduWDNQMDlURUtKV0owci9KMEtLbGdtMHZHcHZMYko4U1AzL3I0RWxXN0JqNFpZeVpKcm1mNnpVVGExWDhLU3liMlhhVWt2Rm9ad0tVOVFqYlljUmJzRVpJay9jUW5VMloyM1ZObDU0ZG9xMHJBek5wcFEzc3gwN21LWVRIdG4xQTNkTmlMckZlYW1vT0tYM0d6a043VlA5OXJrb2pWTFkybldTWkhYT3pVNGhYUTNJR25FbXZhNHNHR0hlaHlYZUU0NVJNN0ppUDBoRjFPR0VnZVIydGJJeWh5eDAxbE5DclciLCJtYWMiOiI4Mzc3OTgyNDVmYTg0YzU0ZjZkM2NlN2RmZjM2ZTc2YzcyZmIxOTNiOTBlOGZiODdhYTkzMmVhZWU2YTE1OWY1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:39:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565887716\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-135254524 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135254524\", {\"maxDepth\":0})</script>\n"}}