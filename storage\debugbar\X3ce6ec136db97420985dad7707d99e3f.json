{"__meta": {"id": "X3ce6ec136db97420985dad7707d99e3f", "datetime": "2025-07-31 12:23:51", "utime": **********.786369, "method": "GET", "uri": "/storage/business_logos/1753948608_logo-dark.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964629.738806, "end": **********.786449, "duration": 2.047642946243286, "duration_str": "2.05s", "measures": [{"label": "Booting", "start": 1753964629.738806, "relative_start": 0, "end": **********.434174, "relative_end": **********.434174, "duration": 1.6953680515289307, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.434227, "relative_start": 1.****************, "end": **********.786454, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3060\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1892 to 1898\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1892\" onclick=\"\">routes/web.php:1892-1898</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00652, "accumulated_duration_str": "6.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.674193, "duration": 0.00652, "duration_str": "6.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/business_logos/1753948608_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/storage/business_logos/1753948608_logo-dark.png", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "image/png", "request_query": "<pre class=sf-dump id=sf-dump-2082287365 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2082287365\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-14699312 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-14699312\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-99659895 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9iMWhuUlp5eU0vb0F4WkdWNmhBTnc9PSIsInZhbHVlIjoiZGVXdkhBeGsrclpDSnAyUXozSXlFSmR3WndUcFBMaks3UkxvcnBucmgydnBuY0NuaUNPL0pkR2tDU0llYkdrbUlHQVM5OE9MT2VrMFdNaXZXc0hjMDM4NnJMbENBd1dEVHk5ZzZRcDJoWThOR0NJZ0RqTDAyQUNGT0pIUTJZSkRhRUNkOCtyWUhLTWhzSVRFei8xRHVWZU5OM3h4c2xreURVeC9wWVZoN2hYcWltREptTlg3NWVTR1FSQTRNeDEzMHEwVG9RUU9mZG4xSnV3bkVEcG1FUjM2dTQ1LzZ3cmxlT00vcXJUcjVUWmFKcTFPamg1ZE9oS1dnRmNCKzllck5weWtnQW5DaHZ5bmhqVlJRSXRsalJTY1pKZFpaNERVWGJJL0NtUlljV0hhRVJqRFFTSko2ZTZ5T3czZkZ5Mnd3cFQyTk9hVURIME1ZdWdZNTlMMUNNQjJhS3paU0Y2TFRreDEzVlE3M1haQjc0ZE1GbWk2d2M1dFl1N0NKV3Z2ajkyR0E4WkVVVFJBZlBKbE1rSzRUQ0JzWlMySDZZdGtyT3p5Q1V6VVNnVGphcThXZVFRQm95UHlQaU1US29BTE9GQnFJYnBrdDZXVWJLamFsYzRvMWp1bGpIK3dpSUhPVG9rUTRoRmhwMkVuNVVMSEl5Qk82UjlVZk1GZnNPdUoiLCJtYWMiOiI4NmVmYzMxNzc1NDk3ZmNjNWFjYjVmMzI5ZWQ1YzE4YTMyMjViN2Y3M2I1ZTY5OGE3NTYzNjdhNmQyYzgwYWYyIiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InE1c2g2U2VWWTgzL0wxUjQvQ3gxTnc9PSIsInZhbHVlIjoiQ2M3SVdUN1I5cTJiVjJCUUx4V1FuZWJlTEJZMG1TSGNHaFZFRzFTY3RKUlpZWEd5c2ZtL0NWZ2NsMDdNL2t5MmJTOEU2bW1ja25YUXJQWHVoTWxJVHhuQTdteC9GbFdWdXpMcHBodGxMV3FjUUxrTHdvR0JEZkxMUTU4SHpmbnBLNjk5d1Z6emhjOS9ha2tEM2JhUzRQRjBzZVdsOWtXRzJTUWpoVy9OSGl2UnlvcTFFMEE0RHhzbnZVWkt6eGxMdlFGMjcxcFIxSWhrTkREQ011YnJOUFQvSCtaRC9xcFE4cjdiZDRkVUZYNGtOVWJDbFJvaEhzWHdQS2RmTE5xdWhrVmc2aXErMlB0WU9CMTllTDBvWnRsQTdXUmZSOTZzTjhMZnFXWGh5TlZSVWQyNFRGcWVPdy9lcHB2c056cEVMVmpaK0RnbktqR3pQNWdNaFlpRHlJaVRTbThoSVI1bkpPcGJ3NHdCY2wzK1VCaXd6bmVmbnRPMWw3VjZyc0VnT3QwZnJza2hQd200ZlZ0YVVFMXRHUTYrb2Z0WXUxbDdiemRzODVSMlQ3a1h6cTVJRGtETmJRUkg0cVBldzl3SU5rNXBEU21iSFl2azRDd0RwcjNlYVBoK2lLMzVWNE94TGZWMEtsK3V4MjdkM1dNc0x4MzNiSEpUZmRLL043NEkiLCJtYWMiOiIxMDBjNmMwMmMxZTAxODhiMzljYjkwYTM0ZDdhY2YzYjAwMjdjODFkODhhZDA4OGEwMjc0MDZlZDcxY2YzMmQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>if-modified-since</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:56:48 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99659895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1651244743 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651244743\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1950342337 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:23:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 07:56:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">25784</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlZK255Z3p6YlVOQTRTZytNbTRKekE9PSIsInZhbHVlIjoiZVNVRGczSjFiVGZRZ2ZpUFBmUUl5S01oaWdPRmFmdERFRmFvU1p5QlpiQThiRUYwOG1KUGFiMTZaYjVxWmJtdGtlNElBOUNuS1N2U3o3N0FTZmNESzZtNXQvdktLRkdLT1NNNDltMHo1KzViV1BaQXBaOEhBQy8yeGwwc3NCNUlpeUVMRWxJNUVmTzRhdFNyckh1cHVSTWVPTEwwVU5YUFJtRDkwSE95b2UvcGN1dHcxUEo2d1dFUlY2N09yNVdoRXZIZXNaSTlTQkVZYU5CWWc2aDFaT1V0TStCR2hhMElRTDg3V1NBaWdxZGhpMDRvWTZHZ3RXalhNUEZEY0dnNEpORUJwNzhNeWtwckNhSXF0OFdoaEhIcUZkcXo3OEFNZTFvWUpOYWtkODFIckc0MlFaVUMvSllyZ3dnNzBKYmY1b0lzb0VSeEVibmVycFZ6R0J0S1g4Vk1kZXZYV29IYVZRRndKQnFwZ21iS0lrdkIrU1hZTFRYc1J4UXduNEx5MG9ybjMrVnBXR2E5dFNsMVBoc2tYUGhsRktyMWVNN050RW5aS2kzdHVSaWtNM051dFdHUTU5VDhNb3ZmSWtDb0tqUlNjV0Z6M3pqWmVJZEppTXFiczZXVmQxNzhFZ25VZGFBUmxrZC9zRlFybjRjTjFHV2J0aDZiaUNXZWdieVkiLCJtYWMiOiJiNWUxYTM0OTVhMWU1NGY2NGYxNGUwODcwMDkxZjQxNjFjYTA3MTFhMDAzZjhiNDFlOGVhYmRjNzUxMmVkMjAxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:23:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IktKT1FKVG16bEVIbDBtOXhuUTBwblE9PSIsInZhbHVlIjoiL013bFlqdjN2YXZBSEVQN3FTeVl3TFZQbGhodVlLRlV3T1NBdjZjcWtCc09vWUxmMXRhVHp5MElmaGlOQ2JsckI2MHNwNDNvNGhsR0xMMFJSVlF0M3VsOFVhY1ZoZU5yY0RZaHNwMHhyUGhrcjhvckJGdWxKc1oyYTcwRmpOM0R1WHVydm9MbGlyNGYwU3Nvb0VCR3ZMVUF3Z0FSRXZGTFVKRW5iY3EyNEN1N0pSWmlRdUVWbk9HSXllMEZzR2Vpc3pqek5tMEt0Wi9sdXc3RFRSK1llbzhVSUhILzk1bVpPQmlhb3NBRWUwVXBUQlh4bENPZHp0alZVeC9uRzU5dVVUalBEbTRKSUpqNmdPMnhDaHRyekNtY2RoOG9MMFNocXhWL3k2eEpBOExKQWFIZjFMUmJReWtSM3lFOHdwM29TZW8yek5LQkZQcnM5akdkN3V0UFUxanA2VnhnVTVjWFQ4dVNCUDZxMHJiUTIrMmoxcHUrbGhkL2IvdG5EWmtrMkRyVFBJbUdtYzZCV1ZsZFh4bjlOY2Z0QXNRaVZlRVd5UlRjK09Da3lMZXo3Ynp6T0h2QlR0RGpJN0tBWWdXYWNHcDZ2emdqaXpxOFJPY0NDNGx3b1RuSDNVVWwzQTlCamkxajRIZFNwbnFjWnB3V3krTndpSUxNSmJMcmJZZWIiLCJtYWMiOiI0NWY3NzQ5Mjc0OGRhYzVjYmZmOGNiZjE2NGZiMTNjOWY2ZTBjNDIwMjkyMmJmYTA2NWZlYTVjY2EzNTQ4YTkxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:23:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlZK255Z3p6YlVOQTRTZytNbTRKekE9PSIsInZhbHVlIjoiZVNVRGczSjFiVGZRZ2ZpUFBmUUl5S01oaWdPRmFmdERFRmFvU1p5QlpiQThiRUYwOG1KUGFiMTZaYjVxWmJtdGtlNElBOUNuS1N2U3o3N0FTZmNESzZtNXQvdktLRkdLT1NNNDltMHo1KzViV1BaQXBaOEhBQy8yeGwwc3NCNUlpeUVMRWxJNUVmTzRhdFNyckh1cHVSTWVPTEwwVU5YUFJtRDkwSE95b2UvcGN1dHcxUEo2d1dFUlY2N09yNVdoRXZIZXNaSTlTQkVZYU5CWWc2aDFaT1V0TStCR2hhMElRTDg3V1NBaWdxZGhpMDRvWTZHZ3RXalhNUEZEY0dnNEpORUJwNzhNeWtwckNhSXF0OFdoaEhIcUZkcXo3OEFNZTFvWUpOYWtkODFIckc0MlFaVUMvSllyZ3dnNzBKYmY1b0lzb0VSeEVibmVycFZ6R0J0S1g4Vk1kZXZYV29IYVZRRndKQnFwZ21iS0lrdkIrU1hZTFRYc1J4UXduNEx5MG9ybjMrVnBXR2E5dFNsMVBoc2tYUGhsRktyMWVNN050RW5aS2kzdHVSaWtNM051dFdHUTU5VDhNb3ZmSWtDb0tqUlNjV0Z6M3pqWmVJZEppTXFiczZXVmQxNzhFZ25VZGFBUmxrZC9zRlFybjRjTjFHV2J0aDZiaUNXZWdieVkiLCJtYWMiOiJiNWUxYTM0OTVhMWU1NGY2NGYxNGUwODcwMDkxZjQxNjFjYTA3MTFhMDAzZjhiNDFlOGVhYmRjNzUxMmVkMjAxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:23:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IktKT1FKVG16bEVIbDBtOXhuUTBwblE9PSIsInZhbHVlIjoiL013bFlqdjN2YXZBSEVQN3FTeVl3TFZQbGhodVlLRlV3T1NBdjZjcWtCc09vWUxmMXRhVHp5MElmaGlOQ2JsckI2MHNwNDNvNGhsR0xMMFJSVlF0M3VsOFVhY1ZoZU5yY0RZaHNwMHhyUGhrcjhvckJGdWxKc1oyYTcwRmpOM0R1WHVydm9MbGlyNGYwU3Nvb0VCR3ZMVUF3Z0FSRXZGTFVKRW5iY3EyNEN1N0pSWmlRdUVWbk9HSXllMEZzR2Vpc3pqek5tMEt0Wi9sdXc3RFRSK1llbzhVSUhILzk1bVpPQmlhb3NBRWUwVXBUQlh4bENPZHp0alZVeC9uRzU5dVVUalBEbTRKSUpqNmdPMnhDaHRyekNtY2RoOG9MMFNocXhWL3k2eEpBOExKQWFIZjFMUmJReWtSM3lFOHdwM29TZW8yek5LQkZQcnM5akdkN3V0UFUxanA2VnhnVTVjWFQ4dVNCUDZxMHJiUTIrMmoxcHUrbGhkL2IvdG5EWmtrMkRyVFBJbUdtYzZCV1ZsZFh4bjlOY2Z0QXNRaVZlRVd5UlRjK09Da3lMZXo3Ynp6T0h2QlR0RGpJN0tBWWdXYWNHcDZ2emdqaXpxOFJPY0NDNGx3b1RuSDNVVWwzQTlCamkxajRIZFNwbnFjWnB3V3krTndpSUxNSmJMcmJZZWIiLCJtYWMiOiI0NWY3NzQ5Mjc0OGRhYzVjYmZmOGNiZjE2NGZiMTNjOWY2ZTBjNDIwMjkyMmJmYTA2NWZlYTVjY2EzNTQ4YTkxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:23:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950342337\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1003668220 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"69 characters\">http://127.0.0.1:8000/storage/business_logos/1753948608_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003668220\", {\"maxDepth\":0})</script>\n"}}