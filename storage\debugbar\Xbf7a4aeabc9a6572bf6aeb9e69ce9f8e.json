{"__meta": {"id": "Xbf7a4aeabc9a6572bf6aeb9e69ce9f8e", "datetime": "2025-07-31 12:16:20", "utime": **********.116412, "method": "GET", "uri": "/finance/business-info/api", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753964178.691349, "end": **********.116506, "duration": 1.425157070159912, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": 1753964178.691349, "relative_start": 0, "end": 1753964179.944027, "relative_end": 1753964179.944027, "duration": 1.2526779174804688, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753964179.944057, "relative_start": 1.2527079582214355, "end": **********.116514, "relative_end": 7.867813110351562e-06, "duration": 0.17245697975158691, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47344912, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/business-info/api", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getBusinessInfoApi", "namespace": null, "prefix": "/finance", "where": [], "as": "business.info.api", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=447\" onclick=\"\">app/Http/Controllers/FinanceController.php:447-521</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02213, "accumulated_duration_str": "22.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.017842, "duration": 0.02056, "duration_str": "20.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 92.906}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.064499, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 92.906, "width_percent": 3.389}, {"sql": "select * from `business_infos` where `created_by` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/BusinessInfo.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\BusinessInfo.php", "line": 60}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 450}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0732481, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BusinessInfo.php:60", "source": "app/Models/BusinessInfo.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=60", "ajax": false, "filename": "BusinessInfo.php", "line": "60"}, "connection": "radhe_same", "start_percent": 96.295, "width_percent": 3.705}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\BusinessInfo": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FBusinessInfo.php&line=1", "ajax": false, "filename": "BusinessInfo.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/business-info/api", "status_code": "<pre class=sf-dump id=sf-dump-88157049 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-88157049\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-12430021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-12430021\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-497834984 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-497834984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1214346787 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2388 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imw0V0lXWmRKQU9iMjBrRVFHcXdjQnc9PSIsInZhbHVlIjoiZFVDV3o2WVlqWXQwblNTYnFvaFdUWVpaV3pzbkJ2Z0NnWUdsOVFUdndLMGZ1WjJuUzd3c3Y0K1B6U2UzanRBdkdxQW4wcmEwZVU5TFFJZjdvTkpNSFJpUU1CVEUxMFg0bnkzSDlKV2JSVSs2cENrekNLbGtQdWJMU3FlT2tXZzBCV29wWCsxdnFEUENpM3dhb2ZXYkRKQ3hwV1FINHhzcktOWDUwdzdaRDY3TWZJWG81RWxJYXdYdXl6NkNJT2lwNVFKelgrYm5RVU1BYkR4T3pZeDROeWlyWlg0NlhUbWtGTWduRC9XTHMwc1JZclBpbDliTTZkbUF0MU12V1IxUXpTNjNhUXI2bk5HTHZ0REFWRXBBZ2hHS1p5cUp3SkV5UkxRZkpvRE9PT1N3VVpUZUYxMmpPMjB2enMwMGJtTDJlSkRCMFoxYWNNWDY4RHFxODVqT0t2cndLSk1mWkRpRVcvSWVkU0xHYmhNcnhoRnY0TWo4QUNkd0xScjFUbmE5RkE1MDJzTWlXbDRXeXNvU1A0dEZ6Ui8yeXg0cHgyN3c3UnhqeFU0YUQwYVI5dkRSNmJ6MlYxVWF3UFJRMHB4SDFYS2hGaWdmdmxuc3B3d0ZnSlZzRUNKYWlXMm93NXJUdHczOEs5WGc1VmpFREdyc3V6aTN0YTZzbncxUHgxb3ciLCJtYWMiOiJiYmRlNzA2YjI1Nzc3MTllZmE3ODM5MWNlZmU2ZjQwNjk4ZTA0MjUxMTk1MjJjYTcyNjVkOGVkY2U2M2FjYjc0IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlRYMU9IOGpURi9xR2w0SWNGdTk2QlE9PSIsInZhbHVlIjoiRDJwQ2x3QUwwbFNaSzFqYkYxWSs5Y0YwREh4OHNwSDlXQWNYTmVKK29MSVlqN0lEcHh6S05weXkwenRNNmZYeEprK0VTMHpFMlFWNDhOeG5VZm9uNlRHZW5mR3lxNTlsK1NZTDhBRXduS0llK0RVZUFFWnhhaVdWTWV5L1NQdzZ5YUFaS1pCVGRQSUxHMHBlSVc3TWpwOWZuanBFdHFsVEpCWGNzQmc3QlEyRVJneENYM0pONUMzS0svUmM4MjFCN0RURENvVDB0UlFkL2VCMnhmL3RiU1NpMEtZMnowZmZGbCtDZ0FCdE1adE5UNXhrNzBMVHFUdFB4STg5NTN6NmdaN0VMUFNkODRabm5zRmM4aVFXUk92MDFsY2IweWxmRExTL1ZpN1lvNm1RZFJpOXplYld3NlNBZFgrU0ZFcHhoVGVEVFE5OVFUZVFOVVNuUmoxM280T3o0YXQ0SUNoNFZqTDNPMUxJcGErUjhCdHI3d0t5Y0tPamZXdGVISFZvaE4wcWRscUdWN0EyOXZFdDUzSG96eXdRUnZLUU9tcUV3MDh6VFVLc2dqTWZsdUVDdk9QMVZ5ODFQRXQxMHQ5TFRtUFVZbXdiWHdxQnhlK2FBRS9FMG1qYVBxbm1PVkE0eXZnM1VRSThBN3BpRmRHZlEzNjZUc1RKeVFzdnhMcE8iLCJtYWMiOiIwODcxY2RlZmYyMjc5ZTVhOTliMDhlYWNkM2NjYmEyMmM2MzMxODQ0ZWViMzdlMTA5ZTQzOWYwMjM2M2M4NjNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1214346787\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2141463492 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QDfIWwXV8Jq5ffn70AtPB78uqfUSgVl03qiMSbZJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141463492\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-151511319 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:16:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjV4M2hWUHJ3UmhCeUhoWko5VHJhYnc9PSIsInZhbHVlIjoiaUVkWjd2SFFiODhKY3VZYWVHY3R3bEpCM0gvaVAwYkRSNTdXZ1NLL3k1UmRiWXhBay9BMXVIMHhhdVNHWWFib0RzV1YxVFBTWTA1TS9ZZUFsdUhKcnc2TnVxMDJFTEIrSUVUbThkQXRsWXU2S01Tb3hObVZ1bHNDeG5velFoK0NYTkJWeUJuNXVPdTY3anA4Wk5haTVPWG93M09nOGhyb2owMEwzT0pDaldOdFhObEs2Q21Zc2F2WmZGUCtNNllDMXFnM1lqUkt0dVdoRlV2Rm5VcGUrNVZkNGlXUmNmWGVMck9JbjM1UHlPSjFOVHljNmF0VjQrU0loZUxuTWZScDUwMDZhZFBjQWpPWUJVY0MvRzg1RmlWMkJDYnd2Z3kxYVQ4ZURvYnBRWjFHTitaK3hmbG1pVEtWZzV6YnNnYVo0T3NOTzg1K3ZiYnh6QlljQWw2d0Vremw0cUgwWEVqUnBDZkN5ZEhYRXYyZWp3Qkp6NGV4TDdHd1hjSjVSSWh1SWhBaGtyRXR6QXg1WVFBUWxLTWpGb0VtTWF2MHVCWC9PY3hkNVpsSXJqZEIxSngxS2tSdSsxdVZYTGMxZXVwQTVON3BESk00N2gxSVF0OHJqOGZPUGhrMDR3Sk9MaWxqcmlzSG9xelcrTUJraFIvRUk4cnJtazR2MG5FSUVBazciLCJtYWMiOiI1NGFjYzQ4ZWQ4ZDQ5ZjRlMWJmNjgwMGVhYTJlZDhiYTFiMmNhODAzZDY3ZmYxY2JmMjdjNzUyNzAzNDZmMDk2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">omx_ai_suite_session=eyJpdiI6IkI0ekZ3c25LbnFaejhqUmd3YThvTkE9PSIsInZhbHVlIjoiUXdocWlVamt3Y3hLa3drZnArVkljUjRCREtMRXQ5akNrdEYweWo3L21YZ1FrQ3BPV2RTOEpUS0ZmM3dwVk92cTdlYUI2SkQ1VkxqU3VlcXo1N1phbmFlOVoweUFqZUlHbHlyVy84eVFTUEJRZUsyM2RtZGM0RmsvTXdBUW00L0crWWJEZTlOZXF4emIzNTM5YUJvZWxqNmRRWFRIS3JZSE4xZEdMaG5VajZKVGExOUdkS1BGalZmZW93bFJYWHlJWHZKbnBJU3JIUkk3cWtpbElLQnJnSUdrTGxuSzZDV1VoUWpwZjRBa0xUM2RPKzFGUTh2VnhaMnd1MTFwQjUzRWRUZysxMXdxem1CYWhONEkzd3IyRVFIajZCbm5nYWZsRzVtQndHbkF3ZDMxQTJ0bFRpWFcydzIzUURBQUt3bjdRYlU4SlJsU29SaFZUc0FCZTdqMkx3YzVqMHVOTTFZUTZ5K0kwdGY2TzRTZk9kaDhLM3d0MVc1V3pieVFudkFoTDd2dlhoQjNOZzV3L1NFZ0VEUFNhRGYvVzkwb2VwTllZVHpJY01TS0J6OXh1Z0NrVXVRVHFwTWtjVmh1Y2lyYXdENHBBY2FLbXVTa2hQOHRCMjY0WitPVHZDdi9ST2JFaE0wOTVTVldzcjVWTGpuUmRRVDNiL0wxRStxY1NjaE0iLCJtYWMiOiJlODBlZjllOGZkYmNmZjk0MmMwZGE0MTc2MDkzMzdhYjIxNTIyMDVhMDNlNWVjMDYwMDk1NzI1MTUyZDdjNmRmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:16:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjV4M2hWUHJ3UmhCeUhoWko5VHJhYnc9PSIsInZhbHVlIjoiaUVkWjd2SFFiODhKY3VZYWVHY3R3bEpCM0gvaVAwYkRSNTdXZ1NLL3k1UmRiWXhBay9BMXVIMHhhdVNHWWFib0RzV1YxVFBTWTA1TS9ZZUFsdUhKcnc2TnVxMDJFTEIrSUVUbThkQXRsWXU2S01Tb3hObVZ1bHNDeG5velFoK0NYTkJWeUJuNXVPdTY3anA4Wk5haTVPWG93M09nOGhyb2owMEwzT0pDaldOdFhObEs2Q21Zc2F2WmZGUCtNNllDMXFnM1lqUkt0dVdoRlV2Rm5VcGUrNVZkNGlXUmNmWGVMck9JbjM1UHlPSjFOVHljNmF0VjQrU0loZUxuTWZScDUwMDZhZFBjQWpPWUJVY0MvRzg1RmlWMkJDYnd2Z3kxYVQ4ZURvYnBRWjFHTitaK3hmbG1pVEtWZzV6YnNnYVo0T3NOTzg1K3ZiYnh6QlljQWw2d0Vremw0cUgwWEVqUnBDZkN5ZEhYRXYyZWp3Qkp6NGV4TDdHd1hjSjVSSWh1SWhBaGtyRXR6QXg1WVFBUWxLTWpGb0VtTWF2MHVCWC9PY3hkNVpsSXJqZEIxSngxS2tSdSsxdVZYTGMxZXVwQTVON3BESk00N2gxSVF0OHJqOGZPUGhrMDR3Sk9MaWxqcmlzSG9xelcrTUJraFIvRUk4cnJtazR2MG5FSUVBazciLCJtYWMiOiI1NGFjYzQ4ZWQ4ZDQ5ZjRlMWJmNjgwMGVhYTJlZDhiYTFiMmNhODAzZDY3ZmYxY2JmMjdjNzUyNzAzNDZmMDk2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">omx_ai_suite_session=eyJpdiI6IkI0ekZ3c25LbnFaejhqUmd3YThvTkE9PSIsInZhbHVlIjoiUXdocWlVamt3Y3hLa3drZnArVkljUjRCREtMRXQ5akNrdEYweWo3L21YZ1FrQ3BPV2RTOEpUS0ZmM3dwVk92cTdlYUI2SkQ1VkxqU3VlcXo1N1phbmFlOVoweUFqZUlHbHlyVy84eVFTUEJRZUsyM2RtZGM0RmsvTXdBUW00L0crWWJEZTlOZXF4emIzNTM5YUJvZWxqNmRRWFRIS3JZSE4xZEdMaG5VajZKVGExOUdkS1BGalZmZW93bFJYWHlJWHZKbnBJU3JIUkk3cWtpbElLQnJnSUdrTGxuSzZDV1VoUWpwZjRBa0xUM2RPKzFGUTh2VnhaMnd1MTFwQjUzRWRUZysxMXdxem1CYWhONEkzd3IyRVFIajZCbm5nYWZsRzVtQndHbkF3ZDMxQTJ0bFRpWFcydzIzUURBQUt3bjdRYlU4SlJsU29SaFZUc0FCZTdqMkx3YzVqMHVOTTFZUTZ5K0kwdGY2TzRTZk9kaDhLM3d0MVc1V3pieVFudkFoTDd2dlhoQjNOZzV3L1NFZ0VEUFNhRGYvVzkwb2VwTllZVHpJY01TS0J6OXh1Z0NrVXVRVHFwTWtjVmh1Y2lyYXdENHBBY2FLbXVTa2hQOHRCMjY0WitPVHZDdi9ST2JFaE0wOTVTVldzcjVWTGpuUmRRVDNiL0wxRStxY1NjaE0iLCJtYWMiOiJlODBlZjllOGZkYmNmZjk0MmMwZGE0MTc2MDkzMzdhYjIxNTIyMDVhMDNlNWVjMDYwMDk1NzI1MTUyZDdjNmRmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:16:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151511319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-916075062 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8OPjc9tsFWHRMdsXUWOwlezh536fmyDLFhI3xU7w</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/storage/products/1753963399_logo-dark.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916075062\", {\"maxDepth\":0})</script>\n"}}